/**
 * @type import('@mfe/cc-api-caller/bin/yapi/types').Config
 */
module.exports = {
    path: './apiSpec',
    errorCode: {
        FAILED: 1,
        // 参数错误
        ILLEGAL_ARG: 700001,
        // 数据错误
        DATA_ERROR: 800001,
        // 系统错误
        SYSTEM_ERROR: 900001,
    },
    yapi: {
        blacklist: [/^\/crm/, /^\/visit/],
        projects: [
            {
                token: 'e0eefe96064939f5cb03f30c16c6e669ef5b0d2b9418ed7133fd85e6650f579a',
                categoryList: [
                    {
                        id: 232782, // yapi链接：/interface/api/cat_400480， 取cat_后面的数字
                        name: '蜜蜂交互', // yapi的分类的名字
                        alias: 'apis',
                    },
                ],
            },
        ],
    },
};
