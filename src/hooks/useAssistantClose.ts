import { Source, useOpenFeedbackModal } from '@mfe/bee-foundation-moses';
import KNB from '@mrn/mrn-knb';
import { useDebounceFn } from 'ahooks';
import { useEffect } from 'react';

import { SOURCE } from '../types';

const useAssistantClose = (
    source = SOURCE.home,
    complainSource = Source.PROFILE,
) => {
    const openFeedbackModal = useOpenFeedbackModal({ source: complainSource });
    const { run: debounceOpenFeedbackModal } = useDebounceFn(
        openFeedbackModal,
        { wait: 200 },
    );
    useEffect(() => {
        KNB?.subscribe({
            action: 'ASSISTANT_CLOSE',
            handle: ({ data }) => {
                if (data?.to === source) {
                    debounceOpenFeedbackModal();
                }
            },
        });
        return () => {
            try {
                KNB?.unsubscribe({
                    action: 'ASSISTANT_CLOSE',
                });
            } catch (e) {
                console.log(e);
            }
        };
    }, []);
};
export default useAssistantClose;
