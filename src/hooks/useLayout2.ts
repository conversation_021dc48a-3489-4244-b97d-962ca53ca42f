import { View } from '@mrn/react-native';
import { useCallback, useRef } from 'react';

const useLayout2 = () => {
    const viewRef = useRef<View>(null);
    const layoutRef = useRef<
        Partial<{
            x: number;
            y: number;
            width: number;
            height: number;
            pageX: number;
            pageY: number;
        }>
    >({});
    const onLayout = useCallback(() => {
        viewRef.current?.measure((x, y, width, height, pageX, pageY) => {
            layoutRef.current = { x, y, width, height, pageX, pageY };
        });
    }, []);
    return { viewRef, onLayout, layoutRef };
};
export default useLayout2;
