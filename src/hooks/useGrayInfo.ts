import { apiCaller } from '@mfe/cc-api-caller-bee';
import { useAsyncEffect } from 'ahooks';
import { useState } from 'react';

import { useBizInfo } from './useBizInfo';

const useGrayInfo = () => {
    const { bizId } = useBizInfo();
    const [grayInfo, setGrayInfo] = useState();

    useAsyncEffect(async () => {
        if (!bizId) {
            return;
        }
        const res = await apiCaller.get('/bee/v1/bdaiassistant/getGraySwitch', {
            bizId,
        });
        if (res.code !== 0) {
            return;
        }
        setGrayInfo(res.data);
    }, [bizId]);
    return grayInfo as { gray: boolean } | null;
};
export default useGrayInfo;
