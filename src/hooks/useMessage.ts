import { useContext } from 'react';

import RootTagContext from './rootTagContext';
import { createMessage } from '../store/message';
import { UiStateAndActions } from '../store/uiState';

const store = {};
export let id = 0;
export const getUseMessage = (rootTag: number, uiState: UiStateAndActions) => {
    const key = String(rootTag || id++);
    if (!store[key]) {
        store[key] = createMessage(uiState);
    }
    return store[key];
};

const useMessage = ((...args) => {
    const root = useContext(RootTagContext);
    return root.useMessage?.(...args) ?? (() => {});
}) as ReturnType<typeof createMessage>;
export default useMessage;
