import { apiCaller } from '@mfe/cc-api-caller-bee';
import { useRequest } from 'ahooks';
import { useMemo } from 'react';

import { AutoGeneratedApisTypes } from '../../apiSpec/autoGeneratedApis';

import { getMSILocation } from '@/utils/getLocation';

type WeatherResponse =
    AutoGeneratedApisTypes['/bee/v1/bdaiassistant/common/weather']['response'];

/**
 * 获取天气信息
 * @param lat 纬度
 * @param lng 经度
 * @returns 天气信息及加载状态
 */
const useWeather = () => {
    const { data, loading, error, run } = useRequest(
        async () => {
            const location = await getMSILocation('bee_assistant');
            const res = await apiCaller.get(
                '/bee/v2/bdaiassistant/common/weather',
                {
                    lat: location?.latitude,
                    lng: location?.longitude,
                },
            );
            if (res.code !== 0) {
                return;
            }
            return res.data as WeatherResponse;
        },
        {
            cacheKey: 'weather',
        },
    );

    const weatherInfo = useMemo(() => {
        return {
            greeting: data?.greeting || '',
            weatherTips: data?.weatherTips || '',
            weatherType: data?.weatherType || 'default',
        };
    }, [data]);

    return {
        weatherInfo,
        loading,
        error,
        refresh: run,
    };
};

export default useWeather;
