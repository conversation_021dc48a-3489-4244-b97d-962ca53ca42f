import { LogUtils } from '@mfe/waimai-mfe-bee-common';
import { AsyncStorage } from '@mrn/react-native';
import { useState, useEffect, DependencyList } from 'react';

const PREFIX = 'BEE_INTELLIGENT_ASSISTANT';

export const useAsyncStorage = <T>(key: string, deps: DependencyList = []) => {
    const uniqKey = `${PREFIX}-${key}`;

    const [data, setData] = useState<T>();
    const [inited, setInited] = useState(false);

    const getAsyncData = () =>
        AsyncStorage.getItem(uniqKey).then((res) => {
            res = res || undefined;
            let paresd;
            try {
                paresd = JSON.parse(res);
            } catch (e) {
                paresd = { data: res };
            }

            // 兼容历史
            if (paresd && !('data' in paresd)) {
                paresd = { data: paresd };
            }

            // 如果过期了的话，则返回空，并清除这个缓存
            if (paresd.expire && paresd.expire <= +new Date()) {
                setData(null);
                clear();
                return;
            }
            LogUtils.log(
                `拜访本地数据获取: key:${uniqKey}, value: ${JSON.stringify(
                    paresd,
                )}`,
            );
            setData(paresd.data);
            setInited(true);
            return paresd.data;
        });

    const setAsyncData = (data: T = null, expire?: number) => {
        setData(data);
        const toSave = JSON.stringify({ data, expire });

        AsyncStorage.setItem(uniqKey, toSave);
    };

    const clear = () => {
        setData(undefined);
        AsyncStorage.removeItem(uniqKey);
    };

    useEffect(() => {
        getAsyncData();
    }, [key, ...deps]);

    return {
        data,
        inited,
        getAsyncData,
        refresh: getAsyncData,
        setAsyncData,
        clear,
    };
};
