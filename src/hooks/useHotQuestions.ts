import { useRequest } from 'ahooks';
import { useMemo } from 'react';

import useCallerRequest from './useCallerRequest';
import { AutoGeneratedApisTypes } from '../../apiSpec/autoGeneratedApis';

type HotQuestionResponse =
    AutoGeneratedApisTypes['/bee/v1/bdaiassistant/question/hot']['response'];

/**
 * 获取热门问题
 * @returns 热门问题列表及加载状态
 */
const useHotQuestions = () => {
    const callerRequest = useCallerRequest();
    const { data, loading, error, run } = useRequest(
        async () => {
            const res = await callerRequest.get(
                '/bee/v2/bdaiassistant/question/hot',
                {},
            );
            if (res.code !== 0) {
                return;
            }
            return res.data as HotQuestionResponse;
        },
        {
            cacheKey: 'hotQuestions',
        },
    );

    const hotQuestions = useMemo(
        () => (data as any)?.hotQuestions.slice(0, 8) || [],
        [data],
    );

    return {
        hotQuestions,
        loading,
        error,
        refresh: run,
    };
};

export default useHotQuestions;
