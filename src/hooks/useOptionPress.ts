import { useDebounceFn } from 'ahooks';

import useMessage from './useMessage';
import { useSendMessage } from './useSendMessage';
import {
    EntryPointType,
    Message,
    MessageContentType,
    SelectionOptionType,
} from '../types';
import useOpenLink from '../utils/openLink';
import { trackEvent } from '../utils/track';

const useOptionPress = ({
    msgId,
    history,
    msgType,
}: Pick<Message, 'msgType' | 'msgId' | 'history'>) => {
    const { send } = useSendMessage();
    const sessionId = useMessage((state) => state.sessionId);
    const openLink = useOpenLink();

    const handleOptionsPress = (
        option: Message['selectionItems'][number],
        entryPoint?: string,
    ) => {
        if (history) {
            return;
        }
        trackEvent('chat_option');
        switch (Number(option.operationType)) {
            case SelectionOptionType.LINK: {
                return openLink(option.url, { msgId, history }, sessionId);
            }
            case SelectionOptionType.MESSAGE:
                return send(
                    {
                        content: option.content,
                        abilityType: option.abilityType,
                        subAbilityType: option.subAbilityType,
                    },
                    msgType === MessageContentType.WELCOME
                        ? EntryPointType.WELCOME
                        : EntryPointType.SECTION,
                    entryPoint,
                );
        }
    };

    const { run } = useDebounceFn(handleOptionsPress, { wait: 500 });
    return { onOptionPress: run };
};
export default useOptionPress;
