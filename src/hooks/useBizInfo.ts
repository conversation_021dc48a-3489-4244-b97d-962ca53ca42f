import { GlobalStorage } from '@mfe/waimai-mfe-bee-common';
import { useInterval } from 'ahooks';
import { useEffect, useState } from 'react';

export interface BizInfo {
    bizId: string;
    tenantId: number;
    bizName: string;
    tenantName: string;
}

const createDefaultBizInfo: () => BizInfo = () => ({
    bizId: undefined,
    bizName: undefined,
    tenantId: undefined,
    tenantName: undefined,
});

export const getCurrentBiz: () => Promise<BizInfo> = async () => {
    const storage: any = await GlobalStorage.getStorage('bee_business_storage');
    try {
        return JSON.parse(storage.value) || createDefaultBizInfo();
    } catch (e) {
        return createDefaultBizInfo();
    }
};

export const useBizInfo = () => {
    const [bizInfo, setBizInfo] = useState<BizInfo>(createDefaultBizInfo());
    const [loading, setLoading] = useState(true);

    const clear = useInterval(() => {
        if (bizInfo.bizId) {
            clear();
        }
        getCurrentBiz().then((res) => {
            setBizInfo(res);
            setLoading(false);
        });
    }, 1000);

    useEffect(() => {
        getCurrentBiz().then((res) => {
            setBizInfo(res);
            setLoading(false);
        });
    }, []);

    return { bizId: bizInfo?.bizId, bizInfo, loading };
};
