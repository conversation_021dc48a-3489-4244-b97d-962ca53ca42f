import { LayoutAnimation, PanResponder } from '@mrn/react-native';
import { Toast } from '@roo/roo-rn';
import { useDebounceFn, useGetState, useThrottleFn } from 'ahooks';
import { useEffect, useRef, useState } from 'react';

import useMessage from './useMessage';
import { useSendMessage } from './useSendMessage';
import { EntryPointType } from '../types';
import { startAnimation } from '../utils/animation';
import { initAndStartRecognize, stopRecognize } from '../utils/speechAsr';

import { getAdditionMessage } from '@/utils/message/getAdditionMessage';

const useVoiceInput = () => {
    const [voiceInputOpen, setVoiceInputOpen, getVoiceInputOpen] =
        useGetState<boolean>(); // 控制语音输入页面是否展示的状态
    const [, setMoved, getMoved] = useGetState<boolean>(); // 移动过的标志，移动过则不再响应滑动事件
    const [_, setCanMoveCancel] = useGetState<boolean>(true);
    const [volumes, setVolumes, getVolumes] = useGetState([]); // 语音音量
    const [text, setText] = useState(''); // 识别出的文本
    const [isEnd, setIsEnd] = useGetState<boolean>(); // 是否完成识别
    const [valid, setValid] = useState<boolean>(); // 最终是否识别出结果
    const [leaved, setLeaved, getLeaved] = useGetState(false); // 手指是否离开屏幕
    const [viewKey, setViewKey] = useState(0); // key，每次打开语音输入页面该值自增1
    const [, setStopDefault, getStopDefault] = useGetState<boolean>(false);
    // 手指移动时记录移动位置，用以下级组件判断是否被触摸
    const [position, setPosition] = useState<{ moveX: number; moveY: number }>(
        null,
    );
    const { run: setPositionThrottle } = useThrottleFn(
        ({ moveX, moveY }) => {
            setPosition({ moveX, moveY });
        },
        { wait: 200 },
    );

    // 是否正在识别语音
    const [recognizing, setRecognizing] = useState<boolean>(); // 是否识别中
    // 停止识别
    const stop = () => {
        stopRecognize();
        setRecognizing(false);
    };
    // 开始识别
    const start = () => {
        initAndStartRecognize({
            onSuccess: () => {
                setRecognizing(true);
            },
            // 展示中间结果
            onGetMiddleResult: (d) => {
                setText(text + d.text);
            },
            // 展示最终结果
            onGetFinalResult: (d) => {
                setText(d.text);
                setValid(d.text);
                setIsEnd(true);
            },
            onGetVolume: (d) => {
                setVolumes([d, ...getVolumes()]);
            },
            onFail: () => {
                setValid(false);
                setIsEnd(true);
                setRecognizing(false);
            },
        });
    };
    const initData = () => {
        setLeaved(false);
        setMoved(false);
        setText('');
        setRecognizing(false);
        setValid(false);
        setCanMoveCancel(true);
        setIsEnd(false);
        setVolumes([]);
    };

    useEffect(() => {
        if (recognizing || !voiceInputOpen) {
            return;
        }
        // 语音输入页面打开自动开始识别语音
        start();
    }, [voiceInputOpen]);

    const { send } = useSendMessage();
    const isWithFile = useMessage((state) => state.isWithFile);
    const file = useMessage((state) => state.file);
    const clearFile = useMessage((state) => state.clearFile);
    const { run: closeVoiceInput } = useDebounceFn(
        (needSend: boolean) => {
            if (needSend) {
                if (!text) {
                    Toast.open('未识别到内容');
                } else {
                    if (isWithFile()) {
                        send(
                            getAdditionMessage(file, text),
                            EntryPointType.VOICE,
                        );
                        clearFile();
                    } else {
                        send(text, EntryPointType.VOICE);
                    }
                }
            }
            stop();
            setVoiceInputOpen(false);
        },
        { wait: 200 },
    );
    // 滑动事件监听和处理
    const panResponder = useRef(
        PanResponder.create({
            // Ask to be the responder:
            onStartShouldSetPanResponder: () =>
                getVoiceInputOpen() && !getMoved() && !getLeaved(),
            // onStartShouldSetPanResponderCapture: () => true,
            onMoveShouldSetPanResponder: () =>
                getVoiceInputOpen() && !getMoved() && !getLeaved(),
            // onMoveShouldSetPanResponderCapture: () => true,
            // onPanResponderGrant: (evt, gestureState) => {
            //     // The gesture has started. Show visual feedback so the user knows
            //     // what is happening!
            //     // gestureState.d{x,y} will be set to zero now
            // },
            onPanResponderMove: (evt, gestureState) => {
                setMoved(true);
                setPositionThrottle(gestureState);
                startAnimation(LayoutAnimation.Presets.linear);
            },
            // onPanResponderTerminationRequest: (evt, gestureState) => true,
            onPanResponderRelease: () => {
                // The user has released all touches while this view is the
                // responder. This typically means a gesture has succeeded
                setCanMoveCancel(false); //第一次离开屏幕后便不允许滑动取消
                stop();
                setLeaved(true);
                setTimeout(() => {
                    if (!getStopDefault()) {
                        closeVoiceInput(true);
                    }
                }, 200);
            },
            // onPanResponderTerminate: (evt, gestureState) => {
            //     // Another component has become the responder, so this gesture
            //     // should be cancelled
            // },
            // onShouldBlockNativeResponder: (evt, gestureState) => {
            //     // Returns whether this component should block native components from becoming the JS
            //     // responder. Returns true by default. Is currently only supported on android.
            //     return true;
            // },
        }),
    ).current;
    return {
        panResponder,
        voiceInputOpen,
        openVoiceInput: () => {
            setViewKey(viewKey + 1);
            initData();
            setVoiceInputOpen(true);
        },
        setStopDefault,
        closeVoiceInput,
        volumes,
        valid,
        text,
        setText,
        recognizing,
        isEnd,
        position,
        leaved,
        setLeaved,
        viewKey,
        stop,
    };
};
export default useVoiceInput;
