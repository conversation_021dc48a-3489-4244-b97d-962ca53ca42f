import { Keyboard, Platform } from '@mrn/react-native';
import { useEffect, useState } from 'react';

import { useUiState } from '../store/uiState';

const useKeyboard = () => {
    const [keyboardOffset, setKeyboardOffset] = useState(0);
    const [isKeyboardShow, setIsKeyboardShow] = useState(false);
    const { setPanelOpen } = useUiState();

    useEffect(() => {
        let showSubscription, hideSubscription;
        if (Platform.OS === 'ios') {
            showSubscription = Keyboard.addListener('keyboardWillShow', (e) => {
                const keyboardHeight = e.endCoordinates.height;
                setKeyboardOffset(keyboardHeight); // 设置键盘高度
                setIsKeyboardShow(true);
                setPanelOpen(false);
            });
            hideSubscription = Keyboard.addListener('keyboardWillHide', () => {
                setKeyboardOffset(0); // 重置键盘高度
                setIsKeyboardShow(false);
            });
        }
        if (Platform.OS === 'android') {
            showSubscription = Keyboard.addListener('keyboardDidShow', (e) => {
                const keyboardHeight = e.endCoordinates.height;
                setKeyboardOffset(keyboardHeight); // 设置键盘高度
                setIsKeyboardShow(true);
                setPanelOpen(false);
            });
            hideSubscription = Keyboard.addListener('keyboardDidHide', () => {
                setKeyboardOffset(0); // 重置键盘高度
                setIsKeyboardShow(false);
            });
        }

        return () => {
            showSubscription?.remove?.();
            hideSubscription?.remove?.();
        };
    }, []);
    return { keyboardOffset, isKeyboardShow };
};
export default useKeyboard;
