import { openPage } from '@mfe/bee-foundation-utils';
import { apiCaller } from '@mfe/cc-api-caller-bee';
import { StyleSheet, Text, View } from '@mrn/react-native';
import { Dialog, MTDTopViewContext } from '@roo/roo-rn';
import _ from 'lodash';
import React, { useContext, useEffect } from 'react';

import { VERSION_PARAMS_3 } from '../../consts';
import {
    createDefaultMessage,
    HISTORY_SYS_MESSAGE_ID,
} from '../../store/message';
import {
    AbilityType,
    MessageContentType,
    MessageStatus,
    MessageType,
} from '../../types';
import RootTagContext from '../rootTagContext';
import { useBizInfo } from '../useBizInfo';
import useCallerRequest from '../useCallerRequest';
import useGrayInfo from '../useGrayInfo';
import useMessage from '../useMessage';

import { useUiState } from '@/store/uiState';

const styles = StyleSheet.create({
    container: {
        position: 'relative',
        marginTop: 14.5,
    },
    closeCircle: {
        height: 24,
        width: 24,
        borderRadius: 12,
        backgroundColor: 'rgba(255, 255, 255, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    complex: {
        paddingHorizontal: 16,
        zIndex: 10,
        flexDirection: 'row',
    },
    simple: {
        paddingHorizontal: 16,
        flexDirection: 'row',
        justifyContent: 'flex-end',
        paddingBottom: 12,
        left: 0,
        right: 0,
    },
    hi: {
        fontWeight: '900',
        fontSize: 16,
        lineHeight: 18,
    },
    title: {
        fontWeight: '600',
        fontSize: 14,
        lineHeight: 18,
    },
    text: {
        fontSize: 11,
        lineHeight: 15,
        marginTop: 2,
    },
    underline: {
        textDecorationLine: 'underline',
        marginLeft: 2,
    },
    noticeText: {
        color: '#666',
        textAlign: 'center',
    },
    link: {
        color: '#FF6A00',
    },
    headMid: {
        justifyContent: 'center',
        marginLeft: 8,
        flex: 1,
    },
    image: {
        height: 24,
        width: 24,
    },
});

interface Props {
    navigator: { pop: () => void };
    aichat_scene_id: number;
    aichat_scene_params: string; // json
    source: string;
    extra: string;
}

export const useStartChat = (props: Props) => {
    const add = useMessage((state) => state.add);
    const setHasHistory = useMessage((state) => state.setHasHistory);
    const pollingMessage = useMessage((state) => state.pollingMessage);
    const delayScrollToEnd = useMessage((state) => state.delayScrollToEnd);
    const appendSessionId = useMessage((state) => state.appendSessionId);
    const setClipboardData = useMessage((state) => state.setClipboardData);
    const setIsPollingMessage = useMessage(
        (state) => state.setIsPollingMessage,
    );
    const clearMsg = useMessage((state) => state.reset);
    const modalContext = useContext(MTDTopViewContext);
    const grayInfo = useGrayInfo();
    const bizInfo = useBizInfo();
    const { rootTag } = useContext(RootTagContext);
    const callerRequest = useCallerRequest();
    const setShowHome = useUiState((state) => state.setShowHome);
    const { source, extra } = props;
    const gotoInstruction = () => {
        openPage(
            'meituanwaimaibee://beewaimai.meituan.com/mrn?mrn_biz=waimaicrm&mrn_entry=bee-assistant-main&mrn_component=bee-assistant&initialRoute=Instruction',
        );
    };

    const createChat = async () => {
        const res = await apiCaller.send('/bee/v1/bdaiassistant/openSession', {
            ...VERSION_PARAMS_3,
            source,
            extra,
        });
        if (res.code !== 0) {
            return;
        }

        appendSessionId(res.data.sessionId);

        if (res.data.existChatRecord) {
            setHasHistory(true);
            add({
                status: MessageStatus.DONE,
                type: MessageType.SYSTEM,
                msgId: _.uniqueId(HISTORY_SYS_MESSAGE_ID),
                abilityType: undefined,
                subAbilityType: undefined,
                msgType: MessageContentType.TEXT,
                currentContent: '下拉查看历史对话',
            });
        }

        if (props.aichat_scene_id) {
            const params = Object.keys(props)
                .filter((v) => v.startsWith('aichat_scene'))
                .map((v) => ({ [v.replace('aichat_scene_', '')]: props[v] }))
                .reduce((pre, cur) => ({ ...pre, ...cur }));
            const messagesRes = await callerRequest.post(
                '/bee/v1/bdaiassistant/triggerScene',
                {
                    id: props.aichat_scene_id,
                    params:
                        params?.params ||
                        JSON.stringify(_.omit(params, ['id'])),
                    bizId: bizInfo.bizId,
                    sessionId: res.data.sessionId,
                },
            );
            if (messagesRes.code !== 0) {
                return;
            }
            setShowHome(false);
            const messages = messagesRes.data as any[];
            for (let msg of messages) {
                const curMsg = { ...msg };
                if (curMsg.msgType === MessageType.QUESTION) {
                    curMsg.msgId = curMsg.id;
                    delete curMsg.questionMsgId;
                }
                add({ ...curMsg, status: MessageStatus.DONE });
            }
            if (messages.length) {
                const lastMsg = _.last(messages);
                if (lastMsg.msgType !== MessageType.QUESTION) {
                    return;
                }
                add(createDefaultMessage({ questionMsgId: lastMsg.id }));
                lastMsg.questionMsgId = lastMsg.id;
                delete lastMsg.id;
                lastMsg.abilityType = AbilityType.GENERAL;

                setTimeout(() => {
                    setIsPollingMessage(true);
                    pollingMessage(lastMsg);
                }, 200);
            }
            delayScrollToEnd();
        }

        const getClipboardData = async () => {
            try {
                const extraData =
                    typeof extra === 'string' ? JSON.parse(extra) : extra;
                if (!extraData?.clipboard) {
                    return;
                }
                const clipboardRes = await apiCaller.get(
                    '/bee/v2/bdaiassistant/clipboard/content',
                    {
                        type: extraData.clipboard,
                        sessionId: res.data.sessionId,
                    },
                );
                if (clipboardRes.code !== 0) {
                    return;
                }
                clipboardRes.data && setClipboardData(clipboardRes.data);
            } catch (e) {
                console.log(e);
            }
        };
        getClipboardData();
    };

    const signAgreement = async () => {
        Dialog.open({
            body: (
                <View>
                    <Text style={styles.noticeText}>
                        使用前请阅读同意
                        <Text style={styles.link} onPress={gotoInstruction}>
                            《使用说明》
                        </Text>
                        ，让小蜜成为您专属助手吧~
                    </Text>
                </View>
            ),
            cancelLabel: '取消',
            confirmLabel: '同意并继续',
            cancelCallback: props.navigator.pop,
            confirmCallback: () => {
                createChat();
                apiCaller.send(
                    '/bee/v1/bdaiassistant/signInstructions',
                    {},
                    { silent: true },
                );
            },
            modalProps: {
                // @ts-ignore
                context: modalContext,
                maskClosable: false,
            },
        });
    };

    const fetchShouldSign = async () => {
        const res = await apiCaller.get(
            '/bee/v1/bdaiassistant/existSignRecord',
            {},
            { silent: true },
        );

        if (res.code === 0 && res.data.sign) {
            createChat();
            return;
        }

        signAgreement();
    };

    const openPermissionModal = async () => {
        Dialog.alert({
            confirmCallback: props.navigator.pop,
            message: '该功能正在内测中，很快会开放给全部同学使用。',
            confirmLabel: '知道了',
            modalProps: {
                context: modalContext,
                maskClosable: false,
            },
        });
    };

    useEffect(() => {
        // grayInfo未就绪
        if (!grayInfo) {
            return;
        }
        clearMsg();
        // 有权限
        if (grayInfo?.gray) {
            fetchShouldSign();
            return;
        }
        openPermissionModal();
    }, [grayInfo, rootTag, bizInfo.bizId]);
};
