import { Source } from '@mfe/bee-foundation-moses';
import { useSafeAreaInsets } from '@mfe/bee-foundation-navigation';
import { apiCaller } from '@mfe/cc-api-caller-bee';
import { TraceUtil } from '@mfe/waimai-mfe-bee-common';
import KNB from '@mrn/mrn-knb';
import { openUrl } from '@mrn/mrn-utils';
import ReactNative, {
    Platform,
    TouchableOpacity,
    StatusBar,
    ScrollView,
    Image,
} from '@mrn/react-native';
import { LinearGradient } from '@mrn/react-native-linear-gradient';
import { Icon, NavigationBar } from '@roo/roo-rn';
import React, { useState, useEffect } from 'react';

import ComplainOld from './ComplainOld';
import WelcomeText from '../../assets/images/complain/welcome.png';
import AssistantIcon from '../../components/AssistantIcon';
import useAssistantClose from '../../hooks/useAssistantClose';
import useGrayInfo from '../../hooks/useGrayInfo';
import TWS from '../../TWS';
import { SOURCE } from '../../types';
import LXTrack from '../../utils/lxTrack';
import openChat from '../../utils/openChat';
import { trackEvent } from '../../utils/track';

const schemeRouter = Platform.select({
    ios: 'meituanwaimaibee://www.meituan.com/mrn?',
    default: 'meituanwaimaibee://beewaimai.meituan.com/mrn?new_bundle=1&',
});

const { View, Text } = ReactNative;

const TtNumber = ({ ttNumber, title, onPress }) => (
    <TouchableOpacity
        onPress={onPress}
        style={[
            {
                backgroundColor: '#f5f6fa',
                width: 153.5,
                borderRadius: 6.5,
                paddingVertical: 8,
                paddingLeft: 12,
                paddingRight: 10,
            },
        ]}
    >
        <Text style={[{ fontWeight: '500', color: '#3d3d3d', fontSize: 14 }]}>
            {title}
        </Text>
        <View
            style={[
                TWS.row(),
                {
                    justifyContent: 'space-between',
                    marginTop: 2,
                    alignItems: 'center',
                },
            ]}
        >
            <Text style={[{ fontWeight: '500', color: '#222', fontSize: 14 }]}>
                {ttNumber}
            </Text>
            <Icon type="chevron-right" />
        </View>
    </TouchableOpacity>
);

const Complain = (props) => {
    const source = props.source;
    const [state, innerSetState] = useState({
        isNoticeVisible: false, // 公告是否展示
        myReporterTTNumber: 0, // 我发起的 TT 工单
        myResolvedTTNumber: 0, // 待我处理的 TT 工单
        noticeInfo: '', // 公告内容信息
        message: '', // 入口页下方提示信息
        ttQueryString: '',
    });
    const setState = (state) => {
        innerSetState((preState) => ({ ...preState, ...state }));
    };

    useEffect(() => {
        trackEvent('complain_source', { title: source });
    }, []);

    const getTTInfo = async () => {
        const res: any = await apiCaller.get('/bee/api/helpbd/r/ttInfo', {});
        const {
            data: { reporter = 0, unresolved = 0, ttSchemeParams = {} } = {},
        } = res;
        let ttQueryString = '';
        Object.keys(ttSchemeParams).forEach((key) => {
            ttQueryString += `&${key}=${encodeURIComponent(
                ttSchemeParams[key],
            )}`;
        });
        setState({
            myReporterTTNumber: reporter,
            myResolvedTTNumber: unresolved,
            ttQueryString,
        });
    };

    useEffect(() => {
        TraceUtil.traceLXEvent('complain_pageview');
        getTTInfo();
    }, []);

    const handleJumpResolved = () => {
        const { ttQueryString } = state;
        LXTrack.moduleClick(
            'c_waimai_e_bee_mine',
            'b_waimai_e_bee_mine_assistant_work_order_mine_mc',
        );
        openUrl(
            `${schemeRouter}mrn_biz=bfe&mrn_entry=tt&mrn_component=tthomelist&pageType=created${ttQueryString}`,
        );
    };

    const handleJumpReporter = () => {
        const { ttQueryString } = state;
        LXTrack.moduleClick(
            'c_waimai_e_bee_mine',
            'b_waimai_e_bee_mine_assistant_work_order_todo_mc',
        );
        openUrl(
            `${schemeRouter}mrn_biz=bfe&mrn_entry=tt&mrn_component=tthomelist&pageType=todo${ttQueryString}`,
        );
    };

    const handleJumpTTList = () => {
        const { ttQueryString } = state;
        LXTrack.moduleClick(
            'c_waimai_e_bee_mine',
            'b_waimai_e_bee_mine_assistant_work_order_mc',
        );
        openUrl(
            `${schemeRouter}mrn_biz=bfe&mrn_entry=tt&mrn_component=tthomelist&pageType=created${ttQueryString}`,
        );
    };

    const { myReporterTTNumber, myResolvedTTNumber } = state;

    const insets = useSafeAreaInsets();
    const statusBarHeight = StatusBar.currentHeight || insets.top;

    // 现在跳转在线提问页的只有工作台和个人主页，后续如果添加了其他入口，需调整此处逻辑，建立Complain页面source和Chat页面source的映射关系
    useAssistantClose(
        source === Source.WORKBENCH ? SOURCE.mine : SOURCE.workbench,
        source,
    );

    const ComplainNew = (
        <ScrollView
            style={{
                paddingHorizontal: 16,
                backgroundColor: '#f5f6fa',
                flex: 1,
                marginTop: -statusBarHeight,
                paddingTop: statusBarHeight,
            }}
        >
            <StatusBar
                backgroundColor="rgba(255,255,255,0)"
                barStyle="light-content"
                translucent // 仅对安卓生效，意味可将内容渲染到status，iOS默认支持
            />
            <NavigationBar
                backgroundColor={'#f5f6fa'}
                title={'在线提问'}
                onPressBackButton={() => {
                    props.navigation.goBack();
                }}
                right={
                    <TouchableOpacity
                        onPress={() => {
                            KNB.openPage({
                                url: 'https://km.sankuai.com/page/2651496459',
                            });
                        }}
                    >
                        <Text>使用指南</Text>
                    </TouchableOpacity>
                }
            />
            <LinearGradient
                colors={['#FFF2C8', '#FFFFFF']}
                start={{ x: 0.4, y: 0.5 }}
                style={[
                    TWS.row(),
                    {
                        paddingVertical: 19,
                        paddingRight: 36,
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        marginTop: 12,
                        borderRadius: 10.5,
                    },
                ]}
            >
                <View style={{ marginLeft: 22 }}>
                    <Image
                        source={WelcomeText}
                        style={{ width: 176, resizeMode: 'contain' }}
                    />
                    <Text
                        style={{
                            fontSize: 12,
                            color: '#666',
                            marginTop: 9,
                        }}
                    >
                        遇到问题都可以向我提问哦
                    </Text>
                </View>

                <TouchableOpacity
                    onPress={() => {
                        LXTrack.moduleClick(
                            'c_waimai_e_bee_mine',
                            'b_waimai_e_bee_mine_assistant_question_mc',
                        );
                        openChat(SOURCE.mine);
                    }}
                >
                    <AssistantIcon size={63.5} />
                    <View
                        style={{
                            backgroundColor: '#ffdd00',
                            borderRadius: 12,
                            alignItems: 'center',
                            justifyContent: 'center',
                            width: 64,
                            height: 24,
                        }}
                    >
                        <Text
                            style={{
                                fontSize: 11,
                                fontWeight: '500',
                                color: '#3d3d3d',
                            }}
                        >
                            点我提问
                        </Text>
                    </View>
                </TouchableOpacity>
            </LinearGradient>
            <View
                style={[
                    TWS.card('#fff', 12, 0),
                    { borderRadius: 10.5, marginTop: 12 },
                ]}
            >
                <Text
                    style={[
                        { fontWeight: '500', color: '#3d3d3d', fontSize: 16 },
                    ]}
                >
                    我的TT工单
                </Text>
                <View
                    style={[
                        TWS.row(),
                        { justifyContent: 'space-between', marginTop: 13 },
                    ]}
                >
                    <TtNumber
                        ttNumber={myResolvedTTNumber}
                        title={'待我处理'}
                        onPress={handleJumpResolved}
                    />
                    <TtNumber
                        ttNumber={myReporterTTNumber}
                        title={'我发起的'}
                        onPress={handleJumpReporter}
                    />
                </View>
            </View>
        </ScrollView>
    );

    const grayInfo = useGrayInfo();
    if (!grayInfo) {
        return null;
    }
    return (
        <>
            {grayInfo.gray ? (
                ComplainNew
            ) : (
                <ComplainOld
                    {...props}
                    {...state}
                    handleJumpReporter={handleJumpReporter}
                    handleJumpResolved={handleJumpResolved}
                    handleJumpTTList={handleJumpTTList}
                />
            )}
        </>
    );
};
export default Complain;
