import { useNavigation } from '@mfe/bee-foundation-navigation';
import { apiCaller } from '@mfe/cc-api-caller-bee';
import { ActivityIndicator, ScrollView, Text, View } from '@mrn/react-native';
import { Loading, NavigationBar } from '@roo/roo-rn';
import { useRequest } from 'ahooks';
import React from 'react';

const Instruction = () => {
    const navigation = useNavigation();

    const fetchInstruction = async () => {
        const res = await apiCaller.get(
            '/bee/v1/bdaiassistant/getInstructions',
            {},
        );

        if (res.code !== 0) {
            return;
        }

        return res.data.instructions;
    };

    const { data = '', loading } = useRequest(fetchInstruction);

    return (
        <View style={{ flex: 1 }}>
            <NavigationBar
                title={'使用说明'}
                onPressBackButton={navigation.goBack}
            />

            <Loading visible={loading}>
                <ActivityIndicator color="white" size="small" />
            </Loading>
            <ScrollView style={{ flex: 1, padding: 12 }}>
                <Text>{data}</Text>
            </ScrollView>
        </View>
    );
};

export default Instruction;
