import { useSafeAreaInsets } from '@mfe/bee-foundation-navigation';
import { apiCaller } from '@mfe/cc-api-caller-bee';
import KNB from '@mrn/mrn-knb';
import {
    Animated,
    Dimensions,
    Image,
    KeyboardAvoidingView,
    LayoutAnimation,
    NativeScrollEvent,
    NativeSyntheticEvent,
    Platform,
    ScrollView,
    StatusBar,
    StyleSheet,
    TextInput,
    UIManager,
    View,
    // @ts-ignore
    unstable_RootTagContext,
    BackHandler,
    ImageStyle,
} from '@mrn/react-native';
import React, { useContext, useEffect, useMemo, useRef, useState } from 'react';

import useBg from './hooks/useBg';
import ChatContent from '../../components/Chat/ChatContent';
import ChatFooter from '../../components/Chat/ChatFooter/ChatFooterNew';
import ChatHeader from '../../components/Chat/ChatHeader/ChatHeaderNew';
import PoiSelector from '../../components/Chat/PoiSelector/PoiSelector';
import VoiceInput2 from '../../components/VoiceInput/VoiceInput2';
import { useStartChat } from '../../hooks/biz/useStartChat';
import RootTagContext from '../../hooks/rootTagContext';
import useKeyboard from '../../hooks/useKeyboard';
import useMessage, { getUseMessage } from '../../hooks/useMessage';
import useVoiceInput from '../../hooks/useVoiceInput';
import { ModalWrapper } from '../../store/slideModal';
import { useUiState } from '../../store/uiState';
import { SOURCE } from '../../types';
import getConditionStyle from '../../utils/getConditionStyle';
import { track } from '../../utils/track';

import ChatHome from '@/components/Chat/ChatHome/ChatHome';
import Condition from '@/components/Condition/Condition';

const styles = StyleSheet.create({
    imageBg: {
        position: 'absolute',
        width: Dimensions.get('window').width,
        height: (Dimensions.get('window').width * 4872) / 2250,
    } as ImageStyle,
});

interface Chat {}

const Wrapper = ({ offset, style = {}, focused, ...props }) => {
    const { keyboardOffset, isKeyboardShow } = useKeyboard();

    const innerStyle = useMemo(() => {
        if (Platform.OS === 'ios') {
            return { flex: 1, paddingBottom: focused ? keyboardOffset : 0 };
        }
        if (!isKeyboardShow) {
            return {
                flex: 1,
            };
        }
        try {
            UIManager.setLayoutAnimationEnabledExperimental(true);
            LayoutAnimation.configureNext(
                LayoutAnimation.Presets.easeInEaseOut,
            );
        } catch (e) {
            console.log(e);
        }
        return {
            height:
                Dimensions.get('window').height -
                keyboardOffset -
                StatusBar.currentHeight,
        };
    }, [keyboardOffset, offset.current, isKeyboardShow]);

    return Platform.OS === 'ios' ? (
        <KeyboardAvoidingView
            {...props}
            behavior={'padding'}
            style={[innerStyle, style]}
        />
    ) : (
        <View {...props} style={[innerStyle, style]} />
    );
};

// Chat页面可能直接挂载在主包路由中，因此不能直接使用useNavigation
// 如果需要跳转，请使用props.navigator.push/pop
// 这个方法在通过业务子包方式打开时，会使用useNavigation().navigation
// 在通过直接挂载主包的方式打开时，会使用主包路由的navigator
const Chat = (props) => {
    const insets = useSafeAreaInsets();
    const statusBarHeight = StatusBar.currentHeight || insets.top;
    const disconnect = useMessage((state) => state.disconnect);
    const reset = useMessage((state) => state.reset);
    const showHome = useUiState((state) => state.showHome);

    useStartChat(props);

    const scrollRef = useRef<ScrollView>();
    const inputRef = useRef<TextInput>();

    const scrollY = new Animated.Value(0);

    const moveParams = useVoiceInput();
    const { panResponder, openVoiceInput, voiceInputOpen, viewKey } =
        moveParams;

    const onScroll = ({
        nativeEvent,
    }: NativeSyntheticEvent<NativeScrollEvent>) => {
        return scrollY.setValue(nativeEvent.contentOffset.y);
    };

    const getLatestSessionId = useMessage((v) => v.getLatestSessionId);
    // 页面退出的时候，关闭轮询
    useEffect(() => {
        track('chat');
        return () => {
            apiCaller.send(
                '/bee/v1/bdaiassistant/closeSession',
                {
                    sessionId: getLatestSessionId(),
                },
                { silent: true },
            );
            // 通知首页弹出评价弹窗
            KNB.publish({
                action: 'ASSISTANT_CLOSE',
                data: { from: 'chat', to: props.source || SOURCE.home },
                success: () => {
                    console.log('ASSISTANT_CLOSE success');
                },
                fail: () => {
                    console.log('ASSISTANT_CLOSE fail');
                },
            });
            disconnect();
            reset();
        };
    }, []);

    const offset = useRef(Platform.OS === 'ios' ? insets.bottom : 20);
    useEffect(() => {
        // iOS在键盘消失后，insets.bottom可能会变化，所以这里只取第一次的默认值
        if (offset.current) {
            return;
        }

        offset.current = Platform.OS === 'ios' ? insets.bottom || 34 : 20;
    }, [insets.bottom]);

    const paddingOffset = Platform.OS === 'ios' ? 0 : 15;

    const [focused, setFocused] = useState(false);
    const { setPanelOpen } = useUiState();

    const bg = useBg();

    const sessionId = useMessage((state) => state.sessionId);
    if (!sessionId.length) {
        return null;
    }

    return (
        <>
            <Wrapper
                onTouchStart={() => {
                    setPanelOpen(false);
                }}
                {...panResponder.panHandlers}
                offset={offset}
                focused={focused}
            >
                <View
                    style={[
                        {
                            flex: 1,
                            backgroundColor: '#f5f6fa',
                            marginTop: -statusBarHeight - 12, // 上移，对statusbar添加背景色
                            paddingTop: statusBarHeight + 12, // 修正上移导致的内容偏上
                        },
                        getConditionStyle(
                            {
                                marginBottom: -insets.bottom, // 覆盖touchbar底色
                                paddingBottom: insets.bottom || paddingOffset, // 修正为了为了覆盖底色导致的内容底部扩大，ios为底部touchbar的高度，安卓则用默认值
                            },
                            Platform.OS === 'ios',
                        ),
                    ]}
                >
                    <Image source={{ uri: bg }} style={[styles.imageBg]} />
                    <StatusBar
                        backgroundColor="rgba(255,255,255,0)"
                        barStyle="light-content"
                        translucent // 仅对安卓生效，意味可将内容渲染到status，iOS默认支持
                    />

                    <ChatHeader {...props} scrollHeight={scrollY} />

                    <Condition condition={[!showHome, showHome]}>
                        <ChatContent
                            ref={scrollRef}
                            inputRef={inputRef}
                            onScroll={onScroll}
                        />
                        <ChatHome />
                    </Condition>

                    <ChatFooter
                        voiceInputOpen={voiceInputOpen}
                        onVoiceLongPress={() => openVoiceInput()}
                        ref={inputRef}
                        offset={offset.current}
                        onFocus={() => setFocused(true)}
                        onBlur={() => setFocused(false)}
                    />
                </View>
                <PoiSelector />
                <ModalWrapper />

                {/*Assistant Logo测试代码，后续Assistant UI迭代可以打开注释进行调试*/}
                {/*<Assistant navigator={props.navigator} scrollY={scrollY} />*/}
            </Wrapper>
            <VoiceInput2
                key={viewKey}
                style={[
                    {
                        marginTop: -statusBarHeight - 12, // 上移，对statusbar添加背景色
                        paddingTop: statusBarHeight + 12, // 修正上移导致的内容偏上
                    },
                    Platform.OS === 'ios'
                        ? {
                              marginBottom: -insets.bottom, // 覆盖touchbar底色
                              paddingBottom: insets.bottom || paddingOffset,
                          }
                        : {},
                ]}
                {...moveParams}
            />
        </>
    );
};

export default (props) => {
    const rootTag = useContext(unstable_RootTagContext);
    const uiState = useUiState();
    const useMessage = useRef(getUseMessage(props.rootTag || rootTag, uiState));
    useEffect(() => {
        uiState.reset();
        if (!props.rootTag) {
            return;
        }
        useMessage.current = getUseMessage(props.rootTag, uiState);
    }, [props.rootTag]);
    const [backing, setBacking] = useState(false);
    const pop = () => {
        setBacking(true);
        setTimeout(props.navigator.pop, 200);
    };
    useEffect(() => {
        if (Platform.OS !== 'android') {
            return;
        }
        const remove = BackHandler.addEventListener('hardwareBackPress', () => {
            pop();
            return true;
        });
        return remove.remove;
    }, []);
    if (!useMessage.current) {
        return null;
    }
    return (
        <RootTagContext.Provider
            value={{
                rootTag: props?.rootTag,
                useMessage: useMessage.current,
                pop,
                backing,
                source: props.source,
                extra: props.extra,
            }}
        >
            <Chat {...props} />
        </RootTagContext.Provider>
    );
};
