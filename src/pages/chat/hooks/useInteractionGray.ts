import { apiCaller } from '@mfe/cc-api-caller-bee';
import { useRequest } from 'ahooks';

/**
 * 获取交互灰度状态的hook
 */
const useInteractionGray = () => {
    const { data, loading } = useRequest(
        async () => {
            const res = await apiCaller.get(
                '/bee/v2/bdaiassistant/common/gray',
                {},
            );
            if (res.code !== 0) {
                return;
            }
            return res.data;
        },
        { cacheKey: 'interactionGray' },
    );

    return {
        isGray: data?.interactionGray,
        loadingGray: loading,
        pictureQuestionGray: data?.pictureQuestionGray,
    };
};

export default useInteractionGray;
