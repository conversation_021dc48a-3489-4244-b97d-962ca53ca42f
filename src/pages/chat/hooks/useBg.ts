import NetImages from '@/assets/images/homeRefactor';
import useWeather from '@/hooks/useWeather';

const useBg = () => {
    const { weatherInfo } = useWeather();

    switch (weatherInfo?.weatherType) {
        case 'rain':
            return NetImages.rainBackground;
        case 'cold':
            return NetImages.coldBackground;
        case 'hot':
            return NetImages.hotBackground;
        default:
            return NetImages.defaultBackground;
    }
};

export default useBg;
