import { produce } from 'immer';
import { create } from 'zustand';

interface ScrollState {
    block: boolean;
    setBlock: (b: boolean) => void;
    reset: () => void;
}

// 用来阻塞auto scroll
// 聊天框会监听FlatList(ScrollView)的onContentSizeChange
// 当高度变化时，会只执行scrollToEnd或者scrollToIndex({viewPosition: 0})来自滚动到底部/顶部
// 某些场景下我们不需要这个自动逻辑，这个时候需要设置setBlock(true)来阻塞自动滚动
export const useScroll = create<ScrollState>((set) => ({
    block: false,
    setBlock: (b: boolean) => {
        set(
            produce<ScrollState>((state) => {
                state.block = b;
            }),
        );
    },

    reset() {
        set(
            produce<ScrollState>((state) => {
                state.block = false;
            }),
        );
    },
}));
