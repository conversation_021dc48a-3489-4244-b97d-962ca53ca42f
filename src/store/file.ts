import _ from 'lodash';

import { AdditionFile } from '@/types/message';

const defaultState = {
    file: [] as AdditionFile[],
};

type State = typeof defaultState;
const getActions = (set: Setter, get: Getter) => ({
    addFile: (file: Omit<AdditionFile, 'key'>) => {
        const draftFile = { ...file, key: _.uniqueId('file_') };
        set({
            file: [...get().file, draftFile],
        });
        return draftFile.key;
    },
    setFile: (file: Omit<AdditionFile, 'key'>[]) => {
        const draftFile = file.map((f) => ({ ...f, key: _.uniqueId('file_') }));
        set({
            file: draftFile,
        });
        return draftFile.map((f) => f.key);
    },
    updateFile: (key: string, file: Partial<AdditionFile>) => {
        set({
            file: get().file.map((f) =>
                f.key === key ? { ...f, ...file } : f,
            ),
        });
    },
    removeFile: (key: string) => {
        set({
            file: get().file.filter((f) => f.key !== key),
        });
    },
    clearFile: () => {
        set({
            file: [],
        });
    },
    isWithFile: () => {
        return get().file.length > 0;
    },
});
export type FileStateAndActions = State & ReturnType<typeof getActions>;
type Setter = (v: Partial<State>) => void;
type Getter = () => FileStateAndActions;

export default { getActions, defaultState, key: 'file' };
