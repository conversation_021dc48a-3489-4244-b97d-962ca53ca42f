import { APISpec, apiCaller } from '@mfe/cc-api-caller-bee';
import { produce } from 'immer';
import { create } from 'zustand';

type User = APISpec['/bee/v1/wmcrm/about/r/userOrgInfo']['response'];

interface UserState {
    user: User;
    loading: boolean;
    fetchUser: () => void;
    getUser: () => User;
}

const store = create<UserState>((set, get) => ({
    loading: false,
    user: {} as User,
    getUser: async () => {
        if (get().user.uid) {
            return get().user;
        }

        const res = await apiCaller.get(
            '/bee/v1/wmcrm/about/r/userOrgInfo',
            {},
        );
        if (res.code !== 0) {
            return {};
        }
        return res.data;
    },
    fetchUser: async () => {
        set(
            produce((state: UserState) => {
                state.loading = true;
            }),
        );
        const res = await apiCaller.get(
            '/bee/v1/wmcrm/about/r/userOrgInfo',
            {},
            { silent: true },
        );
        if (res.code !== 0) {
            return;
        }

        set(
            produce((state: UserState) => {
                state.user = res.data;
                state.loading = false;
            }),
        );
    },
}));

// 初始即执行
store.getState().fetchUser();

export const useUser = store;
