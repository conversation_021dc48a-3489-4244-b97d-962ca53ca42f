import { APISpec } from '@mfe/cc-api-caller-bee';

import { EntryPointType, Message, MessageSendReq } from '../types';

export type MessagePollingReq =
    APISpec['/bee/v1/bdaiassistant/fetchAnswer']['request'];
export enum AddMode {
    APPEND = 'append',
    PREPEND = 'prepend',
}

export type ClipboardData = Partial<{
    content: string;
    toast: string;
    when: 'clickTT';
}>;

export interface MessageState {
    isLoadingHistory: boolean;
    isPollingMessage: boolean;
    hasHistory: boolean;
    minMsgId: string;
    messageList: Message[];
    historyMessageList: Message[];
    sessionId: string[];
    clipboardData: ClipboardData;
    setClipboardData: (data: Partial<MessageState['clipboardData']>) => void;
    getClipboardData: () => ClipboardData;
    getSessionId: () => string[];
    getLatestSessionId: () => string;
    appendSessionId: (id: string) => void;
    add: (m: Message, mode?: AddMode) => void;
    reset: () => void;
    sendMessage: (data: MessageSendReq) => void;
    send: (
        m: string | Omit<MessageSendReq, 'bizId' | 'referer'>,
        bizId: string,
        entryPointType: EntryPointType,
        entryPoint: string,
    ) => void;
    retrySend: (id: string) => Promise<any>;
    pollingMessage: (data: MessagePollingReq, retryCount?: number) => void;
    getHistoryMessageList: (minMsgId?: string) => Promise<void>;
    stopPolling: (msgId: string) => void;
    setTyping: (msgId: string, typing: boolean) => void;
    mutateMsg: (
        msgId: string,
        payload: Partial<Message> | ((msg: Message) => Partial<Message>),
    ) => void;
    disconnect: () => void;
    setHistoryLoading: (loading: boolean) => void;
    setHasHistory: (has: boolean) => void;
    scrollToEnd: (force?: boolean) => void;
    delayScrollToEnd: () => ReturnType<typeof setTimeout>;
    setScrollToEnd: (scrollToEnd: () => void) => void;
    checkIsPolling: () => boolean;
    input: {
        text: string;
        changeEvent: ((str: string) => void)[];
        blur: () => void;
        focus: () => void;
        send: () => void;
        addChangeEvent: (cb: (str: string) => void) => void;
        clear: () => void;
        set: (text: string) => void;
        setInputState: (inputState: Partial<MessageState['input']>) => void;
    };
    refreshSession: () => (
        noRefreshMsg?: boolean,
        callback?: () => void,
    ) => void;
}
