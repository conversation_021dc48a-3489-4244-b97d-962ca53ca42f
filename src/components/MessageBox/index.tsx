import React from 'react';

import Answer from './Answer';
import Question from './Question/QuestionMessage';
import SystemMessage from './SystemMessage';
import { Message, MessageType } from '../../types';

interface MessageBox {
    data: Message;
}

const MessageBox = (props: MessageBox) => {
    switch (props.data.type) {
        case MessageType.QUESTION:
            return <Question {...props} />;
        case MessageType.ANSWER:
            return <Answer {...props} />;
        case MessageType.SYSTEM:
            return <SystemMessage {...props} />;
    }

    return null;
};

export default MessageBox;
