import { useEffect } from 'react';

import useCallerRequest from '../../../../hooks/useCallerRequest';
import { FeedbackType, MessageStatus } from '../../../../types';

import useTrace from '@/hooks/useTrace';

// 用于后端记录用户停止回答
const useSubmitStop = (status, listRef, callback, msgId, feedBackContent) => {
    const callerRequest = useCallerRequest();
    const trace = useTrace();
    const submitStop = async () => {
        if (listRef.current) {
            listRef.current.stop?.();
        }

        if (!callback()) {
            return;
        }
        trace('stop_answer', 'trigger', JSON.stringify({ content: msgId }));
        callerRequest.send(
            '/bee/v1/bdaiassistant/feedBackForChat',
            {
                chatRecordId: msgId,
                type: FeedbackType.BLOCK_ANSWER,
                feedBackContent,
            },
            { silent: true }, // 给后端记录用的，错误不必提示
        );
    };

    useEffect(() => {
        if (status === MessageStatus.STOPPED) {
            submitStop();
            return;
        }

        if (!listRef.current) {
            return;
        }

        listRef.current.stop?.();
    }, [status]);
};
export default useSubmitStop;
