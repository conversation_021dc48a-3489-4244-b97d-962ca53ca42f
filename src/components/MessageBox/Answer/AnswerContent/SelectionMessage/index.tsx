// 兼容性代码
import { View } from '@mrn/react-native';
import React from 'react';

import SelectionContent from './SelectionContent';
import { Message } from '../../../../../types';
import TextMessage from '../TextMessage';

interface SelectionMessage {
    data: Message;
}

const SelectionMessage = (props: SelectionMessage) => {
    const { prefixTextContent = '', postTextContent = '' } = props.data;

    return (
        <View>
            <TextMessage
                data={{ ...props.data, currentContent: prefixTextContent }}
                shouldTyping={false}
            />

            <SelectionContent {...(props as any)} />
            {postTextContent ? (
                <View style={{ marginTop: 8 }}>
                    <TextMessage
                        data={{ ...props, currentContent: postTextContent }}
                    />
                </View>
            ) : null}
        </View>
    );
};

export default SelectionMessage;
