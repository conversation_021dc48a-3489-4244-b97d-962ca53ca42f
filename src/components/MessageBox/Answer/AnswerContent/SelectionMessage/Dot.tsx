import { View, ViewStyle } from '@mrn/react-native';
import React from 'react';

interface Props {
    color?: string;
    width?: number;
    style?: ViewStyle | ViewStyle[];
}
const Dot = ({ color = '#FF192D', width = 8, style }: Props) => {
    return (
        <View
            style={[
                {
                    backgroundColor: color,
                    width,
                    height: width,
                    borderRadius: width / 2,
                },
                ...(Array.isArray(style) ? style : [style]),
            ]}
        />
    );
};

export default Dot;
