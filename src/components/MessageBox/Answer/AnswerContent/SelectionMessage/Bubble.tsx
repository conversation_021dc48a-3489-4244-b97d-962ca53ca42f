import { View, Text, ViewStyle } from '@mrn/react-native';
import React from 'react';

interface Props {
    color?: string;
    content?: string;
    style?: ViewStyle;
}

export const Bubble = ({ color = '#ff192d', content = '新', style }: Props) => {
    return (
        <View
            style={[
                {
                    borderRadius: 7,
                    borderBottomLeftRadius: 2,
                    backgroundColor: color,
                    paddingHorizontal: 4,
                    alignItems: 'center',
                    justifyContent: 'center',
                    height: 14,
                },
                style,
            ]}
        >
            <Text style={[{ fontSize: 11, color: '#fff' }]}>{content}</Text>
        </View>
    );
};
