import { StyleSheet, Text, View } from '@mrn/react-native';
import React from 'react';

import AssistantIcon from '../../../../AssistantIcon';
import TextMessage from '../TextMessage';

const styles = StyleSheet.create({
    head: {
        flexDirection: 'row',
    },
    hi: {
        fontWeight: '900',
        fontSize: 17,
        lineHeight: 19,
    },
    title: {
        fontWeight: '800',
        fontSize: 15,
        lineHeight: 19,
    },
    text: {
        marginTop: 4,
    },
    headRight: {
        justifyContent: 'center',
        marginLeft: 8,
        flex: 1,
    },
});

const WelcomeSelectionMessage = (props) => {
    return (
        <View style={{ flex: 1, marginBottom: 4 }}>
            <View style={styles.head}>
                <AssistantIcon size={50} noText />
                <View style={styles.headRight}>
                    <View style={{ flexDirection: 'row' }}>
                        <Text style={styles.hi}>Hi,</Text>
                        <Text style={styles.title}>我是小蜜智能助手</Text>
                    </View>
                    <Text style={styles.text}>
                        很高兴为你服务，你可以试着问我：
                    </Text>
                </View>
            </View>
            <TextMessage
                data={props.data}
                shouldTyping={false}
                style={{ maxWidth: '100%' }}
            />
        </View>
    );
};

export default WelcomeSelectionMessage;
