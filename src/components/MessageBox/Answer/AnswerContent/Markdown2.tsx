import { useNavigation } from '@mfe/bee-foundation-navigation';
import {
    StyleSheet,
    Text,
    View,
    TouchableOpacity,
    ScrollView,
} from '@mrn/react-native';
import { Icon } from '@roo/roo-rn';
import React, { useMemo } from 'react';
import Markdown from 'react-native-markdown-display';

import { markdownStyles } from './markdownStyle';
import { splitMediaFromMarkdown } from '../../../../utils/delta2message';
import useOpenLink from '../../../../utils/openLink';
import ImageGallery from '../../../ImageGallery/ImageGallery';

import Condition from '@/components/Condition/Condition';

// 基于react-native-markdown-display的md展示容器
const MemoizedMarkdown = React.memo(
    ({
        children,
        tableTitle,
        showRotateButton,
    }: {
        children: string[];
        tableTitle: string;
        showRotateButton?: boolean;
    }) => {
        const { navigate } = useNavigation<any>();

        // 处理表格：将整个格作为一个整体
        const processContent = (text: string) => {
            const lines = text.split('\n');
            const result: string[] = [];
            let currentTable: string[] = [];
            let isInTable = false;
            let otherLines: string[] = [];

            for (let i = 0; i < lines.length; i++) {
                const line = lines[i];
                const isTableLine = /^\|/.test(line);

                if (isTableLine) {
                    if (!isInTable) {
                        isInTable = true;
                        result.push(otherLines.join('\n'));
                        otherLines = [];
                    }
                    currentTable.push(line);
                } else {
                    otherLines.push(line);
                    if (isInTable) {
                        // 表格结束，将整个表格作为一个整体
                        result.push(currentTable.join('\n'));
                        currentTable = [];
                        isInTable = false;
                    }
                }
            }

            // 处理最后一个表格
            if (currentTable.length > 0) {
                result.push(currentTable.join('\n'));
            }
            if (otherLines.length > 0) {
                result.push(otherLines.join('\n'));
            }

            return result;
        };

        // 蜜蜂端由于margin不会合并，所以参考链接前不需要额外的空行，所以需要手动剔除&nbsp;
        const originText = children.join('').replace('&nbsp;', '');
        const parsedData1: any[] = splitMediaFromMarkdown(originText);
        const parsedData2 = parsedData1
            .map((v) => {
                if (v.type !== 'text') {
                    return [v];
                }
                const str = v.insert;
                const processedContent = processContent(str);
                return processedContent.map((v) => {
                    const isTable = /^\|.*/.test(v.split('\n')[0]);
                    if (isTable) {
                        return {
                            type: 'table',
                            insert: v,
                        };
                    }
                    return {
                        type: 'text',
                        insert: v,
                    };
                });
            })
            .reduce((acc, cur) => {
                return [...acc, ...cur];
            }, []);

        return (
            <>
                {parsedData2.map((v, i) => {
                    if (v.type === 'media') {
                        return <ImageGallery images={v.insert.media} key={i} />;
                    }
                    if (v.type === 'table') {
                        return (
                            <View key={v.insert} style={styles.tableScrollView}>
                                <ScrollView
                                    horizontal
                                    showsHorizontalScrollIndicator={false}
                                    style={styles.tableContainer}
                                >
                                    <MemoizedMarkdownInner>
                                        {v.insert}
                                    </MemoizedMarkdownInner>
                                </ScrollView>
                                <Condition condition={[showRotateButton]}>
                                    <TouchableOpacity
                                        style={styles.verticalTextContainer}
                                        onPress={() =>
                                            navigate('HorizontalTable', {
                                                mdData: v.insert,
                                                title: tableTitle,
                                            })
                                        }
                                    >
                                        <Icon
                                            type="rotate"
                                            size={14}
                                            tintColor="#666"
                                        />
                                        <Text style={styles.verticalText}>
                                            横屏查看
                                        </Text>
                                    </TouchableOpacity>
                                </Condition>
                            </View>
                        );
                    }
                    return (
                        <MemoizedMarkdownInner key={v.insert}>
                            {v.insert}
                        </MemoizedMarkdownInner>
                    );
                })}
            </>
        );
    },
    (prev, next) =>
        prev.children.length === next.children.length &&
        prev.tableTitle === next.tableTitle,
);

// Markdown组件没带有memo功能，会疯狂重新渲染
export const MemoizedMarkdownInner = React.memo(
    ({
        children,
        onLinkPress,
    }: {
        children: string;
        onLinkPress?: (url: string) => void;
    }) => {
        const openLink = useOpenLink();

        const rules = useMemo(
            () => ({
                link: (data) => {
                    const { attributes, children } = data;
                    return (
                        <Text
                            style={{ color: '#FF6A00' }}
                            onPress={() => {
                                if (typeof onLinkPress === 'function') {
                                    return onLinkPress(attributes.href);
                                }
                                openLink(attributes.href);
                            }}
                        >
                            {children[0].content}
                        </Text>
                    );
                },
                // 图片剔出来额外展示，点击无法支持展示大图
                image: () => {
                    return null;
                },
            }),
            [openLink],
        );

        return (
            <Markdown
                key={children}
                style={markdownStyles as any}
                rules={rules}
            >
                {children}
            </Markdown>
        );
    },
    (prev, next) => prev.children === next.children,
);

const styles = StyleSheet.create({
    tableScrollView: {},
    tableContainer: {},
    verticalTextContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-start',
        paddingHorizontal: 8,
        marginTop: 4,
    },
    verticalText: {
        color: '#666',
        fontSize: 14,
        marginLeft: 4,
    },
});

export default MemoizedMarkdown;
