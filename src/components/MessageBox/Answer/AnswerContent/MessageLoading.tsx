import { Animated, StyleSheet, View } from '@mrn/react-native';
import React, { useEffect, useRef } from 'react';

const styles = StyleSheet.create({
    container: {
        paddingHorizontal: 4,
        marginBottom: 10,
        paddingVertical: 6.5,
        marginRight: -6,
    },
    item: {
        borderRadius: 7,
    },
    content: {
        position: 'absolute',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        height: 20,
    },
});

const COLOR_SEQUENCE = [
    'rgba(34, 34, 34, 1)',
    'rgba(34, 34, 34, 0.857)',
    'rgba(34, 34, 34, 0.714)',
    'rgba(34, 34, 34, 0.571)',
    'rgba(34, 34, 34, 0.429)',
    'rgba(34, 34, 34, 1)',
];

const SIZE_SEQUENCE = [6, 5, 4, 3, 2, 7];

const deviation = <T,>(arr: T[], i: number) =>
    i ? [...arr.slice(i), ...arr.slice(0, i)] : arr;

const MessageLoading = () => {
    const raw = useRef(new Animated.Value(0)).current;

    const coloFrames = COLOR_SEQUENCE.map((_, i) => ({
        inputRange: COLOR_SEQUENCE.map((_, index) => index),
        outputRange: deviation(COLOR_SEQUENCE, i),
    })).reverse();

    const colorAnimated = coloFrames.map((it) => raw.interpolate(it));

    const sizeFrames = SIZE_SEQUENCE.map((_, i) => ({
        inputRange: SIZE_SEQUENCE.map((_, index) => index),
        outputRange: deviation(SIZE_SEQUENCE, i),
    })).reverse();

    const sizeAnimated = sizeFrames.map((it) => raw.interpolate(it));

    useEffect(() => {
        const animation = Animated.loop(
            Animated.timing(raw, {
                duration: 1500,
                delay: 200,
                toValue:
                    Math.min(COLOR_SEQUENCE.length, SIZE_SEQUENCE.length) - 1,
                useNativeDriver: false,
            }),
        );
        animation.start();

        return animation.stop;
    }, []);

    return (
        <>
            <View style={styles.container}>
                <View style={styles.content}>
                    {COLOR_SEQUENCE.map((key, i) => (
                        <Animated.View
                            key={`${key}_${i}`}
                            style={[
                                styles.item,
                                {
                                    backgroundColor: colorAnimated[i],
                                    width: sizeAnimated[i],
                                    height: sizeAnimated[i],
                                    marginRight: 6,
                                },
                            ]}
                        />
                    ))}
                </View>
            </View>
        </>
    );
};

export default MessageLoading;
