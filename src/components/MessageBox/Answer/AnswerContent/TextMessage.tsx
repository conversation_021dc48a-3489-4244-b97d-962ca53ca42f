import { Style<PERSON><PERSON>, ViewStyle, View } from '@mrn/react-native';
import { useInterval } from 'ahooks';
import _ from 'lodash';
import React, { useEffect, useMemo, useRef, useState } from 'react';

import MemoizedMarkdown from './Markdown2';
import StandardQuestion from './StandardQuestion';
import ThinkContent from './ThinkContent';
import useSubmitStop from './useSubmitStop';
import useMessage from '../../../../hooks/useMessage';
import { useScroll } from '../../../../store/scroll';
import { Message, MessageStatus } from '../../../../types';
import { Config, Message as MessageType } from '../../../../types/message';
import delta2message from '../../../../utils/delta2message';
import { getTypeWriterSpeed } from '../../../../utils/getTypeWriterSpeed';

interface TextMessage {
    data: Message;
    shouldTyping?: boolean;
    style?: StyleProp<ViewStyle>;
    onTypedChange?: (typed: MessageType[]) => void;
}

const isTextType = (type: string) =>
    ['text', 'styledText', 'link'].includes(type);

const TextMessage = (props: TextMessage) => {
    const message = props.data;
    const shouldTyping = props.shouldTyping ?? true;

    const listRef = useRef<any>();

    const setBlock = useScroll((state) => state.setBlock);
    useEffect(() => {
        // 新消息开始渲染时允许自动滚动
        setBlock(false);
    }, []);

    const [originData, setOriginData] = useState([]);

    const { currentContent } = message || {};
    useEffect(() => {
        let draftOriginData = delta2message(currentContent);
        draftOriginData = draftOriginData
            .map((v) => {
                if (isTextType(v.type)) {
                    return v.insert
                        .split('')
                        .map((str) => ({ ...v, insert: str }));
                }
                return [v];
            })
            .reduce((pre, cur) => [...pre, ...cur], []);
        setOriginData(draftOriginData);
        // 如果不需要打字动画则直接展示全部内容
        if (
            props.data.history ||
            !shouldTyping ||
            status === MessageStatus.STOPPED ||
            status === MessageStatus.DONE
        ) {
            setTypedIndex(draftOriginData.length);
        }
    }, [currentContent]);

    // 获取config
    const [config, setConfig] = useState<Config>(null);
    useEffect(() => {
        const draftConfig = originData.find((v) => v.type === 'config');
        draftConfig && setConfig(draftConfig);
    }, [originData]);

    const delayScrollToEnd = useMessage((state) => state.delayScrollToEnd);
    const setTyping = useMessage((state) => state.setTyping);

    const [typedIndex, setTypedIndex] = useState(0);

    const { msgId } = props.data;
    const [status, setStatus] = useState(props.data.status);
    useEffect(() => {
        setStatus(props.data.status);
    }, [props.data.status]);
    const { onTypedChange } = props;

    const clear = useInterval(
        () => {
            if (typedIndex <= originData.length) {
                requestAnimationFrame(() => {
                    setTypedIndex(typedIndex + 1);
                    delayScrollToEnd(200);
                });
            }
        },
        shouldTyping
            ? getTypeWriterSpeed(status, originData.length - typedIndex)
            : null,
    );

    const mutateMsg = useMessage((state) => state.mutateMsg);
    const clearInterval = () => {
        clear();
        // 说明是不是在打字过程中触发的
        if (typedData.length === 0) {
            return;
        }

        setTypedIndex(typedData.length);
        setOriginData(typedData);
        mutateMsg(msgId, {
            status: MessageStatus.DONE,
            currentContent: typedData,
        });
        setStatus(MessageStatus.DONE);
    };

    useEffect(() => {
        if (
            originData.length > 0 &&
            // 打字完成
            originData.length === typedIndex &&
            ([MessageStatus.DONE, MessageStatus.STOPPED].includes(status) ||
                props.data.lastStatus === MessageStatus.DONE)
        ) {
            setTyping(msgId, false);
            clearInterval();
        }
    }, [status, originData.length, typedIndex]);

    const typedData = useMemo(
        () => [...originData.slice(0, typedIndex)].filter(Boolean),
        [originData, typedIndex],
    );

    useEffect(() => {
        onTypedChange?.(originData.slice(0, typedIndex));
    }, [typedIndex]);

    useSubmitStop(
        status,
        listRef,
        () => {
            clearInterval();
            return true;
        },
        msgId,
        JSON.stringify(typedData),
    );

    // 思考数据
    const thinkData = useMemo(
        () => typedData.filter((v) => v.type === 'thinkContent'),
        [typedData],
    );
    const mdData = useMemo(
        () => typedData.filter((v) => v.type === 'markdown'),
        [typedData],
    );
    const otherData = useMemo(
        () =>
            typedData.filter(
                (v) => !['markdown', 'thinkContent'].includes(v.type),
            ),
        [typedData],
    );

    try {
        return (
            <View style={[props.style, config?.insert.config.style]}>
                <ThinkContent
                    status={
                        _.last(typedData)?.type === 'thinkContent'
                            ? 'thinking'
                            : 'done'
                    }
                >
                    {thinkData}
                </ThinkContent>
                {/*正常情况下md数据和标准问数据只会出现其一*/}
                <MemoizedMarkdown
                    key={`markdown-${status}`}
                    tableTitle={config?.insert.config.tableTitle}
                    showRotateButton={[
                        MessageStatus.DONE,
                        MessageStatus.STOPPED,
                        MessageStatus.DONE_AFTER_STOP,
                    ].includes(status)}
                >
                    {mdData.map((v) => v.insert.markdown.text)}
                </MemoizedMarkdown>

                <StandardQuestion
                    status={props.data.status}
                    msgId={props.data.msgId}
                    history={props.data.history}
                    listRef={listRef}
                    shouldTyping={shouldTyping}
                    data={props.data}
                >
                    {otherData}
                </StandardQuestion>
            </View>
        );
    } catch (e) {
        console.log(e);
        return null;
    }
};

export default React.memo(TextMessage, (prev, next) => {
    return (
        prev.data.currentContent.length === next.data.currentContent.length &&
        prev.data.status === next.data.status
    );
});
