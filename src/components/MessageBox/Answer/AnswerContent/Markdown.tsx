import React, { useEffect, useState } from 'react';

import getKeyFromArray from '../../../../utils/getKeyFromArray';
import ImageGallery from '../../../ImageGallery/ImageGallery';
import LinkOrNormalText from '../../../LinkOrNormalText';
type Props = {
    speed: number;
    typing: boolean;
    onEndTyping: () => void;
    msgId: number;
    history: boolean;
    listRef?: any;
} & React.PropsWithChildren<any>;

const styles = {
    link: {
        color: '#FF6A00',
    },
};

export const pic = {
    test: (str: string) => /!\[.*?]\((.*?)\)/.test(str),
    text2Obj: (str: string) => {
        const value = str.split(/!\[.*?]\((.*?)\)/).filter((v) => v);
        return { url: value[0] };
    },
};
export const link = {
    test: (str: string) => /\[.*?]\((.*?)\)/.test(str),
    text2Obj: (str: string) => {
        const value = str.split(/\[(.*?)]\((.*?)\)/).filter((v) => v);
        return { text: value[0], url: value[1] };
    },
};
export const renderMarkdownEle = (
    v,
    innerTyping = false,
    innerOnEndTyping,
    speed,
    msgId,
    history,
    listRef,
) => {
    if (!v) {
        return null;
    }
    const endTypingFn = innerTyping ? innerOnEndTyping : () => {};
    switch (v.type) {
        case 'imgList':
            return (
                <ImageGallery
                    key={getKeyFromArray(['img', ...v.url])}
                    images={v.url.map((val) => ({ image: val }))}
                    onEndTyping={endTypingFn}
                />
            );
        case 'link':
            return (
                <LinkOrNormalText
                    ref={listRef}
                    key={getKeyFromArray(['markdown', v.text, v.url])}
                    url={v.url}
                    style={[styles.link]}
                    msgId={msgId}
                    history={history}
                >
                    {v.text}
                </LinkOrNormalText>
            );
        case 'text':
            return (
                <LinkOrNormalText
                    ref={listRef}
                    key={getKeyFromArray(['markdown', v.text])}
                    msgId={msgId}
                >
                    {v.text}
                </LinkOrNormalText>
            );
        default:
            endTypingFn();
            return null;
    }
};
const splitPattern = /(!\[.*?]\(.*?\))|(\[.*?]\(.*?\))/;
const Markdown = ({
    children,
    msgId,
    history,
    speed,
    onEndTyping,
    typing,
    listRef,
}: Props) => {
    const [typingData, setTypingData] = useState([]);
    const [typedData, setTypedData] = useState([]);

    useEffect(() => {
        if (!children) {
            onEndTyping();
            return;
        }
        let res = [];
        if (pic.test(children) || link.test(children)) {
            const parts = children.split(splitPattern);
            res = parts
                .filter((v) => v && !/^\s*$/.test(v))
                .filter(Boolean)
                .map((p, index) => {
                    if (pic.test(p)) {
                        const { url } = pic.text2Obj(p);
                        return {
                            type: 'img',
                            url,
                            index,
                        };
                    }
                    if (link.test(p)) {
                        const { text, url } = link.text2Obj(p);
                        return {
                            type: 'link',
                            text,
                            url,
                            index,
                        };
                    }
                    return {
                        type: 'text',
                        text: p,
                        index,
                    };
                });
            const finalRes = [];
            res.forEach((v, i) => {
                const lastEle = finalRes[i - 1];
                if (v.type === 'img') {
                    if (lastEle?.type === 'imgList') {
                        lastEle.url.push(v.url);
                        return;
                    }
                    finalRes.push({
                        type: 'imgList',
                        url: [v.url],
                        index: v.index,
                    });
                    return;
                }
                finalRes.push(v);
            });
            res = finalRes;
        }
        if (!pic.test(children) && !link.test(children)) {
            res = [{ type: 'text', text: children }];
        }
        typing ? setTypingData(res) : setTypedData(res);
    }, [children]);
    const innerOnEndTyping = () => {
        setTypingData((pre) => {
            const [first, ...rest] = pre;
            setTypedData([...typedData, first]);
            if (rest.length === 0) {
                onEndTyping();
            }
            return rest;
        });
    };
    if (!children) {
        return null;
    }

    return (
        <>
            {[
                ...typedData.map((v) => {
                    return renderMarkdownEle(
                        v,
                        false,
                        innerOnEndTyping,
                        speed,
                        msgId,
                        history,
                        listRef,
                    );
                }),
                ...[typingData[0]].map((v) => {
                    return renderMarkdownEle(
                        v,
                        true,
                        innerOnEndTyping,
                        speed,
                        msgId,
                        history,
                        listRef,
                    );
                }),
            ]}
        </>
    );
};
export default Markdown;
