import { Text } from '@mrn/react-native';
import React from 'react';

import MessageLoading from './MessageLoading';
import SelectionMessage from './SelectionMessage';
import WelcomeSelectionMessage from './SelectionMessage/WelcomSelectionMessage';
import TextMessage from './TextMessage';
import { Message, MessageContentType, MessageStatus } from '../../../../types';
import { Message as MessageContent } from '../../../../types/message';
import isEmptyAnswer from '../../../../utils/isEmptyAnswer';

interface AnswerContent {
    data: Message;
    onTypedChange?: (v: MessageContent[]) => void;
}

const AnswerContent = (props: AnswerContent) => {
    if (
        [MessageStatus.STOPPED, MessageStatus.DONE_AFTER_STOP].includes(
            props.data.status,
        ) &&
        isEmptyAnswer(props.data)
    ) {
        return <Text>已取消会话</Text>;
    }
    if (
        props.data.status === MessageStatus.TO_GENERATE ||
        isEmptyAnswer(props.data)
    ) {
        return <MessageLoading />;
    }

    if (props.data.status === MessageStatus.ERROR || props.data.sensitive) {
        return (
            <TextMessage
                data={props.data}
                key={props.data.msgId}
                shouldTyping={false}
                onTypedChange={props.onTypedChange}
            />
        );
    }

    switch (props.data.msgType) {
        case MessageContentType.WELCOME:
            return <WelcomeSelectionMessage data={props.data} />;
        // 兼容性代码
        case MessageContentType.WITH_OPTIONS:
            return <SelectionMessage data={props.data} />;
        default:
            return (
                <TextMessage
                    data={props.data}
                    key={props.data.msgId}
                    onTypedChange={props.onTypedChange}
                />
            );
    }
};

export default AnswerContent;
