import { Platform } from '@mrn/react-native';

const INJECTED_JAVASCRIPT = `
(function () {
    let timer;
    const interval = (times = 10) => {
        let i = 0;
        timer && clearInterval(timer);
        timer = setInterval(() => {
            i++;
            if (i >= times) {
                clearInterval(timer);
            } else {
                window.MRNWebView.postMessage(
                    JSON.stringify({
                        type: 'heightChanged',
                        payload: document.body.scrollHeight,
                    }),
                );
            }
        }, 200);
    };

    // 监控到点击事件后则轮询body的scrollHeight，因为点击事件可能造成scrollHeight变化
    window.onload = () => {
        document.body.addEventListener('click', () => {
            interval();
        });
    };

    // 处理安卓和ios差异，安卓需使用document.addEventListener，ios需使用window.addEventListener
    ${Platform.select({
        ios: '',
        android:
            'window.addEventListener = (...p) => document.addEventListener(...p);',
    })}

    // 处理web和RN差异，值得注意的是window.ReactNativeWebView.postMessage接收多个参数会报错
    window.postMessage = (data) => {
        window.MRNWebView.postMessage(
            typeof data === 'string' ? data : JSON.stringify(data),
        );
    };

    // 处理interval事件，即轮询十次body的scrollHeight
    window.addEventListener('message', (e) => {
        try {
            const data = JSON.parse(e.data);
            if (data.type === 'interval') {
                interval();
            }
        } catch (error) {
            console.log(error);
        }
    });
    true;
})();
  `;

export default INJECTED_JAVASCRIPT;
