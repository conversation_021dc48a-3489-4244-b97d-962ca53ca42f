import KNB from '@mrn/mrn-knb';
import WebView from '@mrn/mrn-webview';
import React, { useEffect } from 'react';

import INJECTED_JAVASCRIPT from './hooks/injectedJavascript';
import useWebview from './hooks/useWebview';
import { WebviewMessage } from '../../../../../types/message';

export default ({ data }: { data: WebviewMessage }) => {
    const { ref, webViewHeight, onMessage } = useWebview();

    useEffect(() => {
        KNB.subscribe({
            action: 'ready',
            handle: () => {
                KNB.publish({ action: 'loadData' });
                ref.current.postMessage?.(JSON.stringify({ type: 'interval' }));
            },
        });
    }, []);

    return (
        <WebView
            ref={ref}
            sharedCookiesEnabled={true}
            injectedJavaScript={INJECTED_JAVASCRIPT}
            onMessage={onMessage}
            javaScriptEnabled={true}
            domStorageEnabled={true}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
            originWhitelist={['*']}
            mixedContentMode="always" // 启用混合内容模式
            key={`${data.type}-${data.insert.webview.url}`}
            source={{ uri: data.insert.webview.url }}
            containerStyle={{ flex: 1, height: Math.max(webViewHeight, 200) }}
        />
    );
};
