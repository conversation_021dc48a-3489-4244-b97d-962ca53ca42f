import React from 'react';

import { cast, renderEle } from './textMessageUtils';
import { MessageStatus } from '../../../../types';

const StandardQuestion = ({
    children,
    data,
    status,
    msgId,
    history,
    listRef,
}: {
    children: any[];
    status: MessageStatus;
    msgId: string;
    history: boolean;
    listRef: any;
    shouldTyping: boolean;
    data: any;
}) => {
    return (
        <>
            {cast(
                children.map(renderEle(data)),
                status,
                msgId,
                history,
                listRef,
            )}
        </>
    );
};
export default StandardQuestion;
