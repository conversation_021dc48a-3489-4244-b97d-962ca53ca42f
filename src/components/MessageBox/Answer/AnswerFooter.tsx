import { StyleSheet, Text, View } from '@mrn/react-native';
import React, { useEffect } from 'react';

import AnswerFeedback from './AnswerFeedback';
import useMessage from '../../../hooks/useMessage';
import { Message, MessageContentType, MessageStatus } from '../../../types';

const styles = StyleSheet.create({
    text: {
        fontSize: 12,
        color: '#666',
    },
    line: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    stop: {
        borderWidth: 1,
        borderColor: '#666',
        // padding: 4,
        borderRadius: 7.5,
        width: 13,
        height: 13,
        margin: 1.5,
        marginRight: 5.5,
        justifyContent: 'center',
        alignItems: 'center',
    },
    stopInner: {
        width: 5,
        height: 5,
        backgroundColor: '#666',
        borderRadius: 1.5,
    },
    stopText: {
        color: '#666',
        fontWeight: '500',
        fontSize: 12,
    },
    container: {
        paddingTop: 8,
        marginTop: 12,
        borderTopColor: '#eee',
        borderTopWidth: 0.5,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
});

interface AnswerFooter {
    data: Message;
}

const AnswerFooterContent = (props: AnswerFooter) => {
    switch (props.data.status) {
        case MessageStatus.DONE:
            return (
                <Text style={[styles.text, { color: '#999' }]}>
                    此条回答由AI生成
                </Text>
            );
        case MessageStatus.DONE_AFTER_STOP:
        case MessageStatus.STOPPED:
            return <Text style={styles.text}>回答已停止生成</Text>;
        case MessageStatus.ERROR:
            return <Text style={styles.text}>回答生成失败了</Text>;
        default:
            return null;
    }
};

const AnswerFooter = (props: AnswerFooter) => {
    const mutateMsg = useMessage((state) => state.mutateMsg);

    useEffect(() => {
        if (props.data.status !== MessageStatus.STOPPED) {
            return;
        }

        setTimeout(() => {
            // 3s后，变更显示状态
            mutateMsg(props.data.msgId, {
                status: MessageStatus.DONE_AFTER_STOP,
            });
        }, 3000);
    }, [props.data.status]);

    if (props.data.msgType === MessageContentType.WELCOME) {
        return null;
    }

    if (
        [
            MessageStatus.GENERATING,
            MessageStatus.TO_GENERATE,
            MessageStatus.TYPING,
        ].includes(props.data.status)
    ) {
        return null;
    }
    return (
        <View style={[styles.container]}>
            <AnswerFooterContent {...props} />

            <AnswerFeedback {...props} />
        </View>
    );
};

export default AnswerFooter;
