import { StyleSheet, Text, TouchableOpacity, View } from '@mrn/react-native';
import { LinearGradient } from '@mrn/react-native-linear-gradient';
import _ from 'lodash';
import React, { useState } from 'react';

import AnswerContent from './AnswerContent';
import { AnswerContext } from './AnswerContext';
import AnswerFooter from './AnswerFooter';
import useOptionPress from '../../../hooks/useOptionPress';
import { Message, MessageContentType } from '../../../types';
import { SuffixOptionsMessage } from '../../../types/message';
import getKeyFromArray from '../../../utils/getKeyFromArray';

import NetImages from '@/assets/images/homeRefactor';
import Condition from '@/components/Condition/Condition';
import RNImage from '@/components/RNImage';

const styles = StyleSheet.create({
    container: {
        marginBottom: 12,
    },
    messageContainer: {
        backgroundColor: '#fff',
        borderRadius: 10.5,
        paddingHorizontal: 16,
        paddingTop: 12,
        paddingBottom: 8,
        overflow: 'hidden',
    },
    text: {
        fontSize: 14,
        color: '#fff',
    },
    gradientOverlay: {
        position: 'absolute',
        height: 76,
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
    },
});

interface Answer {
    data: Message;
}

const EXTRA_STYLE = {
    [MessageContentType.WELCOME]: { flex: 1 },
    [MessageContentType.WITH_OPTIONS]: { minWidth: 230 },
};

const Answer = (props: Answer) => {
    const extraStyle = EXTRA_STYLE[props.data.msgType];
    const [typed, setTyped] = useState([]);
    const { onOptionPress } = useOptionPress(
        _.pick(props.data, ['msgType', 'msgId', 'history']),
    );
    const [withForm, setWithForm] = useState(false);
    return (
        <AnswerContext.Provider value={{ withForm, setWithForm }}>
            <View style={styles.container}>
                <View style={extraStyle}>
                    <View style={[styles.messageContainer]}>
                        <Condition condition={[withForm]}>
                            <LinearGradient
                                colors={['#FFFEF2', '#FFFFFF']}
                                locations={[0.06, 1]}
                                style={styles.gradientOverlay}
                            />
                        </Condition>
                        <Condition condition={[withForm]}>
                            <LinearGradient
                                colors={['#FFFFFF00', '#FFFFFF']}
                                locations={[0, 0.68]}
                                style={styles.gradientOverlay}
                            />
                        </Condition>

                        <View style={{ position: 'relative' }}>
                            <AnswerContent
                                {...props}
                                onTypedChange={setTyped}
                            />

                            {/*Footer，展示点赞按钮、会话状态等*/}
                            <AnswerFooter {...props} />
                        </View>
                    </View>
                </View>

                {/*猜你想问，因和整个消息体的UI在同一层级，所以需要提升进行渲染*/}
                {typed
                    .filter((v) => v?.type === 'suffixOptions')
                    .map((v: SuffixOptionsMessage) => {
                        const { descriptions, options } =
                            v.insert.suffixOptions;
                        return (
                            <View style={{ marginTop: 16 }}>
                                <Text
                                    style={{
                                        marginBottom: 8,
                                        color: '#3d3d3d',
                                        fontSize: 12,
                                    }}
                                >
                                    {descriptions}
                                </Text>
                                <View>
                                    {options.map((op) => {
                                        return (
                                            <TouchableOpacity
                                                key={getKeyFromArray([
                                                    'suffixOptionItem',
                                                    op.content,
                                                ])}
                                                style={{
                                                    backgroundColor: '#FAFAFB',
                                                    borderRadius: 12,
                                                    padding: 10,
                                                    marginBottom: 12,
                                                    flexDirection: 'row',
                                                    alignItems: 'center',
                                                    alignSelf: 'flex-start',
                                                }}
                                                onPress={() =>
                                                    onOptionPress(
                                                        op,
                                                        'floating_option_list',
                                                    )
                                                }
                                            >
                                                <Text
                                                    style={{
                                                        color: '#222',
                                                        fontSize: 12,
                                                    }}
                                                >
                                                    {op.content}
                                                </Text>
                                                <RNImage
                                                    source={
                                                        NetImages.arrowRight
                                                    }
                                                    style={{
                                                        width: 10,
                                                        marginLeft: 4,
                                                    }}
                                                />
                                            </TouchableOpacity>
                                        );
                                    })}
                                </View>
                            </View>
                        );
                    })}
            </View>
        </AnswerContext.Provider>
    );
};

export default Answer;
