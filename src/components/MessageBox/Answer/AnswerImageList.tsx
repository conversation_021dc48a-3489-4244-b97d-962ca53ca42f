import { StyleSheet } from '@mrn/react-native';
import React from 'react';

import useMessage from '../../../hooks/useMessage';
import { Message, MessageStatus } from '../../../types';
import ImageGallery from '../../ImageGallery/ImageGallery';

const styles = StyleSheet.create({
    images: {
        marginTop: 8,
    },
});

interface AnswerImageList {
    data: Message;
}

const AnswerImageList = (props: AnswerImageList) => {
    const delayScrollToEnd = useMessage((state) => state.delayScrollToEnd);
    // 生成完了再显示
    if (props.data.status !== MessageStatus.DONE || !props.data.imageList) {
        return null;
    }
    return (
        <ImageGallery
            style={styles.images}
            images={props.data.imageList.map((v) => ({ image: v }))}
            onEndTyping={delayScrollToEnd}
        />
    );
};

export default AnswerImageList;
