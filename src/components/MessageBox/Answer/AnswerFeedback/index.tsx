import { msi } from '@mfe/waimai-mfe-bee-common';
import {
    Keyboard,
    KeyboardAvoidingView,
    Platform,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from '@mrn/react-native';
import { Button, Icon, SlideModal, Toast } from '@roo/roo-rn';
import React, { useRef, useState } from 'react';

import AnswerFeedbackStatus from './AnswerFeedbackStatus';
import useCallerRequest from '../../../../hooks/useCallerRequest';
import useMessage from '../../../../hooks/useMessage';
import {
    FeedbackType,
    Message,
    MessageContentType,
    MessageStatus,
} from '../../../../types';

import getCopyData from '@/utils/message/getCopyData';

const styles = StyleSheet.create({
    container: {
        backgroundColor: '#fff',
        borderRadius: 6.5,
        paddingHorizontal: 10,
        alignSelf: 'flex-end',
    },
    line: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    gap: {
        marginHorizontal: 10,
        width: 0.5,
        height: 14,
    },
    text: {
        fontSize: 12,
        marginLeft: 4,
    },

    contentContainer: {
        backgroundColor: '#fff',
        borderTopLeftRadius: 10,
        borderTopRightRadius: 10,
    },
    contentHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginHorizontal: 16,
        alignItems: 'center',
        position: 'relative',
        height: 48,
    },
    contentTitleLabel: {
        fontSize: 16,
        color: '#222222',
        lineHeight: 22,
        paddingTop: 12,
        paddingBottom: 12,
        fontWeight: '500',
    },
    contentCloseLabel: {
        fontSize: 22,
        lineHeight: 22,
        color: '#aaa',
        fontWeight: '300',
    },
    contentBody: {
        marginHorizontal: 16,
        backgroundColor: '#f5f6fa',
        borderRadius: 10.5,
        paddingHorizontal: 12,
        paddingVertical: 14,
        overflow: 'hidden',
    },
    input: {
        height: 225,
        textAlignVertical: 'top',
        paddingTop: 0,
    },
});

const list = [FeedbackType.COPY, FeedbackType.LIKE, FeedbackType.DISLIKE];

interface AnswerFeedback {
    data: Message;
}

// 最新一条回答才有反馈
const AnswerFeedback = (props: AnswerFeedback) => {
    const messageList = useMessage((state) => state.messageList);
    const mutateMsg = useMessage((state) => state.mutateMsg);

    const timer = useRef<any>();
    const [content, setContent] = useState('');
    const [visible, setVisible] = useState(false);
    const [localType, setType] = useState<FeedbackType>(
        props.data.feedbackType,
    );

    const isLatest =
        !props.data.history &&
        props.data.msgId === messageList.slice(-1)[0]?.msgId;

    const callerRequest = useCallerRequest();
    const submitFeedback = async (type: FeedbackType) => {
        const res = await callerRequest.send(
            '/bee/v1/bdaiassistant/feedBackForChat',
            {
                chatRecordId: props.data.msgId,
                type,
                feedBackContent: content,
            },
        );

        if (res.code !== 0) {
            clearTimeout(timer.current);
            setType(undefined);
            return;
        }

        mutateMsg(props.data.msgId, { feedbackType: type });

        if (type === FeedbackType.LIKE) {
            return;
        }

        Toast.open('提交成功');
        setVisible(false);
    };

    const onFeedbackPress = (type: FeedbackType) => {
        if (type === FeedbackType.COPY) {
            msi.setClipboardData({
                data: getCopyData(props.data.currentContent), // 需要复制的数据
                _mt: {
                    sceneToken: 'bee-assistant-main', // 设置自己的隐私合规token
                },
                success: () => {
                    Toast.open('复制成功', {
                        duration: Toast.SHORT,
                    });
                },
                fail: () => {
                    Toast.open('复制内容为空', {
                        duration: Toast.SHORT,
                    });
                },
            });
            return;
        }
        // 不支持二次点击
        if (localType) {
            return;
        }
        setType(type);

        if (type === FeedbackType.LIKE) {
            return submitFeedback(type);
        }

        // FIX: 修复iOSmodal弹出时KeyboardAvoidingView不生效的问题
        if (Platform.OS === 'ios') {
            Keyboard.dismiss();
        }
        setVisible(true);
    };

    // 未完成的回答不展示
    if (
        (props.data.status !== MessageStatus.DONE &&
            props.data.status !== MessageStatus.DONE_AFTER_STOP) ||
        props.data.msgType === MessageContentType.WELCOME
    ) {
        return null;
    }
    let finalList = list;
    // 非最新回答不展示反馈
    if (!isLatest) {
        finalList = [FeedbackType.COPY];
    }
    // 有反馈结果的则展示
    if (localType || props.data.feedbackType) {
        finalList = [FeedbackType.COPY, localType || props.data.feedbackType];
    }

    return (
        <View style={[styles.container, styles.line]}>
            {finalList.map((it, i) => (
                <React.Fragment key={it}>
                    <TouchableOpacity
                        style={styles.line}
                        onPress={() => onFeedbackPress(it)}
                    >
                        <AnswerFeedbackStatus
                            type={it}
                            active={localType === it}
                        />
                    </TouchableOpacity>
                    {i !== finalList.length - 1 ? (
                        <View style={styles.gap} />
                    ) : null}
                </React.Fragment>
            ))}
            <KeyboardAvoidingView>
                <SlideModal
                    visible={visible}
                    useNativeDriver={true}
                    modalProps={{
                        maskClosable: true,
                        onBeforeClose: () => {
                            setVisible(false);
                            setContent('');
                            // 不重置localType，保持已选择的反馈状态
                        },
                    }}
                >
                    <View style={styles.contentContainer}>
                        <View
                            style={[
                                styles.contentHeader,
                                {
                                    justifyContent: 'center',
                                    position: 'relative',
                                },
                            ]}
                        >
                            <Text style={styles.contentTitleLabel}>
                                您的反馈将有助于小蜜助手的进步
                            </Text>
                            <TouchableOpacity
                                style={{
                                    backgroundColor: 'transparent',
                                    paddingHorizontal: 0,
                                    right: 0,
                                    position: 'absolute',
                                }}
                                onPress={() => {
                                    setVisible(false);
                                    // 用户取消反馈，恢复原状态
                                    setType(props.data.feedbackType);
                                }}
                            >
                                <Icon
                                    type="closed-o"
                                    size={16}
                                    style={{ tintColor: '#222' }}
                                />
                            </TouchableOpacity>
                        </View>
                        <View style={styles.contentBody}>
                            <TextInput
                                value={content}
                                onChangeText={setContent}
                                multiline
                                style={styles.input}
                                textAlignVertical="top"
                                placeholderTextColor={'#999'}
                                placeholder="请留下您的宝贵建议"
                            />
                        </View>
                        <Button
                            style={{
                                marginTop: 8,
                                marginBottom: 26,
                                marginHorizontal: 12,
                            }}
                            type="primary"
                            disabled={!content}
                            onPress={() => submitFeedback(FeedbackType.DISLIKE)}
                        >
                            提交
                        </Button>
                    </View>
                </SlideModal>
            </KeyboardAvoidingView>
        </View>
    );
};

export default AnswerFeedback;
