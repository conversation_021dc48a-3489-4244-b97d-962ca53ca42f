import { ImageStyle, StyleProp } from '@mrn/react-native';
import { Icon } from '@roo/roo-rn';
import React from 'react';

import { FeedbackType } from '../../../../types';

interface AnswerFeedbackStatus {
    type: FeedbackType;
    active?: boolean;
    style?: StyleProp<ImageStyle>;
}

const getIconType = (type: FeedbackType, active: boolean) => {
    switch (type) {
        case FeedbackType.LIKE:
            return active ? 'good-f' : 'good-o';
        case FeedbackType.DISLIKE:
            return active ? 'good-f' : 'bad-o'; // 没有bad-f，需要旋转替代
        case FeedbackType.COPY:
            return 'copy-o';
        default:
            return;
    }
};

const AnswerFeedbackStatus = (props: AnswerFeedbackStatus) => {
    const iconType = getIconType(props.type, props.active);

    if (props.active && props.type === FeedbackType.DISLIKE) {
        return (
            <Icon
                size={14}
                type="good-f"
                style={{
                    transform: [{ rotate: '180deg' }],
                }}
                tintColor="#222"
            />
        );
    }

    if (!iconType) {
        return null;
    }

    return (
        <Icon
            style={props.style}
            size={18}
            type={iconType}
            tintColor={props.active ? '#222' : '#999'}
        />
    );
};

export default AnswerFeedbackStatus;
