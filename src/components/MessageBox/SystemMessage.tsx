import { StyleSheet, Text, TouchableOpacity, View } from '@mrn/react-native';
import React from 'react';

import useMessage from '../../hooks/useMessage';
import { REFRESH_SYS_MESSAGE_ID } from '../../store/message';
import { Message } from '../../types';

interface SystemMessage {
    data: Message;
}

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        flex: 1,
        marginBottom: 12,
        paddingHorizontal: 16,
    },
    line: {
        backgroundColor: '#ccc',
        width: 25,
        height: 0.5,
    },
    text: {
        fontSize: 12,
        color: '#666',
        marginHorizontal: 12,
    },
});

const SystemMessage = (props: SystemMessage) => {
    const extraStyle =
        props.data.msgId === REFRESH_SYS_MESSAGE_ID
            ? { marginTop: 12 }
            : undefined;
    const getHistoryMessageList = useMessage(
        (state) => state.getHistoryMessageList,
    );
    return (
        <TouchableOpacity
            style={[styles.container, extraStyle]}
            onPress={() => {
                if (props.data.currentContent !== '下拉查看历史对话') {
                    return;
                }
                getHistoryMessageList();
            }}
        >
            <View style={styles.line} />
            <Text style={styles.text}>{props.data.currentContent}</Text>
            <View style={styles.line} />
        </TouchableOpacity>
    );
};

export default SystemMessage;
