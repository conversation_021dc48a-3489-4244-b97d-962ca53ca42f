import { Icon } from '@roo/roo-rn';
import { useControllableValue } from 'ahooks';
import React from 'react';
import { View, TouchableOpacity, Text } from 'react-native';

import Condition from '../Condition/Condition';

type Props = {
    value?: boolean;
    onChange?: (value: boolean) => void;
    extendButtonName: string;
};

const CollapsedButton = (props: Props) => {
    const [collapsed, setCollapsed] = useControllableValue(props);

    return (
        <View style={{ justifyContent: 'center', flexDirection: 'row' }}>
            <TouchableOpacity
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                onPress={() => setCollapsed(!collapsed)}
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center',
                }}
            >
                <Text style={{ color: '#FF6A00' }}>
                    {collapsed ? props.extendButtonName : '收起'}
                </Text>
                <Condition condition={[collapsed, !collapsed]}>
                    <Icon
                        type={'expand-less'}
                        tintColor={'#FF6A00'}
                        size={16}
                    />
                    <Icon
                        type={'expand-more'}
                        tintColor={'#FF6A00'}
                        size={16}
                    />
                </Condition>
            </TouchableOpacity>
        </View>
    );
};

export default CollapsedButton;
