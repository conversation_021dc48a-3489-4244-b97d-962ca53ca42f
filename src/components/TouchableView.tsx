import {
    LayoutAnimation,
    StyleProp,
    TouchableWithoutFeedback,
    View,
    ViewStyle,
} from '@mrn/react-native';
import { usePrevious } from 'ahooks';
import React, { useEffect, useState } from 'react';

import useLayout2 from '../hooks/useLayout2';
import { startAnimation } from '../utils/animation';
import getConditionStyle from '../utils/getConditionStyle';
import vibrate from '../utils/vibrate';
interface Props {
    moveX: number;
    moveY: number;
    style: StyleProp<ViewStyle>;
    activeStyle: StyleProp<ViewStyle>;
    children: ((active: boolean) => React.ReactNode) | React.ReactElement;
    leaved: boolean;
    setLeaved: (leaved: boolean) => void;
    onPressOut: () => void;
    onChange?: (v: boolean) => void;
    needVibrate?: boolean;
}
const TouchableView = ({
    moveX,
    moveY,
    style,
    activeStyle,
    leaved,
    setLeaved,
    onPressOut = () => {},
    children,
    onChange = () => {},
    needVibrate = false,
}: Partial<Props>) => {
    const { viewRef, layoutRef, onLayout } = useLayout2();
    const [active, setActive] = useState(false);
    useEffect(() => {
        if (!layoutRef.current) {
            return;
        }
        startAnimation(LayoutAnimation.Presets.easeInEaseOut);
        const { pageX, pageY, width, height } = layoutRef.current;
        if (
            moveX < pageX + width &&
            moveX > pageX &&
            moveY < pageY + height &&
            moveY > pageY
        ) {
            setActive(true);
            return;
        }
        setActive(false);
    }, [moveX, moveY, layoutRef.current]);

    const preActive = usePrevious(active);
    useEffect(() => {
        if (!preActive && active && needVibrate) {
            vibrate();
        }
        onChange(active);
    }, [active]);
    useEffect(() => {
        if (leaved && active) {
            setLeaved(false);
            onPressOut();
        }
    }, [leaved, setLeaved, active]);
    return (
        <TouchableWithoutFeedback onPress={onPressOut}>
            <View
                style={[style, getConditionStyle(activeStyle, active)]}
                ref={viewRef}
                onLayout={onLayout}
            >
                {typeof children === 'function' ? children(active) : null}
                {React.isValidElement(children) ? children : null}
            </View>
        </TouchableWithoutFeedback>
    );
};
export default TouchableView;
