import {
    Image,
    TextInput,
    ViewStyle,
    ImageStyle,
    View,
} from '@mrn/react-native';
import React from 'react';

import searchBlackImg from '../assets/images/poiSelector/search.png';
import TWS from '../TWS';

const SearchBar = ({
    onChange = () => {},
    placeholder = '请输入商家名字/ID',
    styles = {
        wrapper: {},
        icon: {},
        input: {},
    },
}: {
    onChange?: (text: string) => void;
    placeholder?: string;
    styles?: {
        wrapper: ViewStyle;
        icon: ImageStyle;
        input: ViewStyle;
    };
}) => {
    return (
        <View
            style={[
                TWS.row(),
                {
                    backgroundColor: '#F5F6FA',
                    borderRadius: 16,
                    height: 32,
                    alignItems: 'center',
                    marginTop: 11,
                    marginBottom: 18,
                },
                styles.wrapper || {},
            ]}
        >
            <Image
                source={searchBlackImg}
                style={[TWS.square(18), { marginLeft: 12 }, styles.icon || {}]}
            />
            <TextInput
                style={[
                    {
                        flex: 1,
                        height: 32,
                        fontSize: 14,
                        padding: 6,
                    },
                    styles.input || {},
                ]}
                placeholder={placeholder}
                onChangeText={onChange}
            />
        </View>
    );
};
export default SearchBar;
