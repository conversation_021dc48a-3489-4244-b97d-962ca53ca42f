import React, { PropsWithChildren, useEffect, useState } from 'react';
import { Image, ImageProps, ImageStyle, StyleProp } from 'react-native';

interface RNImageProps extends PropsWithChildren<ImageProps> {}

const RNImage = (
    props: RNImageProps & { source: string | ImageProps['source'] },
) => {
    let { style, source, ...restProps } = props;
    // 新增：自动处理source参数
    if (typeof source === 'string') {
        source = { uri: source };
    }
    const [originSize, setOriginSize] = useState<{
        width: number;
        height: number;
    } | null>(null);

    // 获取图片原始宽高
    useEffect(() => {
        let isMounted = true;
        if (typeof source === 'number') {
            // 本地图
            const resolved = Image.resolveAssetSource(source);
            if (resolved && resolved.width && resolved.height) {
                isMounted &&
                    setOriginSize({
                        width: resolved.width,
                        height: resolved.height,
                    });
            }
        } else if (source && 'uri' in source && source.uri) {
            // 网络图
            Image.getSize(
                source.uri,
                (width, height) => {
                    isMounted && setOriginSize({ width, height });
                },
                () => {
                    // 获取失败，忽略
                },
            );
        } else {
            setOriginSize(null);
        }
        return () => {
            isMounted = false;
        };
    }, [source]);

    // 解析外部 style
    const flattenStyle = Array.isArray(style)
        ? Object.assign({}, ...style)
        : style || {};
    const { width, height, ...restStyle } = flattenStyle as ImageStyle;

    let finalWidth = width;
    let finalHeight = height;
    if (originSize) {
        if (width && !height) {
            finalHeight =
                (width as number) * (originSize.height / originSize.width);
        } else if (!width && height) {
            finalWidth =
                (height as number) * (originSize.width / originSize.height);
        } else if (!width && !height) {
            finalWidth = originSize.width;
            finalHeight = originSize.height;
        }
    }

    const finalStyle: StyleProp<ImageStyle> = {
        ...restStyle,
        width: finalWidth,
        height: finalHeight,
    };

    return <Image {...restProps} source={source} style={finalStyle} />;
};

export default RNImage;
