import { openPage } from '@mfe/bee-foundation-utils';
import { TouchableOpacity, Text, View, StyleSheet } from '@mrn/react-native';
import { Toast } from '@roo/roo-rn';
import { useEffect } from 'react';
import React from 'react';

import { ButtonsMessage } from '../types/message';
interface Props {
    data: ButtonsMessage['insert']['buttons'];
    onEndTyping?: () => void;
}
const styles = StyleSheet.create({
    base: {
        paddingVertical: 10,
        paddingHorizontal: 10,
        borderRadius: 25,
        flex: 1,
        alignItems: 'center',
    },
    normal: {
        borderWidth: 1,
        borderColor: '#666',
    },
    primary: {
        backgroundColor: '#ffdd10',
    },
});
const Buttons = ({ data, onEndTyping = () => {} }: Props) => {
    useEffect(() => {
        onEndTyping();
    }, []);
    return (
        <View
            style={[
                {
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    marginVertical: 10,
                },
            ]}
        >
            {data.map((v) => {
                const { type = 'normal', url, color } = v;
                const bgColor =
                    color ?? type === 'primary' ? '#ffdd10' : '#f5f5fa';
                return (
                    <TouchableOpacity
                        onPress={() => {
                            // 预留字段，用来定义出跳链外的其他操作
                            // if(action) {
                            //
                            // }
                            if (url) {
                                openPage(url);
                                return;
                            }
                            Toast.open('缺少链接');
                        }}
                        style={[
                            styles.base,
                            type === 'primary' ? styles.primary : {},
                            type === 'normal' || type === undefined
                                ? styles.normal
                                : {},
                            {
                                backgroundColor: bgColor,
                            },
                        ]}
                    >
                        <Text
                            style={{
                                color: '#222',
                                fontSize: 14,
                                fontWeight: '500',
                            }}
                            numberOfLines={1}
                        >
                            {v.text}
                        </Text>
                    </TouchableOpacity>
                );
            })}
        </View>
    );
};
export default Buttons;
