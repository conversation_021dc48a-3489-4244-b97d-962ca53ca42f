import { ViewStyle } from '@mrn/react-native';

/**
 * AssistantEntry 组件属性类型定义
 */
export interface AssistantEntryProps {
    /** 控制组件显示/隐藏 */
    visible?: boolean;
    /** 提醒标题 */
    title?: string;
    /** 提醒消息内容（必填） */
    message: string;
    /** 操作按钮文字 */
    buttonText?: string;
    /** 按钮点击回调，不传则不显示按钮 */
    onSend?: () => void;
    /** 关闭按钮回调 */
    onClose?: () => void;
    /** 自定义容器样式 */
    style?: ViewStyle;
    sceneParams?: Record<'id' | string, string | number>;
    source?: string;
    extraParams?: Record<string, any>;
}

/**
 * 常用的预设配置类型
 */
export interface AssistantEntryPreset {
    /** 预设类型 */
    type: 'info' | 'warning' | 'success' | 'error';
    /** 预设标题 */
    title: string;
    /** 预设按钮文字 */
    buttonText: string;
    /** 预设颜色方案 */
    colors: {
        background: string;
        border: string;
        button: string;
    };
}

/**
 * 组件内部状态类型
 */
export interface AssistantEntryState {
    /** 是否正在执行动画 */
    animating?: boolean;
    /** 是否已关闭 */
    closed?: boolean;
}
