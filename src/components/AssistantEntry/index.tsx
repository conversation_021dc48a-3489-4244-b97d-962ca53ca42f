import {
    View,
    Text,
    TouchableOpacity,
    StyleSheet,
    ViewStyle,
    TextStyle,
    Platform,
} from '@mrn/react-native';
import { LinearGradient } from '@mrn/react-native-linear-gradient';
import { Icon } from '@roo/roo-rn';
import { useControllableValue, useDebounceFn } from 'ahooks';
import React from 'react';

import { AssistantEntryProps } from './types';
import NetImages from '../../assets/images/homeRefactor';
import TWS from '../../TWS';
import { SOURCE } from '../../types';
import openChat from '../../utils/openChat';
import RNImage from '../RNImage';

const MessageMap = {
    6: '想查询这个商家的点评评分吗？',
};

const AssistantEntry: React.FC<Partial<AssistantEntryProps>> = ({
    message,
    buttonText = '去查询',
    onSend,
    style,
    sceneParams,
    extraParams,
    source,
    ...rest
}) => {
    const [innerVisible, setVisible] = useControllableValue(rest, {
        defaultValue: true,
        valuePropName: 'visible',
        trigger: 'onClose',
    });
    // 防抖处理按钮点击
    const { run: handleButtonPress } = useDebounceFn(
        () => {
            onSend?.();
            const params = Object.keys(sceneParams ?? {})
                .map((v) => ({
                    [`aichat_scene_${v}`]: sceneParams?.[v],
                }))
                .reduce((pre, cur) => ({ ...pre, ...cur }), {});
            openChat(source as SOURCE, {
                ...params,
                ...(extraParams || {}),
            });
        },
        { wait: 200 },
    );

    // 防抖处理关闭操作
    const { run: handleClose } = useDebounceFn(
        () => {
            // 埋点记录关闭操作
            setVisible(false);
        },
        { wait: 200 },
    );

    if (!innerVisible) {
        return null;
    }

    const messageText = message || MessageMap[sceneParams?.id];
    return (
        <View style={[{ position: 'relative' }, style]}>
            <RNImage
                source={NetImages.miniIcon}
                style={{
                    width: 56,
                    zIndex: 10,
                    position: 'absolute',
                    top: -14,
                    left: 28,
                    ...Platform.select({
                        android: {
                            elevation: 10,
                        },
                    }),
                }}
            />
            <LinearGradient
                colors={['#BADCFF', '#C1AEFF']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={[styles.container]}
            >
                <LinearGradient
                    colors={['#BADCFF', '#fff']}
                    locations={[0, 0.6]}
                    style={{
                        width: '100%',
                        paddingHorizontal: 16,
                        paddingVertical: 10,
                        borderRadius: 12,
                    }}
                >
                    <View style={[styles.content]}>
                        {/* 左侧图标和标题 */}
                        <View style={[styles.leftSection]}>
                            <RNImage
                                source={NetImages.tips}
                                style={{
                                    height: 20,
                                }}
                            />
                        </View>

                        {/* 关闭按钮 */}
                        <TouchableOpacity
                            style={styles.closeButton}
                            onPress={handleClose}
                            hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
                        >
                            <Icon
                                type="closed-o"
                                size={14}
                                style={{ tintColor: '#999' }}
                            />
                        </TouchableOpacity>
                    </View>

                    <View
                        style={[
                            TWS.row(),
                            {
                                justifyContent: 'space-between',
                                alignItems: 'center',
                            },
                        ]}
                    >
                        {/* 提醒内容 */}
                        <Text style={styles.message}>{messageText}</Text>

                        {/* 操作按钮 */}
                        <TouchableOpacity
                            style={styles.actionButton}
                            onPress={handleButtonPress}
                            activeOpacity={0.8}
                        >
                            <Text style={styles.buttonText}>{buttonText}</Text>
                        </TouchableOpacity>
                    </View>
                </LinearGradient>
            </LinearGradient>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        borderRadius: 12,
        marginHorizontal: 16,
        padding: 1.5,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 3.84,
        elevation: 5,
    } as ViewStyle,
    content: {
        ...TWS.row(),
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: 8,
    } as ViewStyle,
    leftSection: {
        ...TWS.row(),
        alignItems: 'flex-end',
        flex: 1,
        marginLeft: 48,
    } as ViewStyle,
    title: {
        fontSize: 16,
        fontWeight: '700',
        color: '#222',
        fontStyle: 'italic',
    } as TextStyle,
    closeButton: {} as ViewStyle,
    message: {
        flex: 1,
        fontSize: 13,
        color: '#222',
        lineHeight: 20,
    } as TextStyle,
    actionButton: {
        backgroundColor: '#F0EBFF',
        borderRadius: 20,
        paddingHorizontal: 16,
        paddingVertical: 6,
        alignSelf: 'flex-end',
        alignItems: 'center',
        minWidth: 60,
    } as ViewStyle,
    buttonText: {
        color: '#4000FF',
        fontSize: 12,
        fontWeight: '500',
    } as TextStyle,
});

export default AssistantEntry;
export type { AssistantEntryProps } from './types';
