import { View, ScrollView, Text, StyleSheet } from '@mrn/react-native';
import { Toast, Button } from '@roo/roo-rn';
import React, { useState } from 'react';

import AssistantEntry from './index';
import TWS from '../../TWS';

/**
 * AssistantEntry 使用示例组件
 * 展示了各种不同的使用场景和配置方式
 */
const AssistantEntryExample: React.FC = () => {
    const [visible1, setVisible1] = useState(true);
    const [visible2, setVisible2] = useState(true);
    const [visible3, setVisible3] = useState(true);
    const [visible4, setVisible4] = useState(true);

    return (
        <ScrollView style={styles.container}>
            <Text style={styles.sectionTitle}>小蛋提醒组件示例</Text>

            {/* 基础用法 */}
            <View style={styles.section}>
                <Text style={styles.subtitle}>基础用法 - 资质驳回提醒</Text>
                <AssistantEntry
                    visible={visible1}
                    message="检测到当前商家申核资质被驳回，需重新提交资料"
                    onButtonPress={() => {
                        Toast.open('跳转到资料提交页面');
                        setVisible1(false);
                    }}
                    onClose={() => setVisible1(false)}
                />
                {!visible1 && (
                    <Button
                        style={styles.resetButton}
                        onPress={() => setVisible1(true)}
                    >
                        <Text>重新显示</Text>
                    </Button>
                )}
            </View>

            {/* 自定义标题和按钮 */}
            <View style={styles.section}>
                <Text style={styles.subtitle}>自定义配置 - 系统通知</Text>
                <AssistantEntry
                    visible={visible2}
                    title="系统通知"
                    message="您有新的订单需要处理，请及时查看并确认"
                    buttonText="立即查看"
                    onButtonPress={() => {
                        Toast.open('跳转到订单页面');
                        setVisible2(false);
                    }}
                    onClose={() => setVisible2(false)}
                    style={{
                        marginHorizontal: 20,
                        backgroundColor: '#FFF8E1',
                        borderColor: '#FFECB5',
                    }}
                />
                {!visible2 && (
                    <Button
                        style={styles.resetButton}
                        onPress={() => setVisible2(true)}
                    >
                        <Text>重新显示</Text>
                    </Button>
                )}
            </View>

            {/* 无操作按钮 */}
            <View style={styles.section}>
                <Text style={styles.subtitle}>仅提醒 - 无操作按钮</Text>
                <AssistantEntry
                    visible={visible3}
                    title="维护通知"
                    message="系统将于今晚23:00-01:00进行维护，届时部分功能可能受影响，请您提前做好准备"
                    onClose={() => setVisible3(false)}
                    // 不传 onButtonPress，则不显示操作按钮
                />
                {!visible3 && (
                    <Button
                        style={styles.resetButton}
                        onPress={() => setVisible3(true)}
                    >
                        <Text>重新显示</Text>
                    </Button>
                )}
            </View>

            {/* 长文本消息 */}
            <View style={styles.section}>
                <Text style={styles.subtitle}>长文本处理 - 自动截断</Text>
                <AssistantEntry
                    visible={visible4}
                    title="重要提醒"
                    message="亲爱的商家，根据平台最新政策调整，为了保障消费者权益和提升服务质量，我们将对商家资质认证流程进行优化升级。请您及时关注相关通知，确保店铺正常运营。如有疑问，请联系客服。"
                    buttonText="了解详情"
                    onButtonPress={() => {
                        Toast.open('查看详细政策说明');
                        setVisible4(false);
                    }}
                    onClose={() => setVisible4(false)}
                />
                {!visible4 && (
                    <Button
                        style={styles.resetButton}
                        onPress={() => setVisible4(true)}
                    >
                        <Text>重新显示</Text>
                    </Button>
                )}
            </View>

            {/* 使用说明 */}
            <View style={styles.section}>
                <Text style={styles.subtitle}>使用说明</Text>
                <View style={styles.infoBox}>
                    <Text style={styles.infoText}>
                        • visible: 控制显示/隐藏{'\n'}• title:
                        自定义标题（默认"小蛋提醒"）{'\n'}• message:
                        提醒内容（必填）{'\n'}• buttonText:
                        按钮文字（默认"去发送"）{'\n'}• onButtonPress:
                        按钮点击事件（可选）{'\n'}• onClose: 关闭事件（可选）
                        {'\n'}• style: 自定义样式（可选）
                    </Text>
                </View>
            </View>
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    container: {
        ...TWS.container('#f5f5f5'),
        paddingVertical: 20,
    },
    sectionTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#333',
        textAlign: 'center',
        marginBottom: 20,
    },
    section: {
        marginBottom: 24,
    },
    subtitle: {
        fontSize: 16,
        fontWeight: '600',
        color: '#666',
        marginBottom: 8,
        marginHorizontal: 16,
    },
    resetButton: {
        marginHorizontal: 16,
        marginTop: 8,
        backgroundColor: '#f0f0f0',
        borderRadius: 8,
    },
    infoBox: {
        backgroundColor: '#fff',
        marginHorizontal: 16,
        padding: 16,
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#e0e0e0',
    },
    infoText: {
        fontSize: 14,
        color: '#666',
        lineHeight: 20,
    },
});

export default AssistantEntryExample;
