# AssistantEntry 小蛋提醒组件

一个用于显示小蛋智能助手提醒信息的通知组件，支持自定义消息内容、操作按钮和关闭功能。

## 功能特性

- 🤖 集成小蛋机器人图标
- 📝 支持自定义提醒消息
- 🔘 可配置操作按钮
- ❌ 内置关闭功能
- 🎨 美观的卡片样式设计
- 📱 适配移动端体验

## 使用方式

### 基础用法

```tsx
import AssistantEntry from '@/components/AssistantEntry';
import { Toast } from '@roo/roo-rn';

const Demo = () => {
    const [visible, setVisible] = useState(true);

    return (
        <AssistantEntry
            visible={visible}
            message="检测到当前商家申核资质被驳回，需重新提交资料"
            onButtonPress={() => {
                // 处理按钮点击事件
                Toast.open('跳转到发送页面');
                setVisible(false);
            }}
            onClose={() => {
                // 处理关闭事件
                setVisible(false);
            }}
        />
    );
};
```

### 自定义配置

```tsx
<AssistantEntry
    visible={true}
    title="系统通知"
    message="您有新的待处理事项，请及时查看"
    buttonText="立即查看"
    onButtonPress={() => {
        // 自定义按钮行为
        navigation.navigate('TaskList');
    }}
    onClose={() => {
        // 关闭通知
        setNotificationVisible(false);
    }}
    style={{
        marginHorizontal: 20,
        marginTop: 16,
    }}
/>
```

### 仅显示提醒（无操作按钮）

```tsx
<AssistantEntry
    visible={true}
    message="系统维护中，部分功能可能受影响"
    onClose={() => setVisible(false)}
    // 不传入 onButtonPress，则不显示操作按钮
/>
```

## Props 参数

| 参数 | 类型 | 默认值 | 是否必填 | 说明 |
|------|------|--------|----------|------|
| `visible` | `boolean` | `true` | 否 | 控制组件显示/隐藏 |
| `title` | `string` | `'小蛋提醒'` | 否 | 提醒标题 |
| `message` | `string` | - | **是** | 提醒消息内容 |
| `buttonText` | `string` | `'去发送'` | 否 | 操作按钮文字 |
| `onButtonPress` | `() => void` | - | 否 | 按钮点击回调，不传则不显示按钮 |
| `onClose` | `() => void` | - | 否 | 关闭按钮回调 |
| `style` | `ViewStyle` | - | 否 | 自定义容器样式 |

## 设计说明

- **视觉设计**：采用淡蓝色背景(`#F0F8FF`)，营造温和的提醒氛围
- **布局结构**：顶部为标题栏(图标+标题+关闭按钮)，中部为消息内容，底部为操作按钮
- **交互体验**：按钮采用紫色主题色(`#7C3AED`)，符合品牌色调
- **兼容性**：支持iOS和Android平台的阴影效果

## 注意事项

1. `message` 参数为必填项，请确保传入有意义的提醒内容
2. 如果不需要操作按钮，可以不传 `onButtonPress` 参数
3. 建议搭配状态管理来控制组件的显示和隐藏
4. 可以通过 `style` 参数调整边距和布局位置

## 常见场景

- ✅ 系统通知提醒
- ✅ 资质审核状态提醒
- ✅ 业务流程引导
- ✅ 重要信息告知
- ✅ 功能更新通知