import { Image, Text, TouchableOpacity, View } from '@mrn/react-native';
import React from 'react';

import { TouchParams, VoiceProps } from './types';
import cancelImg from '../../assets/images/voice/cancel.png';
import checkImg from '../../assets/images/voice/check.png';
import closeImg from '../../assets/images/voice/close.png';
import closeActiveImg from '../../assets/images/voice/close_active.png';
import editImg from '../../assets/images/voice/edit.png';
import editActiveImg from '../../assets/images/voice/edit_active.png';
import TWS from '../../TWS';
import Condition from '../Condition/Condition';
import TouchableView from '../TouchableView';

const Buttons = ({
    editable,
    setEditable,
    touchParams,
    closeVoiceInput,
    onClosingStateChange,
    onEditingStateChange,
    setStopDefault,
    stop,
}: Pick<VoiceProps, 'closeVoiceInput' | 'setStopDefault' | 'stop'> & {
    editable: boolean;
    setEditable: (v: boolean) => void;
    touchParams: TouchParams;
    onClosingStateChange: (v: boolean) => void;
    onEditingStateChange: (v: boolean) => void;
}) => {
    const baseStyle = [
        TWS.circle(60),
        TWS.center(),
        { backgroundColor: '#666' },
    ];
    return (
        <View
            style={[
                TWS.row(),
                { justifyContent: 'space-between', paddingHorizontal: 44 },
            ]}
        >
            <Condition condition={[editable, !editable]}>
                <TouchableView
                    onPressOut={() => closeVoiceInput(false)}
                    style={[
                        ...baseStyle,
                        {
                            alignItems: 'center',
                            backgroundColor: 'rgba(0,0,0,0)',
                        },
                    ]}
                >
                    <>
                        <Image source={cancelImg} style={[TWS.square(17.5)]} />
                        <Text style={{ color: '#ccc' }}>取消</Text>
                    </>
                </TouchableView>
                <TouchableView
                    {...touchParams}
                    needVibrate={true}
                    onPressOut={() => {
                        setStopDefault(true);
                        closeVoiceInput(false);
                    }}
                    onChange={onClosingStateChange}
                    style={[...baseStyle]}
                    activeStyle={{ backgroundColor: '#fff' }}
                >
                    {(active) =>
                        active ? (
                            <Image
                                source={closeActiveImg}
                                style={[TWS.square(28)]}
                            />
                        ) : (
                            <Image source={closeImg} style={[TWS.square(28)]} />
                        )
                    }
                </TouchableView>
            </Condition>
            <TouchableView
                {...touchParams}
                needVibrate={true}
                onPressOut={() => {
                    setStopDefault(true);
                    stop();
                    setEditable(true);
                }}
                onChange={onEditingStateChange}
                style={[...baseStyle, editable && { backgroundColor: '#fff' }]}
                activeStyle={{ backgroundColor: '#fff' }}
            >
                {(active: boolean) => {
                    return (
                        <Condition condition={[editable, active, !active]}>
                            <TouchableOpacity
                                style={{
                                    flex: 1,
                                    width: '100%',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                }}
                                onPress={() => closeVoiceInput(true)}
                            >
                                <Image
                                    source={checkImg}
                                    style={[TWS.square(16.7)]}
                                />
                            </TouchableOpacity>

                            <Image
                                source={editActiveImg}
                                style={[TWS.square(16.7)]}
                            />
                            <Image
                                source={editImg}
                                style={[TWS.square(16.7)]}
                            />
                        </Condition>
                    );
                }}
            </TouchableView>
        </View>
    );
};
export default Buttons;
