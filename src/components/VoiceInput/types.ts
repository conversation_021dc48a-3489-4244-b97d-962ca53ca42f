import { StyleProp, ViewStyle } from '@mrn/react-native';

import useVoiceInput from '../../hooks/useVoiceInput';

export type Callback = <T>(params?: T) => void;
export interface Open {
    right: 'voiceS' | 'voiceL' | 'confirm';
    left: 'closeS' | 'closeL';
}

export interface TouchParams {
    leaved: boolean;
    setLeaved: (v: boolean) => void;
    moveX?: number;
    moveY?: number;
}

export type VoiceProps = { style: StyleProp<ViewStyle> } & ReturnType<
    typeof useVoiceInput
>;
