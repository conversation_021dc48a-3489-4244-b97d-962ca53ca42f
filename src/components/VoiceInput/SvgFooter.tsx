import { Dimensions, Image } from '@mrn/react-native';
import React from 'react';
import Svg, { Defs, Ellipse, LinearGradient, Stop } from 'react-native-svg';

import voiceImg from '../../assets/images/voice/voice.png';
import TWS from '../../TWS';

const SCREEN_WIDTH = Dimensions.get('window').width;

const SvgFooter = ({ active }) => {
    const inActiveColor = 'rgba(100,100,100,0.1)';
    return (
        <Svg height={100} width={SCREEN_WIDTH}>
            <Image
                source={voiceImg}
                style={[
                    TWS.square(24),
                    {
                        position: 'absolute',
                        zIndex: 1000,
                        top: 40,
                        left: SCREEN_WIDTH / 2 - 12,
                    },
                ]}
            />
            <Defs>
                <LinearGradient id="grad" x1="0" y1="0" x2="0" y2="1">
                    <Stop
                        offset="0"
                        stopColor={active ? '#E8E8E8' : inActiveColor}
                        stopOpacity="1"
                    />
                    <Stop
                        offset="1"
                        stopColor={
                            active ? 'rgba(149,149,149,0)' : inActiveColor
                        }
                        stopOpacity="1"
                    />
                </LinearGradient>
            </Defs>
            <Ellipse
                cx={SCREEN_WIDTH / 2}
                cy={100}
                rx={SCREEN_WIDTH / 2 + 20}
                ry={100}
                fill="url(#grad)"
            />
        </Svg>
    );
};
export default SvgFooter;
