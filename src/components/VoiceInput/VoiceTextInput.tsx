import {
    Animated,
    Dimensions,
    Image,
    LayoutAnimation,
    StyleProp,
    Text,
    TextInput,
    View,
    ViewStyle,
} from '@mrn/react-native';
import { useCountDown } from 'ahooks';
import React, { useEffect, useMemo, useRef } from 'react';
import Svg, { Polygon } from 'react-native-svg';

import { VoiceProps } from './types';
import VoiceWave from './VoiceWave';
import waveImg from '../../assets/images/voice/wave.png';
import { startAnimation } from '../../utils/animation';
import getConditionStyle from '../../utils/getConditionStyle';
import Condition from '../Condition/Condition';

const SCREEN_WIDTH = Dimensions.get('window').width;
const VoiceTextInput = ({
    style,
    text,
    editable,
    setText,
    volumes,
    closeVoiceInput,
    closing,
    editing,
}: {
    style?: StyleProp<ViewStyle>;
    editable: boolean;
    closing: boolean;
    editing: boolean;
} & Pick<VoiceProps, 'text' | 'setText' | 'volumes' | 'closeVoiceInput'>) => {
    const [countdown] = useCountDown({
        leftTime: 60 * 1000,
        onEnd: () => {
            if (!editable) {
                closeVoiceInput(true);
            }
        },
    });
    const seconds = parseInt(`${countdown / 1000}`, 10);
    const showCountdown = useMemo(
        () => !editable && seconds < 10,
        [seconds, editable],
    );

    const maxWidth = SCREEN_WIDTH * 0.8;
    const animWidth = useRef(new Animated.Value(132)).current;
    const maxSeconds = 10;
    const lastAnimation = useRef<Animated.CompositeAnimation>(null);
    useEffect(() => {
        const needMaxWidth = !(text || editing || editable || closing);
        const v = needMaxWidth
            ? Math.min(132 + ((60 - seconds) / maxSeconds) * maxWidth, maxWidth)
            : maxWidth;

        if (lastAnimation.current) {
            lastAnimation.current.stop();
        }
        const animation = Animated.timing(animWidth, {
            toValue: v,
            duration: needMaxWidth ? 200 : 1000,
            useNativeDriver: false,
        });
        lastAnimation.current = animation;
        animation.start();
    }, [text, seconds, editing, closing]);
    const triStyle = useMemo(() => {
        startAnimation({ ...LayoutAnimation.Presets.linear, duration: 1000 });
        if (editable || editing) {
            return {
                right: -(SCREEN_WIDTH / 2 - 74),
            };
        }
        if (closing) {
            return {
                left: -(SCREEN_WIDTH / 2 - 74),
            };
        }
        return {};
    }, [editable, closing, editing]);

    const inputRef = useRef<TextInput>();
    useEffect(() => {
        if (editable) {
            inputRef?.current.focus();
        }
    }, [editable]);
    return (
        <View
            style={[{ alignItems: 'center', justifyContent: 'center' }, style]}
        >
            <Condition condition={[showCountdown]}>
                <Text style={{ color: '#ccc', marginBottom: 20 }}>
                    {seconds}秒 后将停止录音
                </Text>
            </Condition>

            <Condition
                condition={[!text && !editable, !!text || editable]}
                style={
                    [
                        {
                            backgroundColor: '#ffdd10',
                            paddingHorizontal: 10,
                            minHeight: 63.5,
                            borderRadius: 10.5,
                            justifyContent: 'center',
                            alignItems: 'center',
                            minWidth: animWidth,
                            maxWidth,
                            opacity: 1,
                        },
                        getConditionStyle(
                            { backgroundColor: '#ff192d' },
                            closing,
                        ),
                    ] as StyleProp<ViewStyle>
                }
                Container={Animated.View}
            >
                <VoiceWave volumes={volumes} lines={10} />

                <View
                    style={{
                        width: '100%',
                        alignItems: 'flex-end',
                        paddingBottom: 10,
                    }}
                >
                    <TextInput
                        ref={inputRef}
                        value={text}
                        onChangeText={setText}
                        multiline={true}
                        editable={editable}
                        style={{ color: '#3D3D3D', width: maxWidth - 20 }}
                    />
                    <Condition condition={[!editable]}>
                        <Image
                            source={waveImg}
                            style={[{ width: 58, height: 10 }]}
                        />
                    </Condition>
                </View>
            </Condition>

            <View
                style={{
                    position: 'relative',
                    ...triStyle,
                }}
            >
                <Svg width={16} height={8}>
                    <Polygon
                        points="0,0 8,8 16,0"
                        fill={closing ? '#ff192d' : '#ffdd10'}
                    />
                </Svg>
            </View>
        </View>
    );
};
export default VoiceTextInput;
