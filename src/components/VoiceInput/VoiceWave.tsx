import { Animated, StyleProp, View, ViewStyle } from '@mrn/react-native';
import { useEffect } from 'react';
import React from 'react';

const styles = {
    line: {
        width: 2,
        backgroundColor: '#222',
        borderRadius: 1,
    },
};
const VoiceWaveLine = ({ volume, style }) => {
    const springValue = new Animated.Value(volume);
    const startSpringAnimation = () => {
        Animated.spring(springValue, {
            toValue: volume, // 动画结束时的值
            friction: 1, // 摩擦力（越小弹簧越有弹性）
            useNativeDriver: false, // 使用原生动画驱动
        }).start();
    };
    useEffect(() => {
        startSpringAnimation();
    }, [volume]);

    return (
        <Animated.View style={[styles.line, { height: springValue }, style]} />
    );
};

interface Props {
    volumes: number[];
    lines: number;
    style?: StyleProp<ViewStyle>;
}
const VoiceWave = ({ volumes, lines, style }: Props) => {
    const temp = volumes.map((v) => (v - 30) / 3 + 10).slice(0, lines);
    temp.length = lines;
    temp.fill(7, Math.min(volumes.length, lines), lines);
    const tempReverse = [...temp].reverse();
    const data = [...tempReverse, ...temp.slice(1)];
    return (
        <View style={[{ flexDirection: 'row', alignItems: 'center' }, style]}>
            {data.map((v, i) => (
                <VoiceWaveLine
                    key={i}
                    volume={v}
                    style={[
                        i === data.length - 1 ? undefined : { marginRight: 2 },
                    ]}
                />
            ))}
        </View>
    );
};

export default VoiceWave;
