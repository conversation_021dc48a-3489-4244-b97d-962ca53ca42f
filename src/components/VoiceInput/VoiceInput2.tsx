import {
    Dimensions,
    KeyboardAvoidingView,
    Platform,
    Text,
    View,
} from '@mrn/react-native';
import _ from 'lodash';
import React, { useState } from 'react';

import Buttons from './Buttons';
import SvgFooter from './SvgFooter';
import { VoiceProps, TouchParams } from './types';
import VoiceTextInput from './VoiceTextInput';
import useKeyboard from '../../hooks/useKeyboard';
import Condition from '../Condition/Condition';
import FadeInView from '../FadeInView';
import TouchableView from '../TouchableView';
const VoiceInput2 = (props: VoiceProps) => {
    const [editable, setEditable] = useState(false);
    const { style, closeVoiceInput, position, leaved, setLeaved } = props;
    const touchParams: TouchParams = {
        ...(position || {}),
        leaved,
        setLeaved,
    };
    const [closing, setClosing] = useState(false);
    const [editing, setEditing] = useState(false);
    const TheView = Platform.select({
        ios: KeyboardAvoidingView,
        android: View as unknown as typeof KeyboardAvoidingView,
    });
    const { isKeyboardShow } = useKeyboard();
    return (
        <TheView
            behavior={'padding'}
            style={[
                {
                    height: Dimensions.get('window').height,
                    width: Dimensions.get('window').width,
                    position: 'absolute',
                    left: 0,
                    bottom: 0,
                    backgroundColor: 'rgba(0,0,0,0.7)',
                    justifyContent: 'flex-end',
                    zIndex: props.voiceInputOpen ? 1000 : -1000,
                },
                style,
            ]}
        >
            <FadeInView visible={props.voiceInputOpen}>
                <VoiceTextInput
                    {..._.pick(props, [
                        'text',
                        'setText',
                        'volumes',
                        'closeVoiceInput',
                    ])}
                    closing={closing}
                    editing={editing}
                    style={{ marginBottom: isKeyboardShow ? 18 : 82 }}
                    editable={editable}
                />
                <Buttons
                    editable={editable}
                    setEditable={setEditable}
                    touchParams={touchParams}
                    onClosingStateChange={setClosing}
                    onEditingStateChange={setEditing}
                    {..._.pick(props, [
                        'closeVoiceInput',
                        'setStopDefault',
                        'stop',
                    ])}
                />

                <Condition
                    condition={[!isKeyboardShow || Platform.OS === 'android']}
                >
                    <TouchableView
                        {...touchParams}
                        onPressOut={() => closeVoiceInput(true)}
                    >
                        {(active) => {
                            return (
                                <>
                                    <View
                                        style={{
                                            alignItems: 'center',
                                            marginBottom: 11.5,
                                            height: 20,
                                        }}
                                    >
                                        <Condition condition={[active]}>
                                            <Text style={{ color: '#ccc' }}>
                                                松开发送
                                            </Text>
                                        </Condition>
                                    </View>
                                    <SvgFooter active={active} />
                                </>
                            );
                        }}
                    </TouchableView>
                </Condition>
            </FadeInView>
        </TheView>
    );
};
export default VoiceInput2;
