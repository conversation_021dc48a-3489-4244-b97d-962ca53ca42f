import { View, ViewProps } from '@mrn/react-native';
import { usePrevious } from 'ahooks';
import React, { createContext, PropsWithChildren, useState } from 'react';

export const LayoutContext = createContext({ width: 0, height: 0 });
const ViewWithLayout = (
    props: PropsWithChildren<ViewProps> & { needMemo?: boolean },
) => {
    const [layout, setLayout] = useState({ width: 0, height: 0 });
    const preLayout = usePrevious(layout);
    return (
        <View
            {...props}
            onLayout={(v) => {
                if (
                    !props.needMemo ||
                    preLayout?.width !== layout.width ||
                    preLayout?.height !== layout.height
                ) {
                    setLayout(v.nativeEvent.layout);
                }
                props.onLayout?.(v);
            }}
        >
            <LayoutContext.Provider value={layout}>
                {props.children}
            </LayoutContext.Provider>
        </View>
    );
};
export default ViewWithLayout;
