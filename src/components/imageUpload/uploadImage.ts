import { MSIUploader } from '@mfe/bee-foundation-utils';
import { msi } from '@mfe/waimai-mfe-bee-common';

import { getMSILocation } from '@/utils/getLocation';

interface Params {
    onSuccess: (data: any, index: number) => void;
    onUploadError?: () => void;
    onChooseError?: () => void;
    count?: number;
    sourceType?: ('album' | 'camera')[];
    onChooseSuccess?: (tempFilePaths: string[]) => void;
}

const uploadImage = ({
    onSuccess,
    onChooseSuccess,
    onUploadError = () => {},
    onChooseError = () => {},
    count = 1,
    sourceType = ['album'],
}: Params) => {
    msi.chooseImage({
        _mt: { needMediaLocation: true },
        count,
        sourceType,
        success: (res: any) => {
            if (!res.tempFilePaths.length) {
                return;
            }
            onChooseSuccess(res.tempFilePaths);
            MSIUploader.uploadVenus({
                forceProd: true,
                bucketName: 'bdaiassitant',
                appkey: 'com.sankuai.wmbdaiassistant.server',
                filePaths: res.tempFilePaths,
                success: async (data, index) => {
                    let draftMetaData = { ...res.tempFiles[index] };
                    if (draftMetaData.sourceType === 'camera') {
                        const location = await getMSILocation();
                        draftMetaData.longitude = location?.longitude;
                        draftMetaData.latitude = location?.latitude;
                    }

                    onSuccess(
                        {
                            src: data.data.originalLink,
                            metaData: draftMetaData,
                        },
                        index,
                    );
                },
                fail: () => {
                    onUploadError();
                },
            });
        },
        fail: () => {
            onChooseError();
        },
    });
};

export default uploadImage;
