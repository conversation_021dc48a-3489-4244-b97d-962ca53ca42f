import { Text, TextProps } from '@mrn/react-native';
import React, { useState, forwardRef } from 'react';

import useMessage from '../hooks/useMessage';
import useOpenLink from '../utils/openLink';

interface TypingText {
    text?: string;
    children?: string;
    style?: TextProps['style'];
    url?: string;
    msgId?: string;
    history?: boolean;
}

const LinkOrNormalText = forwardRef((props: TypingText) => {
    const { text: propText, children, url, msgId, history, style } = props;

    const [pressTimer, setPressTimer] = useState(null);

    const text = propText || children;

    const openLink = useOpenLink();
    const getSessionId = useMessage((state) => state.getSessionId);
    const onPress = () => {
        const timer = setTimeout(() => {
            if (!url) {
                return;
            }

            const sessionId = getSessionId();
            openLink(url, { msgId, history } as any, sessionId);
        }, 500);
        setPressTimer(timer);
    };

    return (
        <Text
            style={[url ? { color: '#FF6A00' } : undefined, style]}
            onPress={onPress}
            onLongPress={() => {
                clearTimeout(pressTimer);
            }}
        >
            {text}
        </Text>
    );
});

export default LinkOrNormalText;
