import { useRequest } from 'ahooks';

import useCallerRequest from '@/hooks/useCallerRequest';

const useSceneTips = () => {
    const callerRequest = useCallerRequest();
    const { data } = useRequest(
        async () => {
            const res = await callerRequest.get(
                '/bee/v2/bdaiassistant/scene/tips',
                {},
            );
            if (res.code === 0) {
                return res.data?.sceneTipsMap;
            }
            return {} as any;
        },
        { cacheKey: 'sceneTips' },
    );

    return {
        sceneTips: data,
    };
};

export default useSceneTips;
