import { Toast } from '@roo/roo-rn';
import { useRequest } from 'ahooks';
import { useEffect, useMemo } from 'react';

import useCallerRequest from '@/hooks/useCallerRequest';
import useMessage from '@/hooks/useMessage';
import { useSendMessage } from '@/hooks/useSendMessage';
import useTrace from '@/hooks/useTrace';
import { EntryPointType } from '@/types';
import useOpenLink from '@/utils/openLink';
import { trackEvent } from '@/utils/track';

export enum OperationType {
    JUMP = 1,
    SEND = 2,
}
export interface Options {
    operationType: OperationType;
    content: string;
    url?: string;
    abilityType: any;
    top?: boolean;
    link?: string; // logo
}

const useToolbar = () => {
    const { send } = useSendMessage();
    const callerRequest = useCallerRequest();

    const sessionId = useMessage((v) => v.sessionId);
    const {
        data: options,
        mutate,
        run,
    } = useRequest(
        async () => {
            const res = await callerRequest.get(
                '/bee/v1/bdaiassistant/common/getToolbarConfig',
                {},
            );
            if (res.code === 0) {
                return res.data?.options as Options[];
            }
            return [];
        },
        { cacheKey: 'toolbarConfig' },
    );

    useEffect(() => {
        if (!sessionId?.length) {
            return;
        }
        run();
    }, [sessionId.length]);

    const trace = useTrace();
    const traceToolbarClick = (item: Options) => {
        trace('toolbar', 'trigger', JSON.stringify(item));
    };

    const openLink = useOpenLink();
    const onAbilityPress = (item: Options) => {
        trackEvent('chat_ability', { content: item.content });
        traceToolbarClick(item);
        if (item.operationType === OperationType.JUMP) {
            openLink(item.url);
        } else if (item.operationType === OperationType.SEND) {
            send(
                { content: item.content, abilityType: item.abilityType },
                EntryPointType.TOOL,
            );
        }
    };

    const topToolbarOptions = useMemo(
        () => options?.filter((option) => option.top === true) || [],
        [options],
    );
    const normalToolbarOptions = useMemo(
        () => options?.filter((option) => option.top !== true) || [],
        [options],
    );
    const changeSort = async (draftOptions: Options[]) => {
        const originOptions = options;
        mutate([...topToolbarOptions, ...draftOptions]);
        const res = await callerRequest.post(
            '/bee/v2/bdaiassistant/common/toolbar/order',
            {
                toolNameList: draftOptions.map((v) => v.content),
            },
        );
        if (res.code !== 0) {
            Toast.open('操作失败');
            mutate(originOptions);
        }
    };

    return {
        options,
        topToolbarOptions,
        normalToolbarOptions,
        onAbilityPress,
        changeSort,
    };
};
export default useToolbar;
