// 工具栏
import {
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    Image,
    Keyboard,
    View,
} from '@mrn/react-native';
import React, { useMemo } from 'react';

import useSceneTips from './useSceneTips';
import useToolbar from './useToolbar';
import Announcement from '../Chat/ChatFooter/Announcement';
import RNImage from '../RNImage';

import NetImages from '@/assets/images/homeRefactor';
import useMessage from '@/hooks/useMessage';
import { useSendMessage } from '@/hooks/useSendMessage';
import TWS from '@/TWS';
import { EntryPointType } from '@/types';
import { getAdditionMessage } from '@/utils/message/getAdditionMessage';

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    item: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 5,
        paddingHorizontal: 12,
        marginBottom: 12,
        backgroundColor: '#fff',
        height: 36,
        borderRadius: 50,
    },
    dot: {
        width: 4,
        height: 4,
        borderRadius: 2,
        marginRight: 5,
    },
});

const AbilitySelector = () => {
    const { options, onAbilityPress } = useToolbar();

    const { sceneTips } = useSceneTips();

    const { send } = useSendMessage();

    const file = useMessage((state) => state.file);
    const clearFile = useMessage((state) => state.clearFile);
    const finalOptions = useMemo(() => {
        return file.length > 0
            ? sceneTips?.picture?.recommendedQuestions
            : options;
    }, [options, sceneTips, file.length]);

    const renderToolbarItem = (item, i) => (
        <TouchableOpacity
            style={[
                styles.item,
                { marginRight: i === options.length - 1 ? 0 : 10 },
            ]}
            key={item.content}
            onPress={() => onAbilityPress(item)}
        >
            {item?.link ? (
                <Image
                    source={{
                        uri: item.link,
                    }}
                    style={[TWS.square(16), { marginRight: 2 }]}
                />
            ) : null}
            <Text>{item.content}</Text>
        </TouchableOpacity>
    );

    const isWithFile = useMessage((state) => state.isWithFile);
    const renderSceneTipsItem = (item, i) => {
        return (
            <TouchableOpacity
                key={item}
                style={[
                    styles.item,
                    { marginRight: i === options.length - 1 ? 0 : 10 },
                    isWithFile() ? { backgroundColor: '#f5f6fa' } : {},
                ]}
                onPress={() => {
                    Keyboard.dismiss();
                    send(
                        getAdditionMessage(file, item),
                        EntryPointType.TOOL,
                        'picture_tip',
                    );
                    clearFile();
                }}
            >
                <Text style={{ fontSize: 12 }}>{item}</Text>
                <RNImage
                    source={NetImages.arrowRight}
                    style={[{ marginLeft: 4, width: 10 }]}
                />
            </TouchableOpacity>
        );
    };

    if (!finalOptions?.length) {
        return null;
    }
    return (
        <View style={{ position: 'relative', minHeight: 40 }}>
            <Announcement
                style={{
                    marginBottom: 10,
                    zIndex: 5,
                }}
            />
            <ScrollView
                horizontal
                contentContainerStyle={styles.container}
                showsHorizontalScrollIndicator={false}
                keyboardShouldPersistTaps="handled"
                style={{
                    position: 'absolute',
                    left: 0,
                    right: 0,
                    bottom: 0,
                }}
            >
                {finalOptions.map(
                    file.length > 0 ? renderSceneTipsItem : renderToolbarItem,
                )}
            </ScrollView>
        </View>
    );
};

export default AbilitySelector;
