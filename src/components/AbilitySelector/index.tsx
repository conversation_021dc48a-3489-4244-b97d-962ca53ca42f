// 工具栏
import {
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from '@mrn/react-native';
import React, { useMemo } from 'react';

import useSceneTips from './useSceneTips';
import useToolbar from './useToolbar';

import useMessage from '@/hooks/useMessage';
import { useSendMessage } from '@/hooks/useSendMessage';
import useTrace from '@/hooks/useTrace';
import { EntryPointType } from '@/types';
import { getAdditionMessage } from '@/utils/message/getAdditionMessage';

const styles = StyleSheet.create({
    container: {
        backgroundColor: '#fff',
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    item: {
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 0.5,
        borderColor: '#ddd',
        borderRadius: 6.5,
        paddingVertical: 5,
        paddingHorizontal: 12,
        marginBottom: 12,
    },
    dot: {
        width: 4,
        height: 4,
        borderRadius: 2,
        marginRight: 5,
    },
});

enum OperationType {
    JUMP = 1,
    SEND = 2,
}

const abilityColor = {
    // [AbillityType.GENERAL]: '#FF6A00',
    [OperationType.JUMP]: '#198CFF',
    [OperationType.SEND]: '#00BF7F',
};

const AbilitySelector = () => {
    const { options, onAbilityPress } = useToolbar();

    const { sceneTips } = useSceneTips();

    const { send } = useSendMessage();

    const file = useMessage((state) => state.file);
    const clearFile = useMessage((state) => state.clearFile);
    const finalOptions = useMemo(() => {
        return file.length > 0
            ? sceneTips?.picture?.recommendedQuestions
            : options;
    }, [options, sceneTips, file.length]);

    const trace = useTrace();

    const renderToolbarItem = (item, i) => (
        <TouchableOpacity
            style={[
                styles.item,
                { marginRight: i === options.length - 1 ? 0 : 10 },
            ]}
            key={item.content}
            onPress={() => onAbilityPress(item)}
        >
            <View
                style={[
                    styles.dot,
                    {
                        backgroundColor: abilityColor[item.operationType],
                    },
                ]}
            />
            <Text>{item.content}</Text>
        </TouchableOpacity>
    );

    const renderSceneTipsItem = (item, i) => {
        return (
            <TouchableOpacity
                key={item}
                style={[
                    styles.item,
                    { marginRight: i === options.length - 1 ? 0 : 10 },
                ]}
                onPress={() => {
                    send(
                        getAdditionMessage(file, item),
                        EntryPointType.TOOL,
                        'picture_tip',
                    );
                    trace(
                        'picture_tips',
                        'trigger',
                        JSON.stringify({ content: item }),
                    );
                    clearFile();
                }}
            >
                <Text>{item}</Text>
            </TouchableOpacity>
        );
    };

    if (!finalOptions?.length) {
        return null;
    }
    return (
        <ScrollView
            horizontal
            contentContainerStyle={styles.container}
            showsHorizontalScrollIndicator={false}
        >
            {finalOptions.map(
                file.length > 0 ? renderSceneTipsItem : renderToolbarItem,
            )}
        </ScrollView>
    );
};

export default AbilitySelector;
