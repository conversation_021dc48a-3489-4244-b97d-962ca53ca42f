import {
    Image,
    ImageProps,
    View,
    Modal,
    TouchableOpacity,
    ActivityIndicator,
} from '@mrn/react-native';
import React, { useState } from 'react';
import ImageViewer from 'react-native-image-zoom-viewer';

import defaultImg from '../assets/images/defaultImage.png';

interface ProgreesiveImage extends ImageProps {
    defaultImage?: any;
    preview?: boolean;
}

const ProgreesiveImage = (props: ProgreesiveImage) => {
    const [error, setError] = useState(false);
    const [modalVisible, setVisible] = useState(false);

    const closeModal = () => {
        setVisible(false);
    };

    if (props.preview && typeof props.source !== 'number') {
        const imageUrls = (
            props.source instanceof Array ? props.source : [props.source]
        ).map((s) => ({ url: s.uri }));

        return (
            <View>
                <TouchableOpacity onPress={() => setVisible(true)}>
                    <Image
                        {...props}
                        onError={() => setError(true)}
                        source={
                            error
                                ? props.defaultImage || defaultImg
                                : props.source
                        }
                    />
                </TouchableOpacity>
                <Modal
                    visible={modalVisible}
                    transparent
                    onRequestClose={closeModal}
                    onDismiss={closeModal}
                >
                    <ImageViewer
                        imageUrls={imageUrls}
                        onClick={closeModal}
                        loadingRender={() => (
                            <ActivityIndicator color="white" size="small" />
                        )}
                    />
                </Modal>
            </View>
        );
    }

    return (
        <Image
            {...props}
            onError={() => setError(true)}
            source={error ? props.defaultImage || defaultImg : props.source}
        />
    );
};

export default ProgreesiveImage;
