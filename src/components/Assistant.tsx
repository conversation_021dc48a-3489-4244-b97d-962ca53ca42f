import {
    Animated,
    LayoutAnimation,
    PanResponder,
    Platform,
    StyleSheet,
    TouchableOpacity,
    View,
    Dimensions,
} from '@mrn/react-native';
import { useDebounceFn, useGetState } from 'ahooks';
import _ from 'lodash';
import React, { useEffect, useRef, useState } from 'react';

import AssistantIcon from './AssistantIcon';
import useAssistantClose from '../hooks/useAssistantClose';
import { useAsyncStorage } from '../hooks/useAsyncStorage';
import useGrayInfo from '../hooks/useGrayInfo';
import { useLayout } from '../hooks/useLayout';
import { useUser } from '../store/user';
import { SOURCE } from '../types';
import { startAnimation } from '../utils/animation';
import openChat from '../utils/openChat';
import { track, trackEvent } from '../utils/track';

const { width: screenWidth, height: screenHeight } = Dimensions.get('screen');

interface Assistant {
    scrollY: Animated.Value;
    navigator: any;
}

const styles = StyleSheet.create({
    container: {
        position: 'absolute',
    },
    icon: {
        ...Platform.select({
            ios: {
                shadowColor: '#171717',
                shadowOffset: {
                    width: 0,
                    height: 2,
                },
                shadowOpacity: 0.4,
                shadowRadius: 8,
            },
            android: {
                elevation: 5,
                zIndex: 5,
                borderRadius: 55,
                backgroundColor: '#fff',
            },
        }),
    },
});

const Assistant = (props: Assistant) => {
    const guideStorage = useAsyncStorage<boolean>('20231231-guide');
    const { user } = useUser();
    const pan = useRef(new Animated.ValueXY()).current;
    const opacity = useRef(new Animated.Value(1)).current;
    const { layout, onLayout } = useLayout({ delay: 0 });
    const [visible, setVisible] = useState(false);
    const timer = useRef(null);
    const animationPlaying = useRef(false);
    const grayInfo = useGrayInfo();

    const [moving, setMoving] = useState(false);

    const handlePress = useDebounceFn(
        () => {
            trackEvent('icon_click', { misId: user.misId });
            openChat(SOURCE.home);
            return;
        },
        { wait: 200 },
    );

    const foldIcon = () => {
        // animation playing, 不必再次执行，避免打断动画
        if (animationPlaying.current) {
            return;
        }
        animationPlaying.current = true;
        const position = getPosition();
        Animated.spring(
            pan, // Auto-multiplexed
            {
                toValue: { x: 55 * (position.right ? 1 : -1), y: 0 },
                useNativeDriver: true,
            },
        ).start(() => (animationPlaying.current = false));
        Animated.spring(opacity, {
            toValue: 0.5,
            useNativeDriver: true,
        }).start();
    };

    const unfoldIcon = () => {
        Animated.spring(
            pan, // Auto-multiplexed
            { toValue: { x: 0, y: 0 }, useNativeDriver: true }, // Back to zero
        ).start();
        Animated.spring(opacity, {
            toValue: 1,
            useNativeDriver: true,
        }).start();
    };

    useEffect(() => {
        setVisible(grayInfo?.gray);
    }, [grayInfo]);

    useEffect(() => {
        if (!props.scrollY) {
            return;
        }

        props.scrollY.addListener(() => {
            foldIcon();

            clearTimeout(timer.current);
            // 滚动停止
            timer.current = setTimeout(() => {
                unfoldIcon();
            }, 2000);
        });

        return () => {
            clearTimeout(timer.current);
            props.scrollY.removeAllListeners();
        };
    }, [props.scrollY]);

    useEffect(() => {
        if (!visible) {
            return;
        }

        track('icon');
    }, [visible]);

    // const createGuide = useCreateGuide({ layout, guideStorage });
    // const createGuideOnce = useCallback(_.once(createGuide), []);
    useEffect(() => {
        // visible表示是否在灰度内，guideStorage.inited表示是否已经从SyncStorage中获取数据
        if (!visible || !guideStorage.inited) {
            return;
        }

        // 符合条件则打开弹窗，添加延时是因为首页可能会有请求报错，从而诱发roo-rn的bug
        if (!guideStorage.data && layout.offsetY) {
            // createGuideOnce({ layout });
        }
    }, [guideStorage.data, layout, visible, guideStorage.inited]);

    useAssistantClose(SOURCE.home);

    const [position, setPosition, getPosition] = useGetState<
        Partial<{
            top: number;
            left: number;
            right: number;
            bottom: number;
        }>
    >({ right: 20, bottom: 20 });
    const panResponder = useRef(
        PanResponder.create({
            onStartShouldSetPanResponder: () => {
                return true;
            },
            onMoveShouldSetPanResponder: () => {
                setMoving(true);
                return true;
            },
            onPanResponderMove: (_, gestureState) => {
                const { dx, dy } = gestureState;
                if (Math.abs(dx) < 10 && Math.abs(dy) < 10) {
                    return;
                }
                setPosition({
                    top: gestureState.moveY,
                    left: gestureState.moveX,
                });
                startAnimation();
            },
            onPanResponderRelease: (_, gestureState) => {
                setMoving(false);
                const position = getPosition();
                const { dx, dy } = gestureState;
                if (Math.abs(dx) < 10 && Math.abs(dy) < 10) {
                    // 如果移动距离很小，则认为是点击而非拖动
                    return handlePress.run();
                }

                if (gestureState.moveX > screenWidth / 2) {
                    setPosition({
                        right: 20,
                        top: position.top,
                    });
                } else {
                    setPosition({
                        left: 20,
                        top: position.top,
                    });
                }
                startAnimation(LayoutAnimation.Presets.spring);
            },
        }),
    ).current;

    // 如果图标过上或者过下则重新布局
    useEffect(() => {
        if (moving || !layout.pageY) {
            return;
        }
        if (layout.pageY < 100) {
            setPosition({
                ..._.pick(position, ['right', 'left']),
                top: 140,
            });
            startAnimation(LayoutAnimation.Presets.spring);
        }
        if (layout.pageY > screenHeight - 100) {
            setPosition({
                ..._.pick(position, ['right', 'left']),
                bottom: 20,
            });
            startAnimation(LayoutAnimation.Presets.spring);
        }
    }, [layout]);

    // 暂时屏蔽
    if (!visible) {
        return null;
    }

    return (
        <View
            style={[styles.container, position]}
            onLayout={onLayout}
            {...panResponder.panHandlers}
        >
            {/* 引导对齐有问题，在安卓下纵向有偏移，不知道原因 */}
            {visible ? (
                <Animated.View
                    style={{
                        transform: [
                            { translateX: pan.x },
                            { translateY: pan.y },
                        ],
                        opacity: opacity,
                    }}
                >
                    <TouchableOpacity
                        style={styles.icon}
                        onPress={handlePress.run}
                    >
                        <AssistantIcon />
                    </TouchableOpacity>
                </Animated.View>
            ) : null}
        </View>
    );
};

export default Assistant;
