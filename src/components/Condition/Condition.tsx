import React from 'react';
type Props<T extends React.ElementType> = {
    condition: boolean[];
    children: T;
    Container?: React.ElementType;
} & React.PropsWithChildren<React.ComponentProps<T>>;

const Condition = <T extends React.ElementType>({
    condition,
    children,
    Container,
    ...restProps
}: Props<T>) => {
    children = Array.isArray(children) ? children : [children];
    const ele = children.find((_, i) => {
        return condition[i];
    });
    if (Container) {
        return ele ? <Container {...restProps}>{ele}</Container> : null;
    }
    return React.isValidElement(ele)
        ? React.cloneElement(ele, {
              ...restProps,
              ...(ele?.props || ({} as any)),
              style: [(ele?.props as any)?.style, restProps?.style],
          })
        : null;
};
export default Condition;
