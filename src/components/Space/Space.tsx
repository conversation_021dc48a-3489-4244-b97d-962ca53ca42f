import { View } from '@mrn/react-native';
import React from 'react';

import getConditionStyle from '../../utils/getConditionStyle';

const Space = ({
    children,
    row,
    space = 10,
    style = [],
    sideSpace,
    ...rest
}) => {
    sideSpace = sideSpace ?? space;
    return (
        <View
            style={[getConditionStyle({ flexDirection: 'row' }, row), ...style]}
            {...rest}
        >
            {children.map((v, i) => {
                return React.cloneElement(v, {
                    key: `space_item_${i}`,
                    style: [
                        v.props.style,
                        getConditionStyle({ marginRight: space }, row, {
                            marginBottom: space,
                        }),
                        getConditionStyle(
                            { marginLeft: sideSpace },
                            i === 0 && row,
                        ),
                        getConditionStyle(
                            { marginTop: sideSpace },
                            i === 0 && !row,
                        ),
                        getConditionStyle(
                            { marginRight: sideSpace },
                            i === children.length - 1 && row,
                        ),
                        getConditionStyle(
                            { marginBottom: sideSpace },
                            i === children.length - 1 && !row,
                        ),
                    ],
                });
            })}
        </View>
    );
};
export default Space;
