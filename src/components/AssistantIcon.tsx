import { Image, ImageProps, ImageStyle } from '@mrn/react-native';
import React from 'react';

import robotImg from '../assets/images/icon.gif';
import robotImgWithoutText from '../assets/images/iconWithoutText.gif';

import NetImages from '@/assets/images/homeRefactor';
import useInteractionGray from '@/pages/chat/hooks/useInteractionGray';

interface AssistantIcon extends Omit<ImageProps, 'source'> {
    style?: ImageStyle;
    size?: number;
    noText?: boolean;
}

const AssistantIcon = (props: AssistantIcon) => {
    const { style, size = 55, ...rest } = props;
    const { isGray } = useInteractionGray();
    if (isGray) {
        return (
            <Image
                {...rest}
                source={{
                    uri: props.noText
                        ? NetImages.defaultIconWithoutSpace
                        : NetImages.defaultGifIcon,
                }}
                style={[{ height: size, width: size }, style]}
            />
        );
    }
    return (
        <Image
            {...rest}
            source={props.noText ? robotImgWithoutText : robotImg}
            style={[{ height: size, width: size }, style]}
        />
    );
};

export default AssistantIcon;
