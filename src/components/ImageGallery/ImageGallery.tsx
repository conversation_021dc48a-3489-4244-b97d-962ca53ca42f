import { View, StyleSheet, ViewStyle } from '@mrn/react-native';
import React, { useEffect } from 'react';

import MediaItem from './MediaItem';

const styles = StyleSheet.create({
    container: {
        flex: 1,
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'flex-start',
        marginRight: -8,
        marginBottom: -8,
    },
});

interface ImageGallery {
    images: ({ video: string } | { image: string })[];
    style?: ViewStyle;
    onEndTyping?: () => void;
}

const ImageGallery = (props: ImageGallery) => {
    useEffect(() => {
        props.onEndTyping?.();
    }, []);

    const { images = [] } = props;

    return (
        <View style={[styles.container, props.style]}>
            {images.map((pic) => (
                <MediaItem
                    media={pic}
                    medias={images}
                    key={JSON.stringify(pic)}
                />
            ))}
        </View>
    );
};

export default ImageGallery;
