import { Animated } from '@mrn/react-native';
import { usePrevious } from 'ahooks';
import React, { useEffect, useRef, useState } from 'react';

import Condition from './Condition/Condition';

const DURATION = 300;
const FadeInView = ({ style = {}, children = null, visible }) => {
    const fadeAnim = useRef(new Animated.Value(0)).current; // 初始透明度为0
    const [innerVisible, setInnerVisible] = useState(false);
    const zIndexAnim = useRef(new Animated.Value(-1000)).current;
    const preVisible = usePrevious(visible);
    const fadeIn = () => {
        zIndexAnim.setValue(1000);
        setInnerVisible(true);
        Animated.timing(fadeAnim, {
            toValue: 1, // 最终透明度为1
            duration: DURATION, // 动画持续时间
            useNativeDriver: true, // 使用原生驱动
        }).start();
    };
    const fadeOut = () => {
        Animated.timing(fadeAnim, {
            toValue: 0, // 最终透明度为0
            duration: DURATION, // 动画持续时间
            useNativeDriver: true, // 使用原生驱动
        }).start(() => {
            setInnerVisible(false);
        });
    };

    useEffect(() => {
        if (!preVisible && visible) {
            fadeIn();
            return;
        }
        if (preVisible && !visible) {
            fadeOut();
            return;
        }
    }, [visible]);

    return (
        <Condition condition={[innerVisible]}>
            <Animated.View // 特殊的可动画化视图
                style={[
                    style,
                    { opacity: fadeAnim },
                    innerVisible
                        ? {
                              zIndex: 10001,
                          }
                        : {
                              zIndex: -1000,
                          },
                ]}
            >
                {children}
            </Animated.View>
        </Condition>
    );
};
export default FadeInView;
