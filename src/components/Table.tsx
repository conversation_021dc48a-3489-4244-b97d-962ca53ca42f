import {
    FlatList,
    View,
    Text,
    TouchableOpacity,
    ViewStyle,
} from '@mrn/react-native';
import { Icon } from '@roo/roo-rn';
import { useToggle } from 'ahooks';
import React from 'react';

import Condition from './Condition/Condition';
import ViewWithLayout, { LayoutContext } from './ViewWithLayout/ViewWithLayout';
import TWS from '../TWS';
import { Table as TableMessage } from '../types/message';

const TableItem = ({ item, index, layout, keys, columNum }) => {
    return (
        <View
            style={[
                TWS.row(),
                index === 0
                    ? {
                          backgroundColor: '#f5f6fa',
                          borderTopLeftRadius: 6.5,
                          borderTopRightRadius: 6.5,
                      }
                    : {
                          borderTopWidth: 0.5,
                          borderColor: '#eee',
                      },
            ]}
        >
            {keys.map((k, ci) => {
                return (
                    <View
                        key={`${index}-${k}`}
                        style={[
                            ci === 0
                                ? undefined
                                : {
                                      borderColor: '#eee',
                                      borderLeftWidth: 0.5,
                                  },
                        ]}
                    >
                        <Text
                            style={[
                                {
                                    width: layout.width / columNum,

                                    paddingVertical: 6,
                                    paddingHorizontal: 12,
                                    color: '#222',
                                    fontSize: 11,
                                },
                                index === 0
                                    ? {
                                          fontWeight: '500',
                                      }
                                    : undefined,
                            ]}
                        >
                            {item[k]}
                        </Text>
                    </View>
                );
            })}
        </View>
    );
};

const Table = ({
    data,
    columns,
    showCollapse,
    collapseDesc,
    comment,
    collapseState,
    style,
}: TableMessage['insert']['table'] & { style: ViewStyle }) => {
    const [showTable, { toggle }] = useToggle(
        showCollapse ? collapseState : true,
    );

    const keys = columns.map((v) => v.dataIndex);
    const finalData = [
        columns
            .map((v) => ({ [v.dataIndex]: v.title }))
            .reduce((pre, cur) => ({ ...pre, ...cur }), {}),
        ...data,
    ];
    const columNum = columns.length;

    return (
        <View style={style}>
            <Condition condition={[showCollapse]}>
                <TouchableOpacity
                    onPress={toggle}
                    style={[
                        TWS.row(),
                        { alignItems: 'center', marginBottom: 6 },
                    ]}
                >
                    <Text style={{ fontSize: 12, color: '#222' }}>
                        {collapseDesc}
                    </Text>
                    {!showTable ? (
                        <Icon
                            type={'expand-less'}
                            tintColor={'#222'}
                            size={14}
                        />
                    ) : (
                        <Icon
                            type={'expand-more'}
                            tintColor={'#222'}
                            size={14}
                        />
                    )}
                </TouchableOpacity>
            </Condition>
            <Condition condition={[comment]}>
                <Text style={{ fontSize: 12, color: '#666', marginBottom: 4 }}>
                    {comment}
                </Text>
            </Condition>
            <Condition condition={[showTable]}>
                <ViewWithLayout>
                    <LayoutContext.Consumer>
                        {(layout) => (
                            <FlatList
                                data={finalData}
                                style={{
                                    borderRadius: 6.5,
                                    borderWidth: 0.5,
                                    borderColor: '#eee',
                                }}
                                keyExtractor={(item, index) => `${index}`}
                                renderItem={({ index, item }) => (
                                    <TableItem
                                        index={index}
                                        item={item}
                                        layout={layout}
                                        keys={keys}
                                        columNum={columNum}
                                    />
                                )}
                            />
                        )}
                    </LayoutContext.Consumer>
                </ViewWithLayout>
            </Condition>
        </View>
    );
};
export default Table;
