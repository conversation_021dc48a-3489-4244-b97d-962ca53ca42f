import { View, Text, TouchableOpacity, StyleSheet } from '@mrn/react-native';
import { Icon } from '@roo/roo-rn';
import React, { useState } from 'react';

import useMessage from '../../../hooks/useMessage';
import useOpenLink from '../../../utils/openLink';

import NetImages from '@/assets/images/homeRefactor';
import Condition from '@/components/Condition/Condition';
import RNImage from '@/components/RNImage';

interface ReferenceDocProps {
    title: string;
    list: Array<{
        type: 'km' | 'meituan';
        text: string;
        link: string;
    }>;
}

const styles = StyleSheet.create({
    container: {
        backgroundColor: '#fff',
        borderRadius: 10,
        padding: 8,
        marginTop: 8,
        borderWidth: 1,
        borderColor: '#eee',
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 12,
    },
    headerCollapsed: {
        marginBottom: 0,
    },
    headerTitle: {
        fontSize: 12,
        color: '#222',
    },
    headerRight: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    headerRightText: {
        fontSize: 12,
        color: '#666',
        marginRight: 4,
    },
    listItem: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 8,
    },
    itemIndex: {
        fontSize: 11,
        color: '#222',
        minWidth: 22,
        marginRight: 2,
    },
    itemContent: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
    },
    itemText: {
        fontSize: 12,
        marginLeft: 4,
        flex: 1,
        color: '#222',
    },
    iconText: {
        fontSize: 12,
        color: '#3366CC',
        marginRight: 4,
    },
});

const ReferenceDoc = ({ title, list }: ReferenceDocProps) => {
    const [isExpanded, setIsExpanded] = useState(false);
    const openLink = useOpenLink();
    const getSessionId = useMessage((state) => state.getSessionId);

    const handleItemPress = (link: string) => {
        const sessionId = getSessionId();
        openLink(link, {} as any, sessionId);
    };

    const toggleExpanded = () => {
        setIsExpanded(!isExpanded);
    };

    return (
        <View style={styles.container}>
            <View
                style={[styles.header, !isExpanded && styles.headerCollapsed]}
            >
                <Text style={styles.headerTitle}>{title}</Text>
                <TouchableOpacity
                    style={styles.headerRight}
                    onPress={toggleExpanded}
                >
                    <RNImage
                        source={NetImages.kmWithoutTextIcon}
                        style={{ width: 13, marginRight: 4 }}
                    />
                    <Text
                        style={{
                            fontSize: 11,
                            color: '#3d3d3d',
                            fontWeight: 'bold',
                        }}
                    >
                        学城
                    </Text>
                    <Icon
                        type={isExpanded ? 'expand-more' : 'expand-less'}
                        size={18}
                        tintColor="#999"
                    />
                </TouchableOpacity>
            </View>

            {isExpanded &&
                list.map((item, index) => (
                    <TouchableOpacity
                        key={index}
                        style={styles.listItem}
                        onPress={() => handleItemPress(item.link)}
                    >
                        <Text style={styles.itemIndex}>{index + 1}.</Text>
                        <View style={styles.itemContent}>
                            <Condition condition={[item.type === 'km']}>
                                <RNImage
                                    source={NetImages.kmWithoutTextIcon}
                                    style={{ width: 13 }}
                                />
                            </Condition>

                            <Text
                                style={styles.itemText}
                                numberOfLines={1}
                                ellipsizeMode="tail"
                            >
                                {item.text}
                            </Text>
                        </View>
                    </TouchableOpacity>
                ))}
        </View>
    );
};

export default ReferenceDoc;
