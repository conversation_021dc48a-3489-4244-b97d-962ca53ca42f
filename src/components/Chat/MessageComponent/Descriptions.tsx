import React from 'react';
import { StyleSheet, View } from 'react-native';

import RNText from '@/components/RNText';
import { DescriptionsMessage } from '@/types/message';

const styles = StyleSheet.create({
    text: {
        fontSize: 14,
        color: '#222',
        height: 47.5,
    },
});

const Descriptions = ({
    list,
}: DescriptionsMessage['insert']['descriptions']) => {
    return (
        <>
            {list.map((item, index) => (
                <View
                    key={index}
                    style={{
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                    }}
                >
                    <RNText style={[styles.text, { width: '20%' }]}>
                        {item.label}
                    </RNText>
                    <RNText
                        style={[styles.text, { maxWidth: '70%' }]}
                        numberOfLines={1}
                    >
                        {item.value}
                    </RNText>
                </View>
            ))}
        </>
    );
};

export default Descriptions;
