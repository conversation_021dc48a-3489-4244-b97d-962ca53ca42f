import React, { useEffect, useState, useRef } from 'react';
import { LayoutAnimation, View } from 'react-native';

import CollapsedButton from '@/components/Base/CollapsedButton';
import Condition from '@/components/Condition/Condition';
import ImageGallery from '@/components/ImageGallery/ImageGallery';
import LinkOrNormalText from '@/components/LinkOrNormalText';
import RNText from '@/components/RNText';
import { CollapsibleTextMessage, LinkMessage } from '@/types/message';
import { startAnimation } from '@/utils/animation';

const CollapsibleText = (
    props: CollapsibleTextMessage['insert']['collapsibleText'] & {
        msgId: string;
        history: boolean;
    },
) => {
    const [collapsed, setCollapsed] = useState(true);
    const [isOverflow, setIsOverflow] = useState(false);
    const heightChecked = useRef(false);

    useEffect(() => {
        startAnimation({
            ...LayoutAnimation.Presets.linear,
            duration: 100,
        });
    }, [collapsed]);

    const maxHeight = props.maxHeight || 200;

    const getHeight = () => {
        if (!props.extendButtonName || !isOverflow) {
            return undefined;
        }
        if (collapsed) {
            return maxHeight;
        }
        return undefined;
    };

    const isImageOrVideo = (item: any) =>
        ['image', 'video'].includes(item?.type);
    const isLinkOrText = (item: any) => ['link', 'text'].includes(item?.type);
    const castContent = (originContent: any[]) => {
        const content = [];
        originContent.forEach((item) => {
            const lastEle: any[] = content[content.length - 1];
            if (
                (isLinkOrText(item) && isLinkOrText(lastEle?.[0])) ||
                (isImageOrVideo(item) && isImageOrVideo(lastEle?.[0]))
            ) {
                lastEle.push(item);
            } else {
                content.push([item]);
            }
        });
        return content;
    };
    const content = castContent(props.content);

    return (
        <View>
            <View
                onLayout={(e) => {
                    if (heightChecked.current) {
                        return;
                    }
                    const actualHeight = e.nativeEvent.layout.height;
                    setIsOverflow(actualHeight > maxHeight);
                    heightChecked.current = true;
                }}
                style={{
                    height: getHeight(),
                    overflow: 'hidden',
                }}
            >
                {content.map((c, index) => {
                    if (isLinkOrText(c[0])) {
                        return (
                            <RNText key={index} style={{ lineHeight: 21 }}>
                                {c.map((item) => (
                                    <LinkOrNormalText
                                        key={item.insert}
                                        url={
                                            (item as LinkMessage)?.attributes
                                                ?.link
                                        }
                                        msgId={props.msgId}
                                        history={props.history}
                                    >
                                        {item.insert}
                                    </LinkOrNormalText>
                                ))}
                            </RNText>
                        );
                    }
                    if (isImageOrVideo(c[0])) {
                        return (
                            <ImageGallery
                                images={c.map((v) => v.insert)}
                                key={index}
                            />
                        );
                    }
                    return null;
                })}
            </View>

            <Condition condition={[!!props.extendButtonName && isOverflow]}>
                <CollapsedButton
                    extendButtonName={props.extendButtonName}
                    value={collapsed}
                    onChange={setCollapsed}
                />
            </Condition>
        </View>
    );
};

export default CollapsibleText;
