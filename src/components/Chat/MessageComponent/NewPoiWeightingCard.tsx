import {
    View,
    Text,
    StyleSheet,
    ViewStyle,
    TextStyle,
    TouchableOpacity,
} from '@mrn/react-native';
import { Icon } from '@roo/roo-rn';
import React, { useEffect, useState } from 'react';

import {
    NewPoiCardItem,
    NewPoiWeightingCardMessage,
} from '../../../types/message';

const STATUS_MAP = {
    0: {
        text: '无意义',
        color: '#e2e2e2',
        bgColor: '#eee',
        textColor: '#999',
    },
    1: {
        text: '待生效',
        color: '#C9DAF0',
        bgColor: '#e3e9f8',
        textColor: '#222',
    },
    2: {
        text: '生效中',
        color: '#FF6A00',
        bgColor: '#FFE7D6',
        textColor: '#fff',
    },
    3: {
        text: '已使用',
        color: '#e2e2e2',
        bgColor: '#eee',
        textColor: '#999',
    },
};

export const NewPoiWeightingCard = (
    props: NewPoiWeightingCardMessage['insert']['newPoiWeightingCard'],
) => {
    let data: any = [props]; // 旧数据兼容
    if ('list' in props) {
        data = props.list;
    }
    const [collapsed, setCollapsed] = useState(true);
    const [finalData, setFinalData] = useState<NewPoiCardItem[]>(
        data.slice(0, 3) as any as NewPoiCardItem[],
    );
    useEffect(() => {
        setFinalData(collapsed ? data.slice(0, 3) : data);
    }, [collapsed]);

    return (
        <View style={styles.container}>
            <View style={styles.divider} />
            {finalData.map((v: NewPoiCardItem) => {
                const status =
                    STATUS_MAP[v.avatar.status as keyof typeof STATUS_MAP];
                return (
                    <View style={styles.content}>
                        <View
                            style={[
                                styles.statusBadge,
                                { backgroundColor: status.bgColor },
                            ]}
                        >
                            <View
                                style={{
                                    flex: 1,
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                }}
                            >
                                <Text style={styles.totalDays}>
                                    {v.avatar.totalLimit}
                                    <Text style={styles.totalDaysUnit}>天</Text>
                                </Text>
                            </View>

                            <View
                                style={{
                                    borderBottomLeftRadius: 6.5,
                                    borderBottomRightRadius: 6.5,
                                    backgroundColor: status.color,
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    height: 16,
                                }}
                            >
                                <Text
                                    style={[
                                        styles.statusText,
                                        {
                                            color: status.textColor,
                                        },
                                    ]}
                                >
                                    {status.text}
                                </Text>
                            </View>
                        </View>
                        <View>
                            <Text style={styles.title} numberOfLines={1}>
                                {v.title}
                            </Text>
                            {v.desc.map((line, index) => (
                                <Text key={index} style={styles.descText}>
                                    {line}
                                </Text>
                            ))}
                        </View>
                    </View>
                );
            })}
            {data.length > 3 ? (
                <View
                    style={{ justifyContent: 'center', flexDirection: 'row' }}
                >
                    <TouchableOpacity
                        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                        onPress={() => setCollapsed(!collapsed)}
                        style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: 'center',
                        }}
                    >
                        <Text style={{ fontSize: 12, color: '#666' }}>
                            {collapsed ? '展开' : '收起'}
                        </Text>
                        {collapsed ? (
                            <Icon
                                type={'expand-less'}
                                tintColor={'#222'}
                                size={14}
                            />
                        ) : (
                            <Icon
                                type={'expand-more'}
                                tintColor={'#222'}
                                size={14}
                            />
                        )}
                    </TouchableOpacity>
                </View>
            ) : null}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        width: '100%',
    },
    divider: {
        backgroundColor: '#E8E8E8',
        height: StyleSheet.hairlineWidth,
        marginVertical: 12,
        marginLeft: -14,
        marginRight: -14,
    },
    content: {
        backgroundColor: '#fff',
        borderRadius: 8,
        padding: 12,
        paddingTop: 0,
        flexDirection: 'row',
    },
    statusBadge: {
        borderRadius: 6.5,
        width: 48,
        height: 48,
        marginRight: 8,
    },
    statusText: {
        fontSize: 11,
        color: '#222',
        textAlign: 'center',
    },
    totalDays: {
        fontSize: 18,
        fontWeight: '700',
        color: '#222',
    },
    totalDaysUnit: {
        fontSize: 12,
        color: '#222',
        fontWeight: '500',
    },
    title: {
        fontSize: 14,
        lineHeight: 20,
        color: '#222',
    },
    descText: {
        fontSize: 11,
        color: '#999',
        marginTop: 2,
    },
} as Record<string, ViewStyle | TextStyle>);
