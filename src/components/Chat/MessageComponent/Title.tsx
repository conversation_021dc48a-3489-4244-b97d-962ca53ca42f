import { View, Text } from '@mrn/react-native';
import React, { useContext, useEffect } from 'react';

import { TitleMessage } from '../../../types/message';

import NetImages from '@/assets/images/homeRefactor';
import Condition from '@/components/Condition/Condition';
import { AnswerContext } from '@/components/MessageBox/Answer/AnswerContext';
import RNImage from '@/components/RNImage';

const Title = ({ title, subTitle }: TitleMessage['insert']['title']) => {
    const { setWithForm } = useContext(AnswerContext);
    useEffect(() => {
        setWithForm(true);
    }, []);
    return (
        <>
            <View style={{ position: 'relative', marginBottom: 4 }}>
                <Text
                    style={{
                        fontSize: 16,
                        fontWeight: '700',
                        zIndex: 1,
                        color: '#222',
                    }}
                >
                    {title}
                </Text>
                <RNImage
                    source={{ uri: NetImages.yellowBall }}
                    style={{
                        width: 30,
                        height: 16,
                        position: 'absolute',
                        left: 16,
                        top: 0,
                    }}
                />
            </View>
            <Condition condition={[subTitle]}>
                <Text style={{ fontSize: 12, color: '#999', marginBottom: 16 }}>
                    {subTitle}
                </Text>
            </Condition>
        </>
    );
};

export default Title;
