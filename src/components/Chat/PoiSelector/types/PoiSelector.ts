import { SelectorItemMessage } from '../../../../types/message';

/**
 * POI 项目数据接口
 */
export interface PoiItemData {
    id?: string | number;
    name: string;
    url?: string;
    online?: boolean;
    labels?: SelectorItemMessage['insert']['selectorItem']['content'];
    tags?: string[];
}

/**
 * POI 项目组件属性接口
 */
export interface PoiItemProps extends PoiItemData {
    onPress: () => void;
    isLast?: boolean;
    isMultiSelect?: boolean;
    isSelected?: boolean;
    onToggleSelect?: () => void;
}

/**
 * POI 选择器配置接口
 */
export interface PoiSelectorConfig {
    isMultiSelect: boolean;
    defaultList: PoiItemData[];
}

/**
 * 分页数据接口
 */
export interface PaginationData {
    list: PoiItemData[];
    total: number;
}

/**
 * API 响应接口
 */
export interface ApiResponse<T = any> {
    code: number;
    data?: T;
    message?: string;
}

/**
 * 获取 POI 列表的响应数据
 */
export interface GetPoiListResponse {
    poiList: PoiItemData[];
    total: number;
}
