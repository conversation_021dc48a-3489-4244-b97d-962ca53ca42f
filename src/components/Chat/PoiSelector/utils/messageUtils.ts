import { CardWithAvatarMessage, Config } from '../../../../types/message';
import { PoiItemData } from '../types/PoiSelector';

/**
 * 创建单个 POI 的消息格式
 */
export const createSinglePoiMessage = (poi: PoiItemData): any[] => {
    return [
        {
            type: 'config',
            insert: {
                config: {
                    style: {
                        backgroundColor: '#fff',
                        width: '100%',
                    },
                },
            },
        } as Config,
        {
            type: 'cardWithAvatar',
            insert: {
                cardWithAvatar: {
                    type: 'poi',
                    avatar: poi.url,
                    title: poi.name,
                    content: [
                        {
                            label: 'ID',
                            value: String(poi.id),
                        },
                    ],
                },
            },
        } as CardWithAvatarMessage,
    ];
};

/**
 * 创建多个 POI 的消息格式
 */
export const createMultiPoiMessage = (poiList: PoiItemData[]): any[] => {
    return [
        {
            type: 'text',
            insert: poiList.map((v) => v.id).join(','),
        },
    ];
};
