# PoiSelector 商家选择器

增强版商家选择器组件，支持多选功能和默认列表展示，可通过接口动态配置选择模式和默认列表。

## 项目结构

```
src/components/Chat/PoiSelector/
├── PoiSelector.tsx              # 主组件
├── index.ts                     # 导出文件
├── README.md                    # 文档
├── components/                  # 子组件
│   ├── index.ts
│   ├── PoiItem.tsx             # POI 项目组件
│   ├── ListEmptyComponent.tsx   # 空列表组件
│   └── ConfirmButton.tsx       # 确认按钮组件
├── hooks/                       # 自定义 Hooks
│   ├── index.ts
│   ├── useValidSelection.ts     # 选择验证 Hook
│   ├── usePoiData.ts           # 数据获取 Hook
│   └── usePoiSelectorConfig.ts  # 配置获取 Hook
├── types/                       # 类型定义
│   └── PoiSelector.ts          # 接口定义
├── api/                         # API 函数
│   └── poiApi.ts               # POI 相关 API
├── utils/                       # 工具函数
│   └── messageUtils.ts         # 消息处理工具
└── __tests__/                   # 测试文件
    ├── PoiSelector.test.tsx
    └── useValidSelection.test.ts
```

## 功能特性

- ✅ **单选模式**: 传统的单个商家选择
- ✅ **多选模式**: 支持选择多个商家
- ✅ **动态配置**: 通过接口获取选择模式和默认列表配置
- ✅ **默认列表**: 支持在列表顶部显示推荐/默认商家，与搜索结果合并展示并自动去重
- ✅ **搜索功能**: 支持按关键词搜索商家
- ✅ **搜索加载**: 搜索时显示 loading 遮罩，阻止用户操作
- ✅ **选择状态**: 多选模式下显示复选框和选中状态
- ✅ **确认按钮**: 多选模式下提供确认选择按钮
- ✅ **智能交集**: 最终选择的商家为当前可见列表和已选列表的交集，确保数据一致性

## 使用方式

### 基础用法

```tsx
import { useUiState } from '@/store/uiState';

const MyComponent = () => {
    const { openPoiSelector } = useUiState();

    // 单选模式
    const handleSingleSelect = () => {
        openPoiSelector({
            multiSelect: false,
            defaultList: []
        });
    };

    // 多选模式
    const handleMultiSelect = () => {
        openPoiSelector({
            multiSelect: true,
            defaultList: []
        });
    };
};
```

### 带默认列表

```tsx
const defaultPoiList = [
    {
        id: 'poi_1',
        name: '推荐商家A',
        url: 'https://example.com/avatar.jpg',
        online: true,
        labels: [
            { label: '类型', value: '餐饮' },
            { label: '评分', value: '4.8' }
        ],
        tags: ['热门', '推荐']
    }
];

const handleOpenWithDefaults = () => {
    openPoiSelector({
        multiSelect: true,
        defaultList: defaultPoiList
    });
};
```

## API 接口

### 获取配置信息

**接口路径：** `GET /bee/v2/bdaiassistant/common/getComponentConfig`

**请求参数：**
```typescript
{
  sessionId: string // 自动注入
}
```

**返回数据：**
```typescript
{
  code: number;
  msg: string;
  data: {
    poiSelector: {
      type: 'MultiSelect' | 'SingleSelect'; // 多选：MultiSelect，单选：SingleSelect
      defaultList: PoiItemData[]; // 默认商家列表
    }
  }
}
```

### 获取商家列表

**接口路径：** `GET /bee/v1/bdaiassistant/common/getOwnPoiListByPage`

**请求参数：**
```typescript
{
  pageSize: number;
  pageNum: number;
  data: string; // 搜索关键词
  sessionId: string; // 自动注入
}
```

## API 参数

### openPoiSelector(options)

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| multiSelect | boolean | false | 是否启用多选模式 |
| defaultList | PoiItemData[] | [] | 默认显示的POI列表，会合并到搜索结果中 |

### PoiItemData 接口

```tsx
interface PoiItemData {
    id?: string | number;           // POI唯一标识
    name: string;                   // POI名称
    url?: string;                   // POI头像URL
    online?: boolean;               // 在线状态
    labels?: Array<{                // 标签信息
        label: string;
        value: string | number;
    }>;
    tags?: string[];                // 标签列表
}
```

## 状态管理

组件使用 Zustand 进行状态管理，相关状态包括：

- `poiSelectorOpen`: 选择器是否打开
- `poiSelectorMultiSelect`: 是否为多选模式
- `poiSelectorDefaultList`: 默认POI列表
- `poiSelectorSelectedList`: 已选择的POI列表

## 自动配置

组件会在挂载时自动调用 `getPoiSelectorConfig` 接口获取配置：

1. **选择模式配置**: 根据接口返回的 `type` 字段决定是单选还是多选
2. **默认列表配置**: 使用接口返回的 `defaultList` 作为默认商家列表
3. **降级处理**: 如果接口调用失败，会使用硬编码的默认配置

## 交互行为

### 单选模式
- 点击POI项直接选择并关闭选择器
- 发送单个POI的卡片消息

### 多选模式
- 点击POI项切换选中状态
- 显示复选框和选中状态
- 标题显示已选择数量
- 点击"确定选择"按钮发送所有选中的POI

### 搜索功能
- 输入关键词时隐藏默认列表
- 显示搜索结果
- 支持分页加载
- **搜索加载状态**: 搜索时显示 Spin 遮罩，阻止用户操作，提升用户体验

### 默认列表
- 默认POI显示在列表顶部
- 与搜索结果合并展示，无视觉分隔
- 自动去重：如果搜索结果中包含默认列表中的商家，会优先显示默认列表中的版本
- 搜索时隐藏默认列表

### 智能交集机制
- **数据一致性保证**: 最终选择的商家始终是当前可见列表（`combinedPoiList`）和已选列表（`poiSelectorSelectedList`）的交集
- **自动清理**: 当搜索条件变化导致某些已选商家不在当前列表中时，会自动从选中状态中移除
- **实时同步**: 选中状态与当前可见列表实时同步，避免选中无效项目
- **用户体验**: 确保用户看到的选中数量和实际可操作的商家数量一致

## 样式特性

- 选中状态：蓝色边框和背景色
- 复选框：蓝色勾选样式
- 确认按钮：动态启用/禁用状态

## 使用场景示例

### 智能交集场景
```typescript
// 场景：用户在多选模式下进行以下操作
// 1. 初始状态：显示默认列表 [A, B, C]，用户选择了 [A, B]
// 2. 用户搜索 "关键词"：列表变为 [B, D, E]（B是搜索结果中的）
// 3. 系统自动处理：选中列表从 [A, B] 变为 [B]（只保留交集）
// 4. 用户继续选择 D：选中列表变为 [B, D]
// 5. 用户清空搜索：列表恢复为 [A, B, C]，选中列表变为 [B]（保持交集）

// 这样确保了：
// - 用户看到的选中数量与实际可操作的商家一致
// - 不会出现选中了不可见商家的情况
// - 搜索和选择操作的数据一致性
```

## 注意事项

1. 默认列表仅在无搜索关键词时显示
2. 默认列表与搜索结果合并展示，无分隔符
3. 自动去重：基于POI的id进行去重，默认列表中的商家优先级更高
4. 关闭选择器时会自动重置所有相关状态
5. 选择器支持键盘避让和安全区域适配
6. **智能交集**: 选中的商家会自动与当前可见列表保持同步，确保数据一致性
