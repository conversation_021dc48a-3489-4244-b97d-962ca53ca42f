# Spin 组件

类似 antd 的 Spin 组件，用于在子组件上方显示加载遮罩，阻止用户操作。

## 功能特性

- 🔄 **加载遮罩**: 在子组件上方显示半透明遮罩
- 🚫 **阻止操作**: 遮罩覆盖整个区域，阻止用户交互
- 🎨 **美观设计**: 带阴影的卡片样式加载提示
- ⚙️ **可配置**: 支持自定义加载文字、颜色和大小
- 📱 **移动端优化**: 适配 React Native 环境

## 使用方式

### 基础用法

```tsx
import { Spin } from './components';

<Spin loading={isLoading}>
  <YourComponent />
</Spin>
```

### 自定义配置

```tsx
<Spin 
  loading={isLoading}
  tip="搜索中..."
  color="#FFD100"
  size="large"
>
  <FlatList data={data} renderItem={renderItem} />
</Spin>
```

## API

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| loading | boolean | - | 是否显示加载状态 |
| tip | string | '加载中...' | 加载提示文字 |
| size | 'small' \| 'large' | 'large' | 加载图标大小 |
| color | string | '#FFD100' | 加载图标颜色 |
| children | React.ReactNode | - | 子组件 |

## 设计说明

- **遮罩层**: 使用 `rgba(255, 255, 255, 0.8)` 半透明白色背景
- **加载卡片**: 白色背景，圆角 8px，带阴影效果
- **布局**: 使用绝对定位覆盖整个父容器
- **层级**: zIndex 设为 999，确保在最上层显示

## 在 PoiSelector 中的应用

在 PoiSelector 组件中，Spin 组件用于包装 FlatList，在搜索时显示加载状态：

```tsx
<Spin 
  loading={loading && !!searchValue} 
  tip="搜索中..."
  color="#FFD100"
>
  <FlatList
    data={combinedPoiList}
    renderItem={renderItem}
    // ... 其他 props
  />
</Spin>
```

这样可以在搜索过程中阻止用户对列表的操作，提供更好的用户体验。
