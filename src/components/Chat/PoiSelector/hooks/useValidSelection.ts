import React, { useEffect } from 'react';

import { PoiItemData } from '../types/PoiSelector';

/**
 * 自定义 Hook：管理选中列表与可见列表的交集
 * 确保最终选择的商家都是当前可见列表中的有效项目
 * @param combinedList 当前可见的合并列表
 * @param selectedList 已选择的列表
 * @param setSelectedList 设置选择列表的方法
 * @returns 有效的选中列表（交集）
 */
export const useValidSelection = (
    combinedList: PoiItemData[],
    selectedList: PoiItemData[],
    setSelectedList: (list: PoiItemData[]) => void,
) => {
    // 计算有效的选中列表（combinedList 和 selectedList 的交集）
    const validSelectedList = React.useMemo(() => {
        const combinedIds = new Set(combinedList.map((item) => item.id));
        return selectedList.filter((item) => combinedIds.has(item.id));
    }, [combinedList, selectedList]);

    // 自动清理无效的选中项目，保持数据一致性
    useEffect(() => {
        if (validSelectedList.length !== selectedList.length) {
            setSelectedList(validSelectedList);
        }
    }, [validSelectedList, selectedList, setSelectedList]);

    return validSelectedList;
};
