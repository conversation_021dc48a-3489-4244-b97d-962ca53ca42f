import { TextInput } from '@mrn/react-native';
import React, { useCallback, useEffect, useRef } from 'react';

import useMessage from '@/hooks/useMessage';
import { useSendMessage } from '@/hooks/useSendMessage';
import { EntryPointType } from '@/types';
import { trackEvent } from '@/utils/track';

const AiTextInput = (props) => {
    const {
        onBlur = () => {},
        onFocus = () => {},
        style = {},
        onTextChange = () => {},
    } = props;
    const {
        text: input,
        set: setInput,
        clear,
        setInputState,
    } = useMessage((state) => state.input);
    const inputRef = useRef<TextInput>();
    const checkIsPolling = useMessage((state) => state.checkIsPolling);
    const { send } = useSendMessage();
    const onSend = useCallback(() => {
        if (checkIsPolling()) {
            return;
        }
        send(input, EntryPointType.USER);
        clear();
    }, [input]);
    useEffect(() => {
        setInputState({
            blur: inputRef.current?.blur,
            focus: inputRef.current?.focus,
            send: onSend,
        });
    }, [inputRef, onSend]);

    const onInputFocus = () => {
        trackEvent('chat_input');
        onFocus();
    };

    return (
        <TextInput
            ref={inputRef}
            enablesReturnKeyAutomatically
            value={input}
            onChangeText={(v) => {
                setInput(v);
                onTextChange(v);
            }}
            style={[
                {
                    borderRadius: 10.5,
                    backgroundColor: '#f5f6fa',
                    height: 40,
                    paddingLeft: 12,
                    flex: 1,
                },
                style,
            ]}
            placeholder="有什么问题尽管问我"
            returnKeyType="send"
            returnKeyLabel="发送"
            blurOnSubmit={false}
            maxLength={100}
            onSubmitEditing={onSend}
            onFocus={onInputFocus}
            onBlur={onBlur}
        />
    );
};
export default AiTextInput;
