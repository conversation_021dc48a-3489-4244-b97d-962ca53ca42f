// @ts-ignore
import { unstable_RootTagContext } from '@mrn/react-native';
import { useCallback, useContext } from 'react';
import useSWR, { useSWRConfig } from 'swr';
import useSWRMutation from 'swr/mutation';

import useCallerRequest from '@/hooks/useCallerRequest';

interface AnnouncementData {
    announcementId: number;
    content: string;
}

const ANNOUNCEMENT_KEY = 'announcement';

const useAnnouncement = () => {
    const callerRequest = useCallerRequest();
    const { mutate } = useSWRConfig();

    // 获取公告数据的 fetcher 函数
    const fetchAnnouncement =
        useCallback(async (): Promise<AnnouncementData | null> => {
            const res = await callerRequest.get(
                '/bee/v2/bdaiassistant/common/getAnnouncement',
                {},
            );

            if (res.code === 0 && res.data) {
                return res.data;
            }

            return null;
        }, [callerRequest]);

    const rootTag = useContext(unstable_RootTagContext);

    // 使用 SWR 获取公告，key 依赖于 rootTag
    const {
        data: announcement,
        error,
        isLoading,
        mutate: refresh,
    } = useSWR([ANNOUNCEMENT_KEY, rootTag], fetchAnnouncement, {
        onError: () => {
            // 错误处理
        },
    });

    // 关闭公告的 mutation 函数
    const closeAnnouncementMutation = async (
        url: string,
        { arg }: { arg: { announcementId: number } },
    ): Promise<boolean> => {
        const res = await callerRequest.post(
            '/bee/v2/bdaiassistant/common/closeAnnouncement',
            {
                announcementId: arg.announcementId,
            },
        );

        if (res.code === 0) {
            return true;
        }
        return false;
    };

    // 使用 useSWRMutation 处理关闭公告
    const { trigger: triggerCloseAnnouncement, isMutating: isClosing } =
        useSWRMutation(
            '/bee/v2/bdaiassistant/common/closeAnnouncement',
            closeAnnouncementMutation,
        );

    // 关闭公告
    const closeAnnouncement = useCallback(
        async (announcementId: number): Promise<boolean> => {
            try {
                const success = await triggerCloseAnnouncement({
                    announcementId,
                });

                if (success) {
                    // 成功关闭后，更新 SWR 缓存，将公告设为 null
                    mutate([ANNOUNCEMENT_KEY, rootTag], null, false);
                    return true;
                }
                return false;
            } catch (e) {
                console.error('关闭公告失败:', e);
                return false;
            }
        },
        [triggerCloseAnnouncement, mutate, rootTag],
    );

    return {
        announcement,
        loading: isLoading,
        error,
        closeAnnouncement,
        isClosing,
        refresh,
    };
};

export default useAnnouncement;
