import { msi } from '@mfe/waimai-mfe-bee-common';
import {
    View,
    Image,
    TouchableOpacity,
    ActivityIndicator,
    StyleSheet,
} from '@mrn/react-native';
import React from 'react';

import NetImages from '@/assets/images/homeRefactor';
import Condition from '@/components/Condition/Condition';
import useMessage from '@/hooks/useMessage';
import TWS from '@/TWS';

const MediaList = () => {
    const file = useMessage((state) => state.file);
    const removeFile = useMessage((state) => state.removeFile);

    if (file.length === 0) {
        return null;
    }
    return (
        <View
            style={{
                flexDirection: 'row',
                marginHorizontal: 12,
                borderBottomWidth: StyleSheet.hairlineWidth,
                borderColor: '#eee',
                paddingBottom: 16,
            }}
        >
            {file.map((media) => {
                const baseImage = (
                    <View
                        style={{
                            width: 80,
                            height: 80,
                            borderRadius: 10,
                            overflow: 'hidden',
                        }}
                    >
                        <Image
                            source={{ uri: media.localSrc }}
                            style={[TWS.square(80), { borderRadius: 10 }]}
                        />
                    </View>
                );
                switch (media.type) {
                    case 'image':
                        return (
                            <TouchableOpacity
                                key={media.src}
                                onPress={() => {
                                    msi.previewImage({
                                        current: media.src,
                                        urls: file.map((v) => v.localSrc),
                                    });
                                }}
                                style={{
                                    position: 'relative',
                                    marginRight: 8,
                                }}
                            >
                                <Condition
                                    condition={[
                                        media.status === 'uploading',
                                        media.status === 'error',
                                        media.status === 'success',
                                    ]}
                                >
                                    <View
                                        style={{
                                            position: 'relative',
                                        }}
                                    >
                                        {baseImage}
                                        <View
                                            style={{
                                                position: 'absolute',
                                                top: 0,
                                                left: 0,
                                                width: 80,
                                                height: 80,
                                                backgroundColor:
                                                    'rgba(0, 0, 0, 0.5)',
                                                borderRadius: 4,
                                                justifyContent: 'center',
                                                alignItems: 'center',
                                            }}
                                        >
                                            <ActivityIndicator
                                                size="small"
                                                color="#fff"
                                            />
                                        </View>
                                    </View>

                                    {baseImage}
                                    {baseImage}
                                </Condition>

                                <TouchableOpacity
                                    style={[
                                        TWS.square(20),
                                        {
                                            position: 'absolute',
                                            top: 0,
                                            right: 0,
                                            backgroundColor:
                                                'rgba(0, 0, 0, 0.6)',
                                            borderRadius: 12,
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            shadowColor: '#000',
                                            shadowOffset: {
                                                width: 0,
                                                height: 1,
                                            },
                                            shadowOpacity: 0.3,
                                            shadowRadius: 2,
                                            elevation: 3,
                                        },
                                    ]}
                                    onPress={() => {
                                        removeFile(media.key);
                                    }}
                                >
                                    <Image
                                        source={{
                                            uri: NetImages.closeCircleWhite,
                                        }}
                                        style={[TWS.square(20)]}
                                    />
                                </TouchableOpacity>
                            </TouchableOpacity>
                        );
                    default:
                        return null;
                }
            })}
        </View>
    );
};

export default MediaList;
