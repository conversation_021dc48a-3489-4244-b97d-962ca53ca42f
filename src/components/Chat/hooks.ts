import { apiCaller } from '@mfe/cc-api-caller-bee';
import { Toast } from '@roo/roo-rn';
import dayjs from 'dayjs';
import { useContext } from 'react';

import RootTagContext from '../../hooks/rootTagContext';
import useMessage from '../../hooks/useMessage';
import { REFRESH_SYS_MESSAGE_ID } from '../../store/message';
import { MessageContentType, MessageStatus, MessageType } from '../../types';
import { trackEvent } from '../../utils/track';

export const useRefreshMessage = () => {
    const add = useMessage((state) => state.add);
    const disconnect = useMessage((state) => state.disconnect);
    const scrollToEnd = useMessage((state) => state.scrollToEnd);
    const appendSessionId = useMessage((state) => state.appendSessionId);
    const { source } = useContext(RootTagContext);
    const getLatestSessionId = useMessage((state) => state.getLatestSessionId);

    return async (noRefreshMsg = false, callback = () => {}) => {
        trackEvent('chat_refresh');

        disconnect();
        const res = await apiCaller.send(
            '/bee/v1/bdaiassistant/refreshSession',
            { source, sessionId: getLatestSessionId() },
        );

        // 刷新会话时强制滚动
        scrollToEnd(true);

        if (res.code !== 0) {
            return;
        }

        appendSessionId(res.data.sessionId);
        callback();

        if (!res.data.showRefreshMsg || noRefreshMsg) {
            return;
        }

        Toast.open('已为您生成新的对话 ~ ');
        add({
            status: MessageStatus.DONE,
            type: MessageType.SYSTEM,
            msgId: `${REFRESH_SYS_MESSAGE_ID}_${dayjs().unix()}`,
            abilityType: undefined,
            subAbilityType: undefined,
            msgType: MessageContentType.TEXT,
            currentContent: '问我新的问题吧',
        });
    };
};
