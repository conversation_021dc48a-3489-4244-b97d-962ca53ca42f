# DraggedMenu 工具弹窗组件设计文档

## 1. 组件概述

DraggedMenu 是一个可拖拽的工具弹窗组件，为用户提供快速访问常用工具的功能。组件内的菜单项可拖动调整顺序，提供用户自定义工具顺序的能力。

### 1.1 主要功能
- 工具快速访问：集中展示常用工具，提供一键访问能力
- 顺序调整：支持工具项拖拽排序，满足用户个性化需求
- 状态记忆：自动保存用户的位置和排序偏好
- 金刚位：置顶的工具不支持拖动调整顺序，固定展示在顶部

### 1.2 适用场景
- 需要快速访问多个工具功能的页面
- 用户频繁使用的功能集合
- 需要灵活调整工具顺序和位置的场景
- 不希望占用固定屏幕空间的浮动工具栏

### 1.3 核心价值
- 提升用户操作效率：一键直达常用功能
- 增强用户体验：支持个性化定制
- 节省屏幕空间：可拖拽收起，不影响主界面展示
- 操作便捷：直观的拖拽交互，易于上手

## 2. UI 设计规范

### 2.1 基础样式
- 整体采用圆角设计（borderRadius: 12）
- 背景色使用白色（#FFFFFF）
- 阴影效果：elevation: 4

### 2.2 布局结构
- 主容器：垂直布局
- 顶部标题栏：水平布局
- 工具列表：网格布局（3列）

### 2.3 组件尺寸
- 弹窗宽度：240pt
- 工具图标尺寸：13.34 x 13.34pt
- 标题栏高度：44pt
- 内容区域padding：12pt

## 3. 功能模块

### 3.1 标题栏
- 左侧：标题文本 "工具"

### 3.2 工具列表
每个工具项包含：
- 拖拽图标
- 工具图标
- 文字说明（单行，靠左）
- 点击效果（透明度变化）

### 3.4 交互行为
- 拖拽：
  - 可通过标题栏拖动整个弹窗
  - 拖动时保持在屏幕边界内
  - 松手后自动吸附到最近的边缘
- 点击：
  - 工具项点击触发对应功能，并关闭弹窗
  - 关闭按钮点击关闭弹窗
- 动画：
  - 打开/关闭时的渐入渐出效果
  - 拖动时的跟随动画
- 滚动
  - 支持滚动，容器高度为424pt
-

## 4. 技术实现要点

### 4.1 核心依赖
- react-native-draggable-flatlist

### 4.2 状态管理
```typescript
interface DraggedMenuProps {
  style?: StyleProp<ViewStyle>; // 容器样式
  show?: boolean; // 控制组件显隐
}

interface DragMenuItem {
   icon: string;
   isTop: boolean; // 是否置顶
   content: string;
}
```

### 4.3 性能优化
- 使用react-native-draggable-flatlist提供的DraggableFlatList视线，组件使用demo在src/pages/draggedFlatListDemo/demo.tsx

## 5. 可访问性
- 所有可点击元素添加适当的触摸反馈
- 确保足够的点击区域（最小 44x44）

## 6. 测试要点

- 拖拽功能测试
- 边界碰撞检测
- 工具项点击响应
- 动画流畅度
- 不同机型适配
- 性能测试（60fps）

## 7. 注意事项

1. 确保拖拽时不会完全移出屏幕