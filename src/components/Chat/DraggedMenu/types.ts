import { StyleProp, ViewStyle } from '@mrn/react-native';

import useToolbar from '@/components/AbilitySelector/useToolbar';

export interface DraggedMenuProps {
    style?: StyleProp<ViewStyle>;
    show?: boolean;
    onClose?: () => void;
    initialItems?: DragMenuItem[];
    setShow?: (show: boolean) => void;
    toolbar: ReturnType<typeof useToolbar>;
}

export interface DragMenuItem {
    id: string;
    icon: string;
    isTop: boolean;
    content: string;
    onPress?: () => void;
}

export interface DraggedMenuState {
    items: DragMenuItem[];
    setItems: (items: DragMenuItem[]) => void;
}
