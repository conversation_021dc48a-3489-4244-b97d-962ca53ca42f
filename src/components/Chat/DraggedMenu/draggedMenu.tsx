import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    Image,
} from '@mrn/react-native';
import React, { useCallback, forwardRef, useImperativeHandle } from 'react';

import DraggableFlatList from './DraggableFlatList';
import { DraggedMenuProps } from './types';

import NetImages from '@/assets/images/homeRefactor';
import closeBlack from '@/assets/images/poiSelector/closeBlack.png';
import Condition from '@/components/Condition/Condition';
import TWS from '@/TWS';

const MENU_WIDTH = 240;

export interface DraggedMenuRef {
    closeMenu: () => void;
}

const DraggedMenu = forwardRef<DraggedMenuRef, DraggedMenuProps>(
    ({ style, onClose, toolbar }, ref) => {
        const {
            topToolbarOptions,
            normalToolbarOptions,
            changeSort,
            onAbilityPress,
        } = toolbar;

        useImperativeHandle(ref, () => ({
            closeMenu: () => {
                onClose?.();
            },
        }));

        const handlePress = (item: any) => {
            onClose?.();
            setTimeout(() => {
                onAbilityPress?.(item);
            }, 250);
        };

        const renderItem = useCallback(
            ({
                item,
                isActive,
                move = () => {},
                moveEnd = () => {},
                isLast = false,
            }) => {
                return (
                    <TouchableOpacity
                        style={{
                            height: 32,
                            backgroundColor: isActive
                                ? '#999'
                                : item.backgroundColor,
                            alignItems: 'center',
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                            paddingHorizontal: 14,
                            marginBottom: isLast ? 0 : 10.5,
                        }}
                        onPress={() => handlePress(item)}
                        onLongPress={move}
                        onPressOut={moveEnd}
                    >
                        <View style={[TWS.row(), { alignItems: 'center' }]}>
                            <Image
                                source={{
                                    uri:
                                        item.link || NetImages.defaultSkillIcon,
                                }}
                                style={[TWS.square(15), { marginRight: 4 }]}
                            />
                            <Text style={styles.itemText}>{item.content}</Text>
                        </View>

                        <Condition condition={[!item.top]}>
                            <Image
                                source={{ uri: NetImages.dragIcon }}
                                style={[
                                    TWS.square(14),
                                    {
                                        position: 'relative',
                                        right: 10,
                                    },
                                ]}
                            />
                        </Condition>
                    </TouchableOpacity>
                );
            },
            [onClose],
        );

        return (
            <View style={{ alignItems: 'flex-end' }}>
                <View style={[styles.container, style]}>
                    <View style={styles.header}>
                        <Text style={styles.title}>全部工具</Text>
                    </View>

                    {/* 可拖拽的项目 */}
                    <View style={{ height: 400 }}>
                        <DraggableFlatList
                            ListHeaderComponent={
                                <View>
                                    {topToolbarOptions.map((item, index) =>
                                        renderItem({
                                            item,
                                            isLast:
                                                topToolbarOptions.length ===
                                                index + 1,
                                        }),
                                    )}
                                    <Condition
                                        condition={[
                                            normalToolbarOptions.length > 0,
                                        ]}
                                    >
                                        <View style={TWS.line()} />
                                    </Condition>
                                </View>
                            }
                            showsVerticalScrollIndicator={false}
                            data={normalToolbarOptions}
                            onMoveEnd={({ data }) => changeSort(data)}
                            keyExtractor={(item) => item.content}
                            renderItem={renderItem}
                            scrollPercent={5}
                        />
                    </View>
                </View>
                <TouchableOpacity
                    onPress={onClose}
                    style={{
                        marginTop: 12,
                        position: 'relative',
                        zIndex: 1000,
                        width: 28,
                        height: 28,
                        borderRadius: 20,
                        backgroundColor: '#FFFFFF',
                        alignItems: 'center',
                        justifyContent: 'center',
                    }}
                >
                    <Image
                        source={closeBlack}
                        style={{ width: 18, height: 18 }}
                    />
                </TouchableOpacity>
            </View>
        );
    },
);

const styles = StyleSheet.create({
    container: {
        width: MENU_WIDTH,
        backgroundColor: '#FFFFFF',
        borderRadius: 12,
        elevation: 4,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        zIndex: 10,
        position: 'relative',
    },
    header: {
        height: 44,
        paddingHorizontal: 12,
        flexDirection: 'row',
        alignItems: 'center',
        // borderBottomWidth: StyleSheet.hairlineWidth,
        // borderBottomColor: '#E5E5E5',
    },
    title: {
        fontSize: 16,
        fontWeight: '500',
        color: '#333333',
    },
    content: {
        flex: 1,
        padding: 12,
    },
    itemContainer: {
        width: (MENU_WIDTH - 24 - 16) / 3,
        height: 44,
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 8,
        marginBottom: 8,
    },
    activeItem: {
        backgroundColor: '#F5F5F5',
        borderRadius: 6,
    },
    topItem: {
        backgroundColor: '#F8F8F8',
        borderRadius: 6,
    },
    dragIcon: {
        marginRight: 4,
        opacity: 0.5,
    },
    icon: {
        marginRight: 4,
    },
    itemText: {
        fontSize: 14,
        color: '#333333',
        flex: 1,
    },
});

// 添加组件显示名称以便调试
DraggedMenu.displayName = 'DraggedMenu';

export default DraggedMenu;
