import {
    FlatList,
    Keyboard,
    ScrollViewProps,
    StyleSheet,
    View,
} from '@mrn/react-native';
import { useRequest, useThrottleFn } from 'ahooks';
import React, { forwardRef, useEffect } from 'react';

import useMessage from '../../hooks/useMessage';
import { useScroll } from '../../store/scroll';
import MessageBox from '../MessageBox';

interface ChatContent {
    inputRef: any;
    onScroll: ScrollViewProps['onScroll'];
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        paddingHorizontal: 16,
    },
});

const ChatContent = forwardRef((props: ChatContent, ref: any) => {
    const messageList = useMessage((state) => state.messageList);
    const historyMessageList = useMessage((state) => state.historyMessageList);
    const isLoadingHistory = useMessage((state) => state.isLoadingHistory);
    const setHistoryLoading = useMessage((state) => state.setHistoryLoading);
    const getHistoryMessageList = useMessage(
        (state) => state.getHistoryMessageList,
    );
    const setScrollToEnd = useMessage((state) => state.setScrollToEnd);
    const block = useScroll((state) => state.block);
    const setBlock = useScroll((state) => state.setBlock);

    const scrollToEnd = (force = false) => {
        if (block && !force) {
            return;
        }
        if (isLoadingHistory) {
            try {
                ref.current?.scrollToIndex({
                    viewPosition: 0,
                    index: 0,
                    animated: true,
                });
            } catch (e) {
                console.log(e);
            }
            setTimeout(() => {
                setHistoryLoading(false);
            }, 2000);
            return;
        }
        ref.current?.scrollToEnd();
    };

    const { run, loading } = useRequest(getHistoryMessageList, {
        manual: true,
    });

    const { run: throttleScroll } = useThrottleFn(scrollToEnd, { wait: 100 });

    useEffect(() => {
        setScrollToEnd(throttleScroll);
    }, []);

    return (
        <View style={{ flex: 1 }}>
            <FlatList
                overScrollMode={'never'}
                contentContainerStyle={{ flexGrow: 1 }}
                onScrollBeginDrag={() => {
                    setBlock(true);
                    Keyboard.dismiss();
                }}
                ListFooterComponent={<View style={{ paddingBottom: 40 }} />} // 如果paddingBottom直接放在contentContainerStyle，那么scrollToEnd会忽略他
                removeClippedSubviews={false}
                data={[...historyMessageList, ...messageList]}
                keyExtractor={(item) =>
                    String(item.msgId || item.questionMsgId)
                }
                renderItem={({ item }) => <MessageBox data={item} />}
                ref={ref}
                style={styles.container}
                keyboardShouldPersistTaps="handled"
                onTouchEnd={props.inputRef.current?.blur}
                onScroll={props.onScroll}
                onRefresh={run}
                refreshing={loading}
            />
        </View>
    );
});

export default ChatContent;
