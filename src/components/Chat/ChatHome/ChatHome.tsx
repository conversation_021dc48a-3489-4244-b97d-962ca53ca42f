import { ScrollView, StyleSheet, Text, View, Image } from '@mrn/react-native';
import React, { useMemo } from 'react';

import HotQuestions from './HotQuestions';

import NetImages from '@/assets/images/homeRefactor';
import useWeather from '@/hooks/useWeather';

const ChatHome = () => {
    const { weatherInfo } = useWeather();
    const icon = useMemo(() => {
        switch (weatherInfo?.weatherType) {
            case 'rain':
                return NetImages.rainIcon;
            case 'cold':
                return NetImages.coldIcon;
            case 'hot':
                return NetImages.hotIcon;
            default:
                return NetImages.defaultIcon;
        }
    }, [weatherInfo?.weatherType]);

    return (
        <ScrollView
            style={styles.container}
            showsVerticalScrollIndicator={false}
        >
            <View style={{ alignItems: 'center', marginTop: 40 }}>
                <Image
                    source={{ uri: icon }}
                    style={[
                        { width: 208, height: 158.5 },
                        { marginBottom: -10 },
                    ]}
                />
                <Text
                    style={{
                        fontSize: 24,
                        fontWeight: '900',
                        color: '#222',
                        textAlign: 'center',
                        marginBottom: 10,
                    }}
                >
                    {weatherInfo?.greeting}
                </Text>
                <Text
                    style={{
                        fontSize: 24,
                        fontWeight: '900',
                        color: '#222',
                        textAlign: 'center',
                        marginBottom: 10,
                    }}
                >
                    {weatherInfo?.weatherTips}
                </Text>
            </View>
            <HotQuestions />
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
});

export default ChatHome;
