import { msi } from '@mfe/waimai-mfe-bee-common';

export interface Point {
    latitude: number;
    longitude: number;
    accuracy: number;
    altitude: number;
    speed: number;
    address?: string;
}

export const getMSILocation = async (
    invokerId?: string,
): Promise<null | Point> => {
    return new Promise((resolve) => {
        msi.getLocation({
            type: 'gcj02',
            _mt: {
                sceneToken: invokerId || 'bee_assistant',
                allowNetworkImprovement: true,
                isGeo: true,
            },
            success: (res) => {
                const { latitude, longitude, accuracy, altitude, speed, _mt } =
                    res;
                const { detail } = _mt || {};
                resolve({
                    latitude,
                    longitude,
                    accuracy,
                    altitude,
                    speed,
                    address: detail,
                });
            },
        });
    });
};
