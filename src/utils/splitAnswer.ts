import { Message } from '../types/message';

const splitAnswer = (text: string) => {
    if (!text) {
        return [];
    }
    // ![链接类型](https://***)
    const splitPattern =
        /(!\[pic\|.*?]\(.*?\))|(!\[video\|.*?]\(.*?\))|(\[.*?\|.*?])/;
    const parts = text.split(splitPattern);
    return parts.filter((v) => v && !/^\s*$/.test(v));
};

// type Res =
//     | {
//           type: 'text';
//           content: { text: string; url?: string };
//           origin: string;
//       }
//     | {
//           type: 'pic';
//           content: { text: string; url: string }[];
//           origin: string;
//       }
//     | {
//           type: 'video';
//           content: { text: string; url: string }[];
//           origin: string;
//       }
//     | {
//           type: 'link';
//           content: { url: string; text: string };
//           origin: string;
//       };

const rule = {
    link: {
        test: (str: string) => /\[(.*?)\|(.*?)]/.test(str),
        text2Obj: (str: string) => {
            const value = str.split(/\[(.*?)\|(.*?)]/).filter((v) => v);
            return { text: value[0], url: value[1] };
        },
    },
    pic: {
        test: (str: string) => /!\[pic\|.*?]\((.*?)\)/.test(str),
        text2Obj: (str: string) => {
            const value = str.split(/!\[pic\|(.*?)]\((.*?)\)/).filter((v) => v);
            return { text: value[0], url: value[1] };
        },
    },
    video: {
        test: (str: string) => /!\[video\|.*?]\((.*?)\)/.test(str),
        text2Obj: (str: string) => {
            const value = str
                .split(/!\[video\|(.*?)]\((.*?)\)/)
                .filter((v) => v);
            return { text: value[0], url: value[1] || value[0] };
        },
    },
};
const mergeAnswer = (parts: string[]): Message[] => {
    const res: Message[] = [];
    parts.forEach((p) => {
        const lastEle = (res[res.length - 1] || {
            type: '',
        }) as Message;
        if (rule.pic.test(p)) {
            const imgObj = rule.pic.text2Obj(p);
            if (lastEle.type === 'media') {
                lastEle.insert.media.push({ image: imgObj.url });
            } else {
                res.push({
                    type: 'media',
                    insert: { media: [{ image: imgObj.url }] },
                });
            }
            return;
        }
        if (rule.video.test(p)) {
            const imgObj = rule.video.text2Obj(p);
            if (lastEle.type === 'media') {
                lastEle.insert.media.push({ video: imgObj.url });
            } else {
                res.push({
                    type: 'media',
                    insert: { media: [{ image: imgObj.url }] },
                });
            }
            return;
        }
        if (rule.link.test(p)) {
            const linkObj = rule.link.text2Obj(p);
            res.push({
                type: 'link',
                insert: linkObj.text,
                attributes: { link: linkObj.url },
            });
            return;
        }
        res.push({
            type: 'text',
            insert: p,
        });
    });
    return res;
};
export default (text: string) => mergeAnswer(splitAnswer(text));
