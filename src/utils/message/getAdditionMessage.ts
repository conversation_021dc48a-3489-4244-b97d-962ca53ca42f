import { AdditionFile, AdditionMessage } from '@/types/message';

export const getAdditionMessage = (file: AdditionFile[], text?: string) => {
    if (!file.length) {
        return '';
    }
    return JSON.stringify(
        [
            {
                type: 'addition',
                insert: {
                    addition: {
                        additionList: file.filter(
                            (item) => item.status === 'success',
                        ),
                    },
                },
            } as AdditionMessage,
            text
                ? {
                      type: 'text',
                      insert: text,
                  }
                : null,
        ].filter(Boolean),
    );
};
