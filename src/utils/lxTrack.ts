import MRNUtils from '@mrn/mrn-utils';

/**
 * @description: 灵犀埋点方法，调用的是MRN灵犀桥：https://km.sankuai.com/docs/mrn/page/293902581#id-%E7%81%B5%E7%8A%80%E5%9F%8B%E7%82%B9[0.1.3%E6%96%B0%E5%A2%9E]
 * @return {*}
 */
export function LXTrack() {}

/**
 * @description: 页面展示方法，cid必传，其他可不传
 * @param {string} cid
 * @param {object} lab 自定义参数
 * @param {string} pageInfoKey 页面标识，可不传
 * @param {*} channel 默认都是外卖channel
 * @return {*}
 */
LXTrack.pageView = (
    cid: string,
    lab?: { [key: string]: any },
    pageInfoKey?: string,
    channel = 'waimai_e',
) => {
    MRNUtils.lxTrackMPT(channel, pageInfoKey, cid, lab || {});
};

/**
 * @description: 模块展示方法，cid和bid必传，其他可不传
 * @param {string} cid
 * @param {string} bid
 * @param {object} lab 自定义参数
 * @param {string} pageInfoKey 页面标识，可不传
 * @param {*} channel 默认都是外卖channel
 * @return {*}
 */
LXTrack.moduleView = (
    cid: string,
    bid: string,
    lab?: { [key: string]: any },
    pageInfoKey?: string,
    channel = 'waimai_e',
) => {
    MRNUtils.lxTrackModuleView({
        val_bid: bid,
        val_cid: cid,
        pageInfoKey: pageInfoKey,
        channelName: channel,
        val_lab: lab || {},
    });
};

/**
 * @description: 模块点击方法，cid和bid必传，其他可不传
 * @param {string} cid
 * @param {string} bid
 * @param {object} lab 自定义参数
 * @param {string} pageInfoKey 页面标识，可不传
 * @param {*} channel 默认都是外卖channel
 * @return {*}
 */
LXTrack.moduleClick = (
    cid: string,
    bid: string,
    lab?: { [key: string]: any },
    pageInfoKey?: string,
    channel = 'waimai_e',
) => {
    MRNUtils.lxTrackModuleClick({
        val_bid: bid,
        val_cid: cid,
        pageInfoKey: pageInfoKey,
        channelName: channel,
        val_lab: lab || {},
    });
};

/**
 * @description: 模块编辑方法
 * @param {string} cid
 * @param {string} bid
 * @param {object} lab
 * @param {string} pageInfoKey
 * @param {*} channel
 * @return {*}
 */
LXTrack.moduleEdit = (
    cid: string,
    bid: string,
    lab?: { [key: string]: any },
    pageInfoKey?: string,
    channel = 'waimai_e',
) => {
    MRNUtils.lxTrackModuleEdit({
        val_bid: bid,
        val_cid: cid,
        pageInfoKey: pageInfoKey,
        channelName: channel,
        val_lab: lab || {},
    });
};

export default LXTrack;
