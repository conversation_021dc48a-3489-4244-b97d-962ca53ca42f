import KNB from '@mrn/mrn-knb';
import { Linking } from '@mrn/react-native';
import { Dialog } from '@roo/roo-rn';
import { getUniqueID } from 'react-native-device-info';

const baseParams = {
    app_key: 'OleGEqobLFNZoEd1sF3rYWISg9SAaP5ML6qtupIqyig=',
    secret_key: 'c9ec52e5f4db48009e89310c4f1a1119',
    uuid: getUniqueID(), // 设备唯一标示
    cat_app_id: '1742432103146741772', // cat 埋点需要的 appId
};

const init = () => {
    KNB.use('speechAsr.init', {
        ...baseParams,
        log_level: 3, // 默认值0，0不打印日志；1只打印错误日志；2警告日志和错误日志；3打印所有日志
        api_version: '1.3.0',
        success: function () {
            //TODO
        },
        fail: function () {
            //TODO
        },
    });
};

type Callback = (v?: any) => void;
interface Props {
    onGetMiddleResult: Callback;
    onGetFinalResult: Callback;
    onGetVolume: Callback;
    onStop: Callback;
    onFail: Callback;
    onSuccess: Callback;
}
const startRecognize = ({
    onGetMiddleResult = () => {},
    onGetFinalResult = () => {},
    onGetVolume = () => {},
    onStop = () => {},
    onFail = () => {},
    onSuccess = () => {},
}: Partial<Props>) => {
    KNB.use('speechAsr.startRecognize', {
        app_key: baseParams.app_key,
        secret_key: baseParams.secret_key,
        privacy_scene_token: 'waimai_cd_fe_bee_assistant',
        needContinuousInvoke: true,
        // audio_Id: 'xxxxxxxxxxxxxxxxxx', // 一个随机不重复字符串，不传该参数的话会内部自动生成
        asr_params: {
            is_need_audio_cache: 0, //    设置是否需要语音缓存数据，默认值0，代表不需要语音缓存数据
            asr_model: 0, //  语言模型，默认值0。
            asr_sub_model: 0, //  子语言模型，默认值0。若想针对场景进行定制可使用改参数。子语言模型(自定义语言模型)相关的需求请联系：chenxiang22
            is_ignore_tmp_result: 0, //  设置是否需要中间的识别结果，默认值0，代表需要中间结果
            is_need_punctuation: 1, //  设置是否添加符号，默认值0。0添加符号；1添加符号；2空格模式
            record_sound_max_size: 60000, //  设置语音超时时间，默认值60000，代表60000毫秒，取值区间[1000 , 60000]
            // biz_data: '扩展字段', //  支持拓展字段，最长4k，默认为空。
            nbest_count: 1, // 设置最终获得识别结果数，默认1，代表只需要一个最优结果，值域[1,10]
            support_vad: false, //是否启用vad，默认是false，只有当VAD SDK已经接入时，此设置才会起作用
            support_codec: false, //是否启用语音压缩，默认值false，只有当SPEEX SDK接入后，此设置才会起作用
            overtime_autostop: true, // 录音超时自动停止识别
            background_autostop: true, // 进入后台自动停止识别
        },
        api_version: '1.3.0',
        handle: function (result) {
            switch (result.errorCode) {
                case 1:
                    onGetFinalResult(result.data);
                    console.log('%c识别结果为：', 'color: blue', result.data);
                    // 若 code 为 1，则是 语音识别的结果。
                    break;
                case 2:
                    onGetVolume(result.data);
                    console.log('%c声音分贝为：', 'color: blue', result.data);
                    // 若 code 为2，则是 声音分贝回调
                    break;
                case 3:
                    onGetMiddleResult(result.data);
                    console.log('%c中间结果为：', 'color: blue', result.data);
                    // 若 code 为3，则是 语音识别的中间结果
                    break;
                case 4:
                    console.log('%c超时回调：', 'color: blue', result.data);
                    // 若 code 为4，则是 录音超时回调
                    break;
                case 5:
                    onStop();
                    console.log('%c自动停止回调：', 'color: blue', result.data);
                    // 若 code 为5，则是 录音超时且已主动停止识别 回调
                    break;
                case 6:
                    onStop();
                    console.log(
                        '%c进入后台且已主动停止识别：',
                        'color: blue',
                        result.data,
                    );
                    // 若 code 为6，则是 进入后台且已主动停止识别 回调
                    break;
                default:
                    // 可以处理其他未知的 errorCode 或者不做任何操作
                    break;
            }
            console.log('%chandle', 'color:red', result);
        },
        success: function (result) {
            console.log('开始识别成功', 'color: blue', result);
            //调用成功
            onSuccess();
        },
        fail: function (error) {
            console.log('开始识别失败', 'color:blue', error);
            //调用失败
            if (error.errorCode === -102 || error.errorCode === -103) {
                // -102/-103 为重复开始/停止识别警报，可以忽略
            } else if (error.errorCode === 9100) {
                getMicroPhone();
                onFail(error);
            } else {
                onFail(error);
            }
        },
    });
};

export const stopRecognize = () => {
    KNB.use('speechAsr.endRecognize', {
        success: function () {
            //调用成功
        },
        fail: function () {
            //调用失败
        },
    });
};

export const initAndStartRecognize = async (props: Partial<Props>) => {
    init();
    startRecognize({ ...props });
};

export type CallbackGetMicroPhone = (res?: any) => void;
export const getMicroPhone = (
    onSuccess: CallbackGetMicroPhone = () => {},
    onFail: CallbackGetMicroPhone = () => {},
) => {
    KNB.requestPermission({
        sceneToken: 'Microphone', // 受隐私合规中长期方案影响，新增参数sceneToken，type与sceneToken的对应关系如下表
        type: 'microphone', // 权限类型，支持选项参考下表
        readonly: false, // 如果为 true，将只读权限，不做申请权限的尝试
        forceJump: true, // 默认值为false，为false时会询问用户，向用户申请权限。如果为 true，将直接唤起系统设置面板。
        success: function (res) {
            console.log(
                '%cSuccess to request microphone permission',
                'color:red',
                res,
            );
            onSuccess(res);
        },
        fail: function (res) {
            console.log(
                '%cFail to request microphone permission',
                'color:blue',
                JSON.stringify(res),
            ); // 没有该权限，或者唤起请求面板后用户拒绝该权限
            Dialog.open({
                title: '是否前往设置权限',
                message: '无麦克风权限，是否前往设置页面设置权限',
                confirmCallback: () => Linking.openSettings(),
            });
            onFail(res);
        },
    });
};
