import { Message, MessageContentType } from '../types';

const isEmptyAnswer = (m: Message) => {
    const hasNoImage = !m.imageList?.length;
    switch (m.msgType) {
        case MessageContentType.TEXT:
            return !m.currentContent && !m.previousContent && hasNoImage;
        case MessageContentType.WITH_OPTIONS:
            return (
                !m.prefixTextContent &&
                !m.postTextContent &&
                !m.selectionItems.length &&
                hasNoImage
            );
        case MessageContentType.WELCOME:
            return false;
    }
};
export default isEmptyAnswer;
