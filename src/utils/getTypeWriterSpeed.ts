import { MessageStatus } from '../types';

export const normalSpeed = 60; // 常规打字速度
export const fasterSpeed = 30; // 更快的流式打字速度
export const fastestSpeedWithAllContentReturned = 5; // 最大速度，触发场景为流式结果已全部返回后

export const getTypeWriterSpeed = (lastStatus, typingStrArrLength) => {
    if (lastStatus === MessageStatus.DONE) {
        return fastestSpeedWithAllContentReturned;
    }
    return typingStrArrLength > 1 ? fasterSpeed : normalSpeed;
};
