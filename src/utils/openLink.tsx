import { msi } from '@mfe/waimai-mfe-bee-common';
import KNB from '@mrn/mrn-knb';
import { Platform, Text, View } from '@mrn/react-native';
import { Dialog, Toast } from '@roo/roo-rn';
import { parse, updateURLParam } from '@utiljs/param';
import useCountdown from 'ahooks/es/useCountDown';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useEffect, useRef } from 'react';

import { isTTLink, logTTLink } from './url';
import Condition from '../components/Condition/Condition';
import useMessage from '../hooks/useMessage';
import { useUser as getUser } from '../store/user';
import { Message } from '../types';

const CountDownTitle = ({ leftTime = 2000, onEnd }) => {
    const [_, countdownRes] = useCountdown({ leftTime, onEnd });
    return (
        <View style={{ alignItems: 'center' }}>
            <Condition condition={[countdownRes.seconds > 0, true]}>
                <Text style={{ fontSize: 15, color: '#222' }}>
                    {countdownRes.seconds}秒后即将自动跳转
                </Text>
                <Text style={{ fontSize: 15, color: '#222' }}>
                    即将自动跳转
                </Text>
            </Condition>
        </View>
    );
};

const useOpenLink = () => {
    const getClipboardData = useMessage((state) => state.getClipboardData);
    const openLink = async (
        url: string,
        message?: Pick<Message, 'msgId' | 'history'>,
        sessionId = [],
    ) => {
        // 该域名网站在安卓app内无法正常打开，需跳转浏览器
        if (url.includes('gov.cn') && Platform.OS === 'android') {
            return KNB.pureUse('openScheme', {
                url: `${url}${url.includes('?') ? '&' : '?'}openInApp=2`,
            });
        }
        if (!isTTLink(url)) {
            return KNB.openPage({
                url,
            });
        }

        //tt链接加参逻辑，用于数据看板埋点
        if (message.history) {
            Toast.open(
                '历史记录的tt不支持点击喔 ~ 老铁可以试试跟我们机器人重新交流一下~',
            );
            return;
        }

        logTTLink(url, message.msgId, _.last(sessionId));
        const uid = (await getUser.getState().getUser()).uid;
        const res = parse(url);
        const { originURL } = res as any;
        const newURL1 = updateURLParam(originURL, 'source', 'bd_ai_assistant');
        const newURL2 = encodeURIComponent(
            updateURLParam(
                newURL1,
                'associatedField',
                `${message.msgId}_${uid}_${dayjs().format(
                    'YYYY-MM-DD HH:mm:ss',
                )}_${sessionId.join(',')}`,
            ),
        );
        const finalUrl = updateURLParam(url, 'originURL', newURL2);

        const clipboardData = getClipboardData();
        if (clipboardData.when === 'clickTT') {
            msi.setClipboardData({
                data: clipboardData.content, // 需要复制的数据
                _mt: {
                    sceneToken: 'bee-assistant-main', // 设置自己的隐私合规token
                },
            });
            let dialogIns;
            const confirm = () => {
                KNB.openPage({ url: finalUrl });
                dialogIns?.close();
            };
            dialogIns = Dialog.open({
                header: <CountDownTitle onEnd={confirm} />,
                message: clipboardData.toast,
                confirmLabel: '跳转',
                confirmCallback: confirm,
                modalProps: {
                    maskClosable: false,
                },
            });
            return dialogIns;
        }

        return KNB.openPage({ url: finalUrl });
    };
    const fnDebounce = useRef<typeof openLink>();

    useEffect(() => {
        fnDebounce.current = _.debounce(openLink);
    }, []);
    return fnDebounce.current || ((() => {}) as any as typeof openLink);
};
export default useOpenLink;
