import { TraceUtil } from '@mfe/waimai-mfe-bee-common';

import lxTrack from './lxTrack';

export const enum TrackEventType {
    MC = 'mc',
    MV = 'mv',
}

export const track = (view: string) => {
    if (!view) {
        return;
    }

    // 页面埋点, bee_common中已经添加了bee_rn前缀，直接使用ocean中的配置View即可
    // assistant_是小蜜助手子包特有前缀
    TraceUtil.traceLXView(`assistant_${view}`);
};

export const trackEvent = (
    event: string,
    obj = {},
    type = TrackEventType.MC,
) => {
    if (!event) {
        return;
    }

    const eventName = `b_waimai_e_bee_rn_assistant_${event}_${type}`;
    const cid = 'c_waimai_e_bee_rn_assistant_chat';
    if (type === TrackEventType.MC) {
        lxTrack.moduleClick(cid, eventName, obj);
        return;
    }
    lxTrack.moduleView(cid, eventName, obj);
};
