// rule.ts
import { StyleSheet, ViewStyle } from '@mrn/react-native';
import _ from 'lodash';

import { parseColor } from './util';

const rules1 = {
    square: (size = 20) => ({ width: size, height: size }),
    circle: _.memoize((size = 10) => ({
        width: size,
        height: size,
        borderRadius: size / 2,
    })),
    container: (backgroundColor = '#f5f5f5') =>
        ({
            backgroundColor: parseColor(backgroundColor),
            flex: 1,
            width: '100%',
        } as ViewStyle),
    rowContainer: ({} = {}) =>
        ({
            width: '100%',
            backgroundColor: '#fff',
            flexDirection: 'row',
            paddingHorizontal: 10,
            alignItems: 'center',
        } as ViewStyle),
    card: (backgroundColor = '#fff', padding = 12, margin = 12) => ({
        backgroundColor,
        padding,
        margin,
    }),
    row: () => ({ flexDirection: 'row' } as const),
    center: () => ({ justifyContent: 'center', alignItems: 'center' } as const),
    line: ({
        backgroundColor = '#999',
        width = '100%',
    }: { width?: string | number; backgroundColor?: string } = {}) => ({
        width,
        height: StyleSheet.hairlineWidth,
        backgroundColor,
        opacity: 0.3,
        marginTop: 4,
        marginBottom: 4,
    }),
    button: ({
        borderRadius = 16,
        borderColor = '#999',
        borderWidth = 1,
        paddingHorizontal = 20,
        paddingVertical = 5,
        alignItems = 'center',
        justifyContent = 'center',
    } = {}) => {
        return {
            borderRadius,
            borderColor,
            borderWidth,
            paddingHorizontal,
            paddingVertical,
            alignItems,
            justifyContent,
        } as ViewStyle;
    },
};

const defaultRules = { ...rules1 };
export default defaultRules;
