# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ampproject/remapping@^2.2.0":
  version "2.2.1"
  resolved "http://r.npm.sankuai.com/@ampproject/remapping/download/@ampproject/remapping-2.2.1.tgz#99e8e11851128b8702cd57c33684f1d0f260b630"
  integrity sha1-mejhGFESi4cCzVfDNoTx0PJgtjA=
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.0"
    "@jridgewell/trace-mapping" "^0.3.9"

"@ant-design/colors@^7.1.0":
  version "7.1.0"
  resolved "http://r.npm.sankuai.com/@ant-design/colors/download/@ant-design/colors-7.1.0.tgz#60eadfa2e21871d8948dac5d50b9f056062f8af3"
  integrity sha1-YOrfouIYcdiUjaxdULnwVgYvivM=
  dependencies:
    "@ctrl/tinycolor" "^3.6.1"

"@babel/code-frame@7.12.11":
  version "7.12.11"
  resolved "http://r.npm.sankuai.com/@babel/code-frame/download/@babel/code-frame-7.12.11.tgz#f4ad435aa263db935b8f10f2c552d23fb716a63f"
  integrity sha1-9K1DWqJj25NbjxDyxVLSP7cWpj8=
  dependencies:
    "@babel/highlight" "^7.10.4"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.18.6", "@babel/code-frame@^7.21.4":
  version "7.21.4"
  resolved "http://r.npm.sankuai.com/@babel/code-frame/download/@babel/code-frame-7.21.4.tgz#d0fa9e4413aca81f2b23b9442797bda1826edb39"
  integrity sha1-0PqeRBOsqB8rI7lEJ5e9oYJu2zk=
  dependencies:
    "@babel/highlight" "^7.18.6"

"@babel/compat-data@^7.17.7", "@babel/compat-data@^7.20.5", "@babel/compat-data@^7.21.4":
  version "7.21.4"
  resolved "http://r.npm.sankuai.com/@babel/compat-data/download/@babel/compat-data-7.21.4.tgz#457ffe647c480dff59c2be092fc3acf71195c87f"
  integrity sha1-RX/+ZHxIDf9Zwr4JL8Os9xGVyH8=

"@babel/core@^7.0.0", "@babel/core@^7.1.0", "@babel/core@^7.11.6", "@babel/core@^7.12.3", "@babel/core@^7.14.0", "@babel/core@^7.7.5":
  version "7.21.4"
  resolved "http://r.npm.sankuai.com/@babel/core/download/@babel/core-7.21.4.tgz#c6dc73242507b8e2a27fd13a9c1814f9fa34a659"
  integrity sha1-xtxzJCUHuOKif9E6nBgU+fo0plk=
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.21.4"
    "@babel/generator" "^7.21.4"
    "@babel/helper-compilation-targets" "^7.21.4"
    "@babel/helper-module-transforms" "^7.21.2"
    "@babel/helpers" "^7.21.0"
    "@babel/parser" "^7.21.4"
    "@babel/template" "^7.20.7"
    "@babel/traverse" "^7.21.4"
    "@babel/types" "^7.21.4"
    convert-source-map "^1.7.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.2"
    semver "^6.3.0"

"@babel/eslint-parser@^7.18.2":
  version "7.21.3"
  resolved "http://r.npm.sankuai.com/@babel/eslint-parser/download/@babel/eslint-parser-7.21.3.tgz#d79e822050f2de65d7f368a076846e7184234af7"
  integrity sha1-156CIFDy3mXX82igdoRucYQjSvc=
  dependencies:
    "@nicolo-ribaudo/eslint-scope-5-internals" "5.1.1-v1"
    eslint-visitor-keys "^2.1.0"
    semver "^6.3.0"

"@babel/generator@^7.21.4", "@babel/generator@^7.5.0":
  version "7.21.4"
  resolved "http://r.npm.sankuai.com/@babel/generator/download/@babel/generator-7.21.4.tgz#64a94b7448989f421f919d5239ef553b37bb26bc"
  integrity sha1-ZKlLdEiYn0IfkZ1SOe9VOze7Jrw=
  dependencies:
    "@babel/types" "^7.21.4"
    "@jridgewell/gen-mapping" "^0.3.2"
    "@jridgewell/trace-mapping" "^0.3.17"
    jsesc "^2.5.1"

"@babel/helper-annotate-as-pure@^7.18.6":
  version "7.18.6"
  resolved "http://r.npm.sankuai.com/@babel/helper-annotate-as-pure/download/@babel/helper-annotate-as-pure-7.18.6.tgz#eaa49f6f80d5a33f9a5dd2276e6d6e451be0a6bb"
  integrity sha1-6qSfb4DVoz+aXdInbm1uRRvgprs=
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-builder-binary-assignment-operator-visitor@^7.18.6":
  version "7.18.9"
  resolved "http://r.npm.sankuai.com/@babel/helper-builder-binary-assignment-operator-visitor/download/@babel/helper-builder-binary-assignment-operator-visitor-7.18.9.tgz#acd4edfd7a566d1d51ea975dff38fd52906981bb"
  integrity sha1-rNTt/XpWbR1R6pdd/zj9UpBpgbs=
  dependencies:
    "@babel/helper-explode-assignable-expression" "^7.18.6"
    "@babel/types" "^7.18.9"

"@babel/helper-compilation-targets@^7.17.7", "@babel/helper-compilation-targets@^7.18.9", "@babel/helper-compilation-targets@^7.20.7", "@babel/helper-compilation-targets@^7.21.4":
  version "7.21.4"
  resolved "http://r.npm.sankuai.com/@babel/helper-compilation-targets/download/@babel/helper-compilation-targets-7.21.4.tgz#770cd1ce0889097ceacb99418ee6934ef0572656"
  integrity sha1-dwzRzgiJCXzqy5lBjuaTTvBXJlY=
  dependencies:
    "@babel/compat-data" "^7.21.4"
    "@babel/helper-validator-option" "^7.21.0"
    browserslist "^4.21.3"
    lru-cache "^5.1.1"
    semver "^6.3.0"

"@babel/helper-create-class-features-plugin@^7.18.6", "@babel/helper-create-class-features-plugin@^7.21.0":
  version "7.21.4"
  resolved "http://r.npm.sankuai.com/@babel/helper-create-class-features-plugin/download/@babel/helper-create-class-features-plugin-7.21.4.tgz#3a017163dc3c2ba7deb9a7950849a9586ea24c18"
  integrity sha1-OgFxY9w8K6feuaeVCEmpWG6iTBg=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-function-name" "^7.21.0"
    "@babel/helper-member-expression-to-functions" "^7.21.0"
    "@babel/helper-optimise-call-expression" "^7.18.6"
    "@babel/helper-replace-supers" "^7.20.7"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.20.0"
    "@babel/helper-split-export-declaration" "^7.18.6"

"@babel/helper-create-regexp-features-plugin@^7.18.6":
  version "7.21.4"
  resolved "http://r.npm.sankuai.com/@babel/helper-create-regexp-features-plugin/download/@babel/helper-create-regexp-features-plugin-7.21.4.tgz#40411a8ab134258ad2cf3a3d987ec6aa0723cee5"
  integrity sha1-QEEairE0JYrSzzo9mH7GqgcjzuU=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    regexpu-core "^5.3.1"

"@babel/helper-define-polyfill-provider@^0.3.3":
  version "0.3.3"
  resolved "http://r.npm.sankuai.com/@babel/helper-define-polyfill-provider/download/@babel/helper-define-polyfill-provider-0.3.3.tgz#8612e55be5d51f0cd1f36b4a5a83924e89884b7a"
  integrity sha1-hhLlW+XVHwzR82tKWoOSTomIS3o=
  dependencies:
    "@babel/helper-compilation-targets" "^7.17.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    debug "^4.1.1"
    lodash.debounce "^4.0.8"
    resolve "^1.14.2"
    semver "^6.1.2"

"@babel/helper-environment-visitor@^7.18.9":
  version "7.18.9"
  resolved "http://r.npm.sankuai.com/@babel/helper-environment-visitor/download/@babel/helper-environment-visitor-7.18.9.tgz#0c0cee9b35d2ca190478756865bb3528422f51be"
  integrity sha1-DAzumzXSyhkEeHVoZbs1KEIvUb4=

"@babel/helper-explode-assignable-expression@^7.18.6":
  version "7.18.6"
  resolved "http://r.npm.sankuai.com/@babel/helper-explode-assignable-expression/download/@babel/helper-explode-assignable-expression-7.18.6.tgz#41f8228ef0a6f1a036b8dfdfec7ce94f9a6bc096"
  integrity sha1-QfgijvCm8aA2uN/f7HzpT5prwJY=
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-function-name@^7.18.9", "@babel/helper-function-name@^7.21.0":
  version "7.21.0"
  resolved "http://r.npm.sankuai.com/@babel/helper-function-name/download/@babel/helper-function-name-7.21.0.tgz#d552829b10ea9f120969304023cd0645fa00b1b4"
  integrity sha1-1VKCmxDqnxIJaTBAI80GRfoAsbQ=
  dependencies:
    "@babel/template" "^7.20.7"
    "@babel/types" "^7.21.0"

"@babel/helper-hoist-variables@^7.18.6":
  version "7.18.6"
  resolved "http://r.npm.sankuai.com/@babel/helper-hoist-variables/download/@babel/helper-hoist-variables-7.18.6.tgz#d4d2c8fb4baeaa5c68b99cc8245c56554f926678"
  integrity sha1-1NLI+0uuqlxouZzIJFxWVU+SZng=
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-member-expression-to-functions@^7.20.7", "@babel/helper-member-expression-to-functions@^7.21.0":
  version "7.21.0"
  resolved "http://r.npm.sankuai.com/@babel/helper-member-expression-to-functions/download/@babel/helper-member-expression-to-functions-7.21.0.tgz#319c6a940431a133897148515877d2f3269c3ba5"
  integrity sha1-MZxqlAQxoTOJcUhRWHfS8yacO6U=
  dependencies:
    "@babel/types" "^7.21.0"

"@babel/helper-module-imports@^7.18.6", "@babel/helper-module-imports@^7.21.4":
  version "7.21.4"
  resolved "http://r.npm.sankuai.com/@babel/helper-module-imports/download/@babel/helper-module-imports-7.21.4.tgz#ac88b2f76093637489e718a90cec6cf8a9b029af"
  integrity sha1-rIiy92CTY3SJ5xipDOxs+KmwKa8=
  dependencies:
    "@babel/types" "^7.21.4"

"@babel/helper-module-transforms@^7.21.2":
  version "7.21.2"
  resolved "http://r.npm.sankuai.com/@babel/helper-module-transforms/download/@babel/helper-module-transforms-7.21.2.tgz#160caafa4978ac8c00ac66636cb0fa37b024e2d2"
  integrity sha1-Fgyq+kl4rIwArGZjbLD6N7Ak4tI=
  dependencies:
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-module-imports" "^7.18.6"
    "@babel/helper-simple-access" "^7.20.2"
    "@babel/helper-split-export-declaration" "^7.18.6"
    "@babel/helper-validator-identifier" "^7.19.1"
    "@babel/template" "^7.20.7"
    "@babel/traverse" "^7.21.2"
    "@babel/types" "^7.21.2"

"@babel/helper-optimise-call-expression@^7.18.6":
  version "7.18.6"
  resolved "http://r.npm.sankuai.com/@babel/helper-optimise-call-expression/download/@babel/helper-optimise-call-expression-7.18.6.tgz#9369aa943ee7da47edab2cb4e838acf09d290ffe"
  integrity sha1-k2mqlD7n2kftqyy06Dis8J0pD/4=
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.12.13", "@babel/helper-plugin-utils@^7.14.5", "@babel/helper-plugin-utils@^7.16.7", "@babel/helper-plugin-utils@^7.18.6", "@babel/helper-plugin-utils@^7.18.9", "@babel/helper-plugin-utils@^7.19.0", "@babel/helper-plugin-utils@^7.20.2", "@babel/helper-plugin-utils@^7.8.0":
  version "7.20.2"
  resolved "http://r.npm.sankuai.com/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.20.2.tgz#d1b9000752b18d0877cff85a5c376ce5c3121629"
  integrity sha1-0bkAB1KxjQh3z/haXDds5cMSFik=

"@babel/helper-replace-supers@^7.18.6", "@babel/helper-replace-supers@^7.20.7":
  version "7.20.7"
  resolved "http://r.npm.sankuai.com/@babel/helper-replace-supers/download/@babel/helper-replace-supers-7.20.7.tgz#243ecd2724d2071532b2c8ad2f0f9f083bcae331"
  integrity sha1-JD7NJyTSBxUyssitLw+fCDvK4zE=
  dependencies:
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-member-expression-to-functions" "^7.20.7"
    "@babel/helper-optimise-call-expression" "^7.18.6"
    "@babel/template" "^7.20.7"
    "@babel/traverse" "^7.20.7"
    "@babel/types" "^7.20.7"

"@babel/helper-simple-access@^7.20.2":
  version "7.20.2"
  resolved "http://r.npm.sankuai.com/@babel/helper-simple-access/download/@babel/helper-simple-access-7.20.2.tgz#0ab452687fe0c2cfb1e2b9e0015de07fc2d62dd9"
  integrity sha1-CrRSaH/gws+x4rngAV3gf8LWLdk=
  dependencies:
    "@babel/types" "^7.20.2"

"@babel/helper-skip-transparent-expression-wrappers@^7.20.0":
  version "7.20.0"
  resolved "http://r.npm.sankuai.com/@babel/helper-skip-transparent-expression-wrappers/download/@babel/helper-skip-transparent-expression-wrappers-7.20.0.tgz#fbe4c52f60518cab8140d77101f0e63a8a230684"
  integrity sha1-++TFL2BRjKuBQNdxAfDmOoojBoQ=
  dependencies:
    "@babel/types" "^7.20.0"

"@babel/helper-split-export-declaration@^7.18.6":
  version "7.18.6"
  resolved "http://r.npm.sankuai.com/@babel/helper-split-export-declaration/download/@babel/helper-split-export-declaration-7.18.6.tgz#7367949bc75b20c6d5a5d4a97bba2824ae8ef075"
  integrity sha1-c2eUm8dbIMbVpdSpe7ooJK6O8HU=
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-string-parser@^7.19.4":
  version "7.19.4"
  resolved "http://r.npm.sankuai.com/@babel/helper-string-parser/download/@babel/helper-string-parser-7.19.4.tgz#38d3acb654b4701a9b77fb0615a96f775c3a9e63"
  integrity sha1-ONOstlS0cBqbd/sGFalvd1w6nmM=

"@babel/helper-validator-identifier@^7.18.6", "@babel/helper-validator-identifier@^7.19.1":
  version "7.19.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.19.1.tgz#7eea834cf32901ffdc1a7ee555e2f9c27e249ca2"
  integrity sha1-fuqDTPMpAf/cGn7lVeL5wn4knKI=

"@babel/helper-validator-option@^7.21.0":
  version "7.21.0"
  resolved "http://r.npm.sankuai.com/@babel/helper-validator-option/download/@babel/helper-validator-option-7.21.0.tgz#8224c7e13ace4bafdc4004da2cf064ef42673180"
  integrity sha1-giTH4TrOS6/cQATaLPBk70JnMYA=

"@babel/helpers@^7.21.0":
  version "7.21.0"
  resolved "http://r.npm.sankuai.com/@babel/helpers/download/@babel/helpers-7.21.0.tgz#9dd184fb5599862037917cdc9eecb84577dc4e7e"
  integrity sha1-ndGE+1WZhiA3kXzcnuy4RXfcTn4=
  dependencies:
    "@babel/template" "^7.20.7"
    "@babel/traverse" "^7.21.0"
    "@babel/types" "^7.21.0"

"@babel/highlight@^7.10.4", "@babel/highlight@^7.18.6":
  version "7.18.6"
  resolved "http://r.npm.sankuai.com/@babel/highlight/download/@babel/highlight-7.18.6.tgz#81158601e93e2563795adcbfbdf5d64be3f2ecdf"
  integrity sha1-gRWGAek+JWN5Wty/vfXWS+Py7N8=
  dependencies:
    "@babel/helper-validator-identifier" "^7.18.6"
    chalk "^2.0.0"
    js-tokens "^4.0.0"

"@babel/parser@^7.0.0", "@babel/parser@^7.1.0", "@babel/parser@^7.14.7", "@babel/parser@^7.20.7", "@babel/parser@^7.21.4", "@babel/parser@^7.7.0":
  version "7.21.4"
  resolved "http://r.npm.sankuai.com/@babel/parser/download/@babel/parser-7.21.4.tgz#94003fdfc520bbe2875d4ae557b43ddb6d880f17"
  integrity sha1-lAA/38Ugu+KHXUrlV7Q9222IDxc=

"@babel/plugin-external-helpers@^7.0.0":
  version "7.18.6"
  resolved "http://r.npm.sankuai.com/@babel/plugin-external-helpers/download/@babel/plugin-external-helpers-7.18.6.tgz#58f2a6eca8ad05bc130de8cec569c43dc19b6deb"
  integrity sha1-WPKm7KitBbwTDejOxWnEPcGbbes=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-proposal-class-properties@^7.0.0":
  version "7.18.6"
  resolved "http://r.npm.sankuai.com/@babel/plugin-proposal-class-properties/download/@babel/plugin-proposal-class-properties-7.18.6.tgz#b110f59741895f7ec21a6fff696ec46265c446a3"
  integrity sha1-sRD1l0GJX37CGm//aW7EYmXERqM=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-proposal-export-default-from@^7.0.0":
  version "7.18.10"
  resolved "http://r.npm.sankuai.com/@babel/plugin-proposal-export-default-from/download/@babel/plugin-proposal-export-default-from-7.18.10.tgz#091f4794dbce4027c03cf4ebc64d3fb96b75c206"
  integrity sha1-CR9HlNvOQCfAPPTrxk0/uWt1wgY=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"
    "@babel/plugin-syntax-export-default-from" "^7.18.6"

"@babel/plugin-proposal-nullish-coalescing-operator@^7.0.0":
  version "7.18.6"
  resolved "http://r.npm.sankuai.com/@babel/plugin-proposal-nullish-coalescing-operator/download/@babel/plugin-proposal-nullish-coalescing-operator-7.18.6.tgz#fdd940a99a740e577d6c753ab6fbb43fdb9467e1"
  integrity sha1-/dlAqZp0Dld9bHU6tvu0P9uUZ+E=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"

"@babel/plugin-proposal-object-rest-spread@^7.0.0":
  version "7.20.7"
  resolved "http://r.npm.sankuai.com/@babel/plugin-proposal-object-rest-spread/download/@babel/plugin-proposal-object-rest-spread-7.20.7.tgz#aa662940ef425779c75534a5c41e9d936edc390a"
  integrity sha1-qmYpQO9CV3nHVTSlxB6dk27cOQo=
  dependencies:
    "@babel/compat-data" "^7.20.5"
    "@babel/helper-compilation-targets" "^7.20.7"
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-transform-parameters" "^7.20.7"

"@babel/plugin-proposal-optional-catch-binding@^7.0.0":
  version "7.18.6"
  resolved "http://r.npm.sankuai.com/@babel/plugin-proposal-optional-catch-binding/download/@babel/plugin-proposal-optional-catch-binding-7.18.6.tgz#f9400d0e6a3ea93ba9ef70b09e72dd6da638a2cb"
  integrity sha1-+UANDmo+qTup73CwnnLdbaY4oss=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"

"@babel/plugin-proposal-optional-chaining@^7.0.0":
  version "7.21.0"
  resolved "http://r.npm.sankuai.com/@babel/plugin-proposal-optional-chaining/download/@babel/plugin-proposal-optional-chaining-7.21.0.tgz#886f5c8978deb7d30f678b2e24346b287234d3ea"
  integrity sha1-iG9ciXjet9MPZ4suJDRrKHI00+o=
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.20.0"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"

"@babel/plugin-syntax-async-generators@^7.8.4":
  version "7.8.4"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-async-generators/download/@babel/plugin-syntax-async-generators-7.8.4.tgz#a983fb1aeb2ec3f6ed042a210f640e90e786fe0d"
  integrity sha1-qYP7Gusuw/btBCohD2QOkOeG/g0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-bigint@^7.8.3":
  version "7.8.3"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-bigint/download/@babel/plugin-syntax-bigint-7.8.3.tgz#4c9a6f669f5d0cdf1b90a1671e9a146be5300cea"
  integrity sha1-TJpvZp9dDN8bkKFnHpoUa+UwDOo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.0.0", "@babel/plugin-syntax-class-properties@^7.8.3":
  version "7.12.13"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-class-properties/download/@babel/plugin-syntax-class-properties-7.12.13.tgz#b5c987274c4a3a82b89714796931a6b53544ae10"
  integrity sha1-tcmHJ0xKOoK4lxR5aTGmtTVErhA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-dynamic-import@^7.0.0":
  version "7.8.3"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-dynamic-import/download/@babel/plugin-syntax-dynamic-import-7.8.3.tgz#62bf98b2da3cd21d626154fc96ee5b3cb68eacb3"
  integrity sha1-Yr+Ysto80h1iYVT8lu5bPLaOrLM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-export-default-from@^7.0.0", "@babel/plugin-syntax-export-default-from@^7.18.6":
  version "7.18.6"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-export-default-from/download/@babel/plugin-syntax-export-default-from-7.18.6.tgz#8df076711a4818c4ce4f23e61d622b0ba2ff84bc"
  integrity sha1-jfB2cRpIGMTOTyPmHWIrC6L/hLw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-syntax-flow@^7.0.0", "@babel/plugin-syntax-flow@^7.18.6", "@babel/plugin-syntax-flow@^7.2.0":
  version "7.21.4"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-flow/download/@babel/plugin-syntax-flow-7.21.4.tgz#3e37fca4f06d93567c1cd9b75156422e90a67107"
  integrity sha1-Pjf8pPBtk1Z8HNm3UVZCLpCmcQc=
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"

"@babel/plugin-syntax-import-meta@^7.8.3":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-import-meta/download/@babel/plugin-syntax-import-meta-7.10.4.tgz#ee601348c370fa334d2207be158777496521fd51"
  integrity sha1-7mATSMNw+jNNIge+FYd3SWUh/VE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-json-strings@^7.8.3":
  version "7.8.3"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-json-strings/download/@babel/plugin-syntax-json-strings-7.8.3.tgz#01ca21b668cd8218c9e640cb6dd88c5412b2c96a"
  integrity sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-jsx@^7.0.0", "@babel/plugin-syntax-jsx@^7.18.6", "@babel/plugin-syntax-jsx@^7.21.4":
  version "7.21.4"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-jsx/download/@babel/plugin-syntax-jsx-7.21.4.tgz#f264ed7bf40ffc9ec239edabc17a50c4f5b6fea2"
  integrity sha1-8mTte/QP/J7COe2rwXpQxPW2/qI=
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"

"@babel/plugin-syntax-logical-assignment-operators@^7.8.3":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-logical-assignment-operators/download/@babel/plugin-syntax-logical-assignment-operators-7.10.4.tgz#ca91ef46303530448b906652bac2e9fe9941f699"
  integrity sha1-ypHvRjA1MESLkGZSusLp/plB9pk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.0.0", "@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  version "7.8.3"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-nullish-coalescing-operator/download/@babel/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz#167ed70368886081f74b5c36c65a88c03b66d1a9"
  integrity sha1-Fn7XA2iIYIH3S1w2xlqIwDtm0ak=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.8.3":
  version "7.10.4"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-numeric-separator/download/@babel/plugin-syntax-numeric-separator-7.10.4.tgz#b9b070b3e33570cd9fd07ba7fa91c0dd37b9af97"
  integrity sha1-ubBws+M1cM2f0Hun+pHA3Te5r5c=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.0.0", "@babel/plugin-syntax-object-rest-spread@^7.8.3":
  version "7.8.3"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-object-rest-spread/download/@babel/plugin-syntax-object-rest-spread-7.8.3.tgz#60e225edcbd98a640332a2e72dd3e66f1af55871"
  integrity sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  version "7.8.3"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-optional-catch-binding/download/@babel/plugin-syntax-optional-catch-binding-7.8.3.tgz#6111a265bcfb020eb9efd0fdfd7d26402b9ed6c1"
  integrity sha1-YRGiZbz7Ag6579D9/X0mQCue1sE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.0.0", "@babel/plugin-syntax-optional-chaining@^7.8.3":
  version "7.8.3"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-optional-chaining/download/@babel/plugin-syntax-optional-chaining-7.8.3.tgz#4f69c2ab95167e0180cd5336613f8c5788f7d48a"
  integrity sha1-T2nCq5UWfgGAzVM2YT+MV4j31Io=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-top-level-await@^7.8.3":
  version "7.14.5"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-top-level-await/download/@babel/plugin-syntax-top-level-await-7.14.5.tgz#c1cfdadc35a646240001f06138247b741c34d94c"
  integrity sha1-wc/a3DWmRiQAAfBhOCR7dBw02Uw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-typescript@^7.20.0":
  version "7.21.4"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-typescript/download/@babel/plugin-syntax-typescript-7.21.4.tgz#2751948e9b7c6d771a8efa59340c15d4a2891ff8"
  integrity sha1-J1GUjpt8bXcajvpZNAwV1KKJH/g=
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"

"@babel/plugin-transform-arrow-functions@^7.0.0":
  version "7.20.7"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-arrow-functions/download/@babel/plugin-transform-arrow-functions-7.20.7.tgz#bea332b0e8b2dab3dafe55a163d8227531ab0551"
  integrity sha1-vqMysOiy2rPa/lWhY9gidTGrBVE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"

"@babel/plugin-transform-block-scoped-functions@^7.0.0":
  version "7.18.6"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-block-scoped-functions/download/@babel/plugin-transform-block-scoped-functions-7.18.6.tgz#9187bf4ba302635b9d70d986ad70f038726216a8"
  integrity sha1-kYe/S6MCY1udcNmGrXDwOHJiFqg=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-block-scoping@^7.0.0":
  version "7.21.0"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-block-scoping/download/@babel/plugin-transform-block-scoping-7.21.0.tgz#e737b91037e5186ee16b76e7ae093358a5634f02"
  integrity sha1-5ze5EDflGG7ha3bnrgkzWKVjTwI=
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"

"@babel/plugin-transform-classes@^7.0.0":
  version "7.21.0"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-classes/download/@babel/plugin-transform-classes-7.21.0.tgz#f469d0b07a4c5a7dbb21afad9e27e57b47031665"
  integrity sha1-9GnQsHpMWn27Ia+tnifle0cDFmU=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    "@babel/helper-compilation-targets" "^7.20.7"
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-function-name" "^7.21.0"
    "@babel/helper-optimise-call-expression" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/helper-replace-supers" "^7.20.7"
    "@babel/helper-split-export-declaration" "^7.18.6"
    globals "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.0.0":
  version "7.20.7"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-computed-properties/download/@babel/plugin-transform-computed-properties-7.20.7.tgz#704cc2fd155d1c996551db8276d55b9d46e4d0aa"
  integrity sha1-cEzC/RVdHJllUduCdtVbnUbk0Ko=
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/template" "^7.20.7"

"@babel/plugin-transform-destructuring@^7.0.0":
  version "7.21.3"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-destructuring/download/@babel/plugin-transform-destructuring-7.21.3.tgz#73b46d0fd11cd6ef57dea8a381b1215f4959d401"
  integrity sha1-c7RtD9Ec1u9X3qijgbEhX0lZ1AE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"

"@babel/plugin-transform-exponentiation-operator@^7.0.0":
  version "7.18.6"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-exponentiation-operator/download/@babel/plugin-transform-exponentiation-operator-7.18.6.tgz#421c705f4521888c65e91fdd1af951bfefd4dacd"
  integrity sha1-QhxwX0UhiIxl6R/dGvlRv+/U2s0=
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-flow-strip-types@^7.0.0":
  version "7.21.0"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-flow-strip-types/download/@babel/plugin-transform-flow-strip-types-7.21.0.tgz#6aeca0adcb81dc627c8986e770bfaa4d9812aff5"
  integrity sha1-auygrcuB3GJ8iYbncL+qTZgSr/U=
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/plugin-syntax-flow" "^7.18.6"

"@babel/plugin-transform-for-of@^7.0.0":
  version "7.21.0"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-for-of/download/@babel/plugin-transform-for-of-7.21.0.tgz#964108c9988de1a60b4be2354a7d7e245f36e86e"
  integrity sha1-lkEIyZiN4aYLS+I1Sn1+JF826G4=
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"

"@babel/plugin-transform-function-name@^7.0.0":
  version "7.18.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-function-name/download/@babel/plugin-transform-function-name-7.18.9.tgz#cc354f8234e62968946c61a46d6365440fc764e0"
  integrity sha1-zDVPgjTmKWiUbGGkbWNlRA/HZOA=
  dependencies:
    "@babel/helper-compilation-targets" "^7.18.9"
    "@babel/helper-function-name" "^7.18.9"
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-literals@^7.0.0":
  version "7.18.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-literals/download/@babel/plugin-transform-literals-7.18.9.tgz#72796fdbef80e56fba3c6a699d54f0de557444bc"
  integrity sha1-cnlv2++A5W+6PGppnVTw3lV0RLw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-member-expression-literals@^7.0.0":
  version "7.18.6"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-member-expression-literals/download/@babel/plugin-transform-member-expression-literals-7.18.6.tgz#ac9fdc1a118620ac49b7e7a5d2dc177a1bfee88e"
  integrity sha1-rJ/cGhGGIKxJt+el0twXehv+6I4=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-modules-commonjs@^7.0.0", "@babel/plugin-transform-modules-commonjs@^7.21.2":
  version "7.21.2"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-modules-commonjs/download/@babel/plugin-transform-modules-commonjs-7.21.2.tgz#6ff5070e71e3192ef2b7e39820a06fb78e3058e7"
  integrity sha1-b/UHDnHjGS7yt+OYIKBvt44wWOc=
  dependencies:
    "@babel/helper-module-transforms" "^7.21.2"
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/helper-simple-access" "^7.20.2"

"@babel/plugin-transform-object-assign@^7.0.0":
  version "7.18.6"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-object-assign/download/@babel/plugin-transform-object-assign-7.18.6.tgz#7830b4b6f83e1374a5afb9f6111bcfaea872cdd2"
  integrity sha1-eDC0tvg+E3Slr7n2ERvPrqhyzdI=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-object-super@^7.0.0":
  version "7.18.6"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-object-super/download/@babel/plugin-transform-object-super-7.18.6.tgz#fb3c6ccdd15939b6ff7939944b51971ddc35912c"
  integrity sha1-+zxszdFZObb/eTmUS1GXHdw1kSw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/helper-replace-supers" "^7.18.6"

"@babel/plugin-transform-parameters@^7.0.0", "@babel/plugin-transform-parameters@^7.20.7":
  version "7.21.3"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-parameters/download/@babel/plugin-transform-parameters-7.21.3.tgz#18fc4e797cf6d6d972cb8c411dbe8a809fa157db"
  integrity sha1-GPxOeXz21tlyy4xBHb6KgJ+hV9s=
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"

"@babel/plugin-transform-property-literals@^7.0.0":
  version "7.18.6"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-property-literals/download/@babel/plugin-transform-property-literals-7.18.6.tgz#e22498903a483448e94e032e9bbb9c5ccbfc93a3"
  integrity sha1-4iSYkDpINEjpTgMum7ucXMv8k6M=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-react-display-name@^7.0.0":
  version "7.18.6"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-react-display-name/download/@babel/plugin-transform-react-display-name-7.18.6.tgz#8b1125f919ef36ebdfff061d664e266c666b9415"
  integrity sha1-ixEl+RnvNuvf/wYdZk4mbGZrlBU=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-react-jsx-self@^7.0.0":
  version "7.21.0"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-react-jsx-self/download/@babel/plugin-transform-react-jsx-self-7.21.0.tgz#ec98d4a9baafc5a1eb398da4cf94afbb40254a54"
  integrity sha1-7JjUqbqvxaHrOY2kz5Svu0AlSlQ=
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"

"@babel/plugin-transform-react-jsx-source@^7.0.0":
  version "7.19.6"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-react-jsx-source/download/@babel/plugin-transform-react-jsx-source-7.19.6.tgz#88578ae8331e5887e8ce28e4c9dc83fb29da0b86"
  integrity sha1-iFeK6DMeWIfozijkydyD+ynaC4Y=
  dependencies:
    "@babel/helper-plugin-utils" "^7.19.0"

"@babel/plugin-transform-react-jsx@^7.0.0":
  version "7.21.0"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-react-jsx/download/@babel/plugin-transform-react-jsx-7.21.0.tgz#656b42c2fdea0a6d8762075d58ef9d4e3c4ab8a2"
  integrity sha1-ZWtCwv3qCm2HYgddWO+dTjxKuKI=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    "@babel/helper-module-imports" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/plugin-syntax-jsx" "^7.18.6"
    "@babel/types" "^7.21.0"

"@babel/plugin-transform-regenerator@^7.0.0":
  version "7.20.5"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-regenerator/download/@babel/plugin-transform-regenerator-7.20.5.tgz#57cda588c7ffb7f4f8483cc83bdcea02a907f04d"
  integrity sha1-V82liMf/t/T4SDzIO9zqAqkH8E0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"
    regenerator-transform "^0.15.1"

"@babel/plugin-transform-runtime@^7.0.0":
  version "7.21.4"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-runtime/download/@babel/plugin-transform-runtime-7.21.4.tgz#2e1da21ca597a7d01fc96b699b21d8d2023191aa"
  integrity sha1-Lh2iHKWXp9AfyWtpmyHY0gIxkao=
  dependencies:
    "@babel/helper-module-imports" "^7.21.4"
    "@babel/helper-plugin-utils" "^7.20.2"
    babel-plugin-polyfill-corejs2 "^0.3.3"
    babel-plugin-polyfill-corejs3 "^0.6.0"
    babel-plugin-polyfill-regenerator "^0.4.1"
    semver "^6.3.0"

"@babel/plugin-transform-shorthand-properties@^7.0.0":
  version "7.18.6"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-shorthand-properties/download/@babel/plugin-transform-shorthand-properties-7.18.6.tgz#6d6df7983d67b195289be24909e3f12a8f664dc9"
  integrity sha1-bW33mD1nsZUom+JJCePxKo9mTck=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-spread@^7.0.0":
  version "7.20.7"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-spread/download/@babel/plugin-transform-spread-7.20.7.tgz#c2d83e0b99d3bf83e07b11995ee24bf7ca09401e"
  integrity sha1-wtg+C5nTv4PgexGZXuJL98oJQB4=
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.20.0"

"@babel/plugin-transform-sticky-regex@^7.0.0":
  version "7.18.6"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-sticky-regex/download/@babel/plugin-transform-sticky-regex-7.18.6.tgz#c6706eb2b1524028e317720339583ad0f444adcc"
  integrity sha1-xnBusrFSQCjjF3IDOVg60PRErcw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-template-literals@^7.0.0":
  version "7.18.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-template-literals/download/@babel/plugin-transform-template-literals-7.18.9.tgz#04ec6f10acdaa81846689d63fae117dd9c243a5e"
  integrity sha1-BOxvEKzaqBhGaJ1j+uEX3ZwkOl4=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-typescript@^7.21.3", "@babel/plugin-transform-typescript@^7.5.0":
  version "7.21.3"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-typescript/download/@babel/plugin-transform-typescript-7.21.3.tgz#316c5be579856ea890a57ebc5116c5d064658f2b"
  integrity sha1-MWxb5XmFbqiQpX68URbF0GRljys=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    "@babel/helper-create-class-features-plugin" "^7.21.0"
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/plugin-syntax-typescript" "^7.20.0"

"@babel/plugin-transform-unicode-regex@^7.0.0":
  version "7.18.6"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-unicode-regex/download/@babel/plugin-transform-unicode-regex-7.18.6.tgz#194317225d8c201bbae103364ffe9e2cea36cdca"
  integrity sha1-GUMXIl2MIBu64QM2T/6eLOo2zco=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/preset-typescript@^7.10.4":
  version "7.21.4"
  resolved "http://r.npm.sankuai.com/@babel/preset-typescript/download/@babel/preset-typescript-7.21.4.tgz#b913ac8e6aa8932e47c21b01b4368d8aa239a529"
  integrity sha1-uROsjmqoky5HwhsBtDaNiqI5pSk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/helper-validator-option" "^7.21.0"
    "@babel/plugin-syntax-jsx" "^7.21.4"
    "@babel/plugin-transform-modules-commonjs" "^7.21.2"
    "@babel/plugin-transform-typescript" "^7.21.3"

"@babel/register@^7.0.0":
  version "7.21.0"
  resolved "http://r.npm.sankuai.com/@babel/register/download/@babel/register-7.21.0.tgz#c97bf56c2472e063774f31d344c592ebdcefa132"
  integrity sha1-yXv1bCRy4GN3TzHTRMWS69zvoTI=
  dependencies:
    clone-deep "^4.0.1"
    find-cache-dir "^2.0.0"
    make-dir "^2.1.0"
    pirates "^4.0.5"
    source-map-support "^0.5.16"

"@babel/regjsgen@^0.8.0":
  version "0.8.0"
  resolved "http://r.npm.sankuai.com/@babel/regjsgen/download/@babel/regjsgen-0.8.0.tgz#f0ba69b075e1f05fb2825b7fad991e7adbb18310"
  integrity sha1-8LppsHXh8F+yglt/rZkeetuxgxA=

"@babel/runtime@^7.11.2", "@babel/runtime@^7.5.5", "@babel/runtime@^7.8.4", "@babel/runtime@^7.9.2":
  version "7.21.0"
  resolved "http://r.npm.sankuai.com/@babel/runtime/download/@babel/runtime-7.21.0.tgz#5b55c9d394e5fcf304909a8b00c07dc217b56673"
  integrity sha1-W1XJ05Tl/PMEkJqLAMB9whe1ZnM=
  dependencies:
    regenerator-runtime "^0.13.11"

"@babel/runtime@^7.18.9", "@babel/runtime@^7.21.0":
  version "7.23.5"
  resolved "http://r.npm.sankuai.com/@babel/runtime/download/@babel/runtime-7.23.5.tgz#11edb98f8aeec529b82b211028177679144242db"
  integrity sha1-Ee25j4ruxSm4KyEQKBd2eRRCQts=
  dependencies:
    regenerator-runtime "^0.14.0"

"@babel/template@^7.0.0", "@babel/template@^7.20.7", "@babel/template@^7.3.3":
  version "7.20.7"
  resolved "http://r.npm.sankuai.com/@babel/template/download/@babel/template-7.20.7.tgz#a15090c2839a83b02aa996c0b4994005841fd5a8"
  integrity sha1-oVCQwoOag7AqqZbAtJlABYQf1ag=
  dependencies:
    "@babel/code-frame" "^7.18.6"
    "@babel/parser" "^7.20.7"
    "@babel/types" "^7.20.7"

"@babel/traverse@^7.0.0", "@babel/traverse@^7.1.0", "@babel/traverse@^7.20.7", "@babel/traverse@^7.21.0", "@babel/traverse@^7.21.2", "@babel/traverse@^7.21.4", "@babel/traverse@^7.7.0", "@babel/traverse@^7.7.4":
  version "7.21.4"
  resolved "http://r.npm.sankuai.com/@babel/traverse/download/@babel/traverse-7.21.4.tgz#a836aca7b116634e97a6ed99976236b3282c9d36"
  integrity sha1-qDasp7EWY06Xpu2Zl2I2sygsnTY=
  dependencies:
    "@babel/code-frame" "^7.21.4"
    "@babel/generator" "^7.21.4"
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-function-name" "^7.21.0"
    "@babel/helper-hoist-variables" "^7.18.6"
    "@babel/helper-split-export-declaration" "^7.18.6"
    "@babel/parser" "^7.21.4"
    "@babel/types" "^7.21.4"
    debug "^4.1.0"
    globals "^11.1.0"

"@babel/types@^7.0.0", "@babel/types@^7.12.6", "@babel/types@^7.18.6", "@babel/types@^7.18.9", "@babel/types@^7.20.0", "@babel/types@^7.20.2", "@babel/types@^7.20.7", "@babel/types@^7.21.0", "@babel/types@^7.21.2", "@babel/types@^7.21.4", "@babel/types@^7.3.0", "@babel/types@^7.3.3", "@babel/types@^7.7.0":
  version "7.21.4"
  resolved "http://r.npm.sankuai.com/@babel/types/download/@babel/types-7.21.4.tgz#2d5d6bb7908699b3b416409ffd3b5daa25b030d4"
  integrity sha1-LV1rt5CGmbO0FkCf/TtdqiWwMNQ=
  dependencies:
    "@babel/helper-string-parser" "^7.19.4"
    "@babel/helper-validator-identifier" "^7.19.1"
    to-fast-properties "^2.0.0"

"@bcoe/v8-coverage@^0.2.3":
  version "0.2.3"
  resolved "http://r.npm.sankuai.com/@bcoe/v8-coverage/download/@bcoe/v8-coverage-0.2.3.tgz#75a2e8b51cb758a7553d6804a5932d7aace75c39"
  integrity sha1-daLotRy3WKdVPWgEpZMteqznXDk=

"@cnakazawa/watch@^1.0.3":
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/@cnakazawa/watch/download/@cnakazawa/watch-1.0.4.tgz#f864ae85004d0fcab6f50be9141c4da368d1656a"
  integrity sha1-+GSuhQBND8q29QvpFBxNo2jRZWo=
  dependencies:
    exec-sh "^0.3.2"
    minimist "^1.2.0"

"@ctrl/tinycolor@^3.6.1":
  version "3.6.1"
  resolved "http://r.npm.sankuai.com/@ctrl/tinycolor/download/@ctrl/tinycolor-3.6.1.tgz#b6c75a56a1947cc916ea058772d666a2c8932f31"
  integrity sha1-tsdaVqGUfMkW6gWHctZmosiTLzE=

"@dp/weixin-js-sdk-loader@^0.4.6":
  version "0.4.24"
  resolved "http://r.npm.sankuai.com/@dp/weixin-js-sdk-loader/download/@dp/weixin-js-sdk-loader-0.4.24.tgz#287c1a8dd4c6605047aa20bb17f382859a23cde3"
  integrity sha1-KHwajdTGYFBHqiC7F/OChZojzeM=

"@eslint-community/eslint-utils@^4.2.0":
  version "4.4.0"
  resolved "http://r.npm.sankuai.com/@eslint-community/eslint-utils/download/@eslint-community/eslint-utils-4.4.0.tgz#a23514e8fb9af1269d5f7788aa556798d61c6b59"
  integrity sha1-ojUU6Pua8SadX3eIqlVnmNYca1k=
  dependencies:
    eslint-visitor-keys "^3.3.0"

"@eslint-community/regexpp@^4.4.0":
  version "4.5.0"
  resolved "http://r.npm.sankuai.com/@eslint-community/regexpp/download/@eslint-community/regexpp-4.5.0.tgz#f6f729b02feee2c749f57e334b7a1b5f40a81724"
  integrity sha1-9vcpsC/u4sdJ9X4zS3obX0CoFyQ=

"@eslint/eslintrc@^0.4.3":
  version "0.4.3"
  resolved "http://r.npm.sankuai.com/@eslint/eslintrc/download/@eslint/eslintrc-0.4.3.tgz#9e42981ef035beb3dd49add17acb96e8ff6f394c"
  integrity sha1-nkKYHvA1vrPdSa3ResuW6P9vOUw=
  dependencies:
    ajv "^6.12.4"
    debug "^4.1.1"
    espree "^7.3.0"
    globals "^13.9.0"
    ignore "^4.0.6"
    import-fresh "^3.2.1"
    js-yaml "^3.13.1"
    minimatch "^3.0.4"
    strip-json-comments "^3.1.1"

"@hapi/address@2.x.x":
  version "2.1.4"
  resolved "http://r.npm.sankuai.com/@hapi/address/download/@hapi/address-2.1.4.tgz#5d67ed43f3fd41a69d4b9ff7b56e7c0d1d0a81e5"
  integrity sha1-XWftQ/P9QaadS5/3tW58DR0KgeU=

"@hapi/bourne@1.x.x":
  version "1.3.2"
  resolved "http://r.npm.sankuai.com/@hapi/bourne/download/@hapi/bourne-1.3.2.tgz#0a7095adea067243ce3283e1b56b8a8f453b242a"
  integrity sha1-CnCVreoGckPOMoPhtWuKj0U7JCo=

"@hapi/hoek@8.x.x", "@hapi/hoek@^8.3.0":
  version "8.5.1"
  resolved "http://r.npm.sankuai.com/@hapi/hoek/download/@hapi/hoek-8.5.1.tgz#fde96064ca446dec8c55a8c2f130957b070c6e06"
  integrity sha1-/elgZMpEbeyMVajC8TCVewcMbgY=

"@hapi/joi@^15.0.3":
  version "15.1.1"
  resolved "http://r.npm.sankuai.com/@hapi/joi/download/@hapi/joi-15.1.1.tgz#c675b8a71296f02833f8d6d243b34c57b8ce19d7"
  integrity sha1-xnW4pxKW8Cgz+NbSQ7NMV7jOGdc=
  dependencies:
    "@hapi/address" "2.x.x"
    "@hapi/bourne" "1.x.x"
    "@hapi/hoek" "8.x.x"
    "@hapi/topo" "3.x.x"

"@hapi/topo@3.x.x":
  version "3.1.6"
  resolved "http://r.npm.sankuai.com/@hapi/topo/download/@hapi/topo-3.1.6.tgz#68d935fa3eae7fdd5ab0d7f953f3205d8b2bfc29"
  integrity sha1-aNk1+j6uf91asNf5U/MgXYsr/Ck=
  dependencies:
    "@hapi/hoek" "^8.3.0"

"@humanwhocodes/config-array@^0.5.0":
  version "0.5.0"
  resolved "http://r.npm.sankuai.com/@humanwhocodes/config-array/download/@humanwhocodes/config-array-0.5.0.tgz#1407967d4c6eecd7388f83acf1eaf4d0c6e58ef9"
  integrity sha1-FAeWfUxu7Nc4j4Os8er00Mbljvk=
  dependencies:
    "@humanwhocodes/object-schema" "^1.2.0"
    debug "^4.1.1"
    minimatch "^3.0.4"

"@humanwhocodes/object-schema@^1.2.0":
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/@humanwhocodes/object-schema/download/@humanwhocodes/object-schema-1.2.1.tgz#b520529ec21d8e5945a1851dfd1c32e94e39ff45"
  integrity sha1-tSBSnsIdjllFoYUd/Rwy6U45/0U=

"@istanbuljs/load-nyc-config@^1.0.0":
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/@istanbuljs/load-nyc-config/download/@istanbuljs/load-nyc-config-1.1.0.tgz#fd3db1d59ecf7cf121e80650bb86712f9b55eced"
  integrity sha1-/T2x1Z7PfPEh6AZQu4ZxL5tV7O0=
  dependencies:
    camelcase "^5.3.1"
    find-up "^4.1.0"
    get-package-type "^0.1.0"
    js-yaml "^3.13.1"
    resolve-from "^5.0.0"

"@istanbuljs/schema@^0.1.2":
  version "0.1.3"
  resolved "http://r.npm.sankuai.com/@istanbuljs/schema/download/@istanbuljs/schema-0.1.3.tgz#e45e384e4b8ec16bce2fd903af78450f6bf7ec98"
  integrity sha1-5F44TkuOwWvOL9kDr3hFD2v37Jg=

"@jest/console@^24.9.0":
  version "24.9.0"
  resolved "http://r.npm.sankuai.com/@jest/console/download/@jest/console-24.9.0.tgz#79b1bc06fb74a8cfb01cbdedf945584b1b9707f0"
  integrity sha1-ebG8Bvt0qM+wHL3t+UVYSxuXB/A=
  dependencies:
    "@jest/source-map" "^24.9.0"
    chalk "^2.0.1"
    slash "^2.0.0"

"@jest/console@^26.6.2":
  version "26.6.2"
  resolved "http://r.npm.sankuai.com/@jest/console/download/@jest/console-26.6.2.tgz#4e04bc464014358b03ab4937805ee36a0aeb98f2"
  integrity sha1-TgS8RkAUNYsDq0k3gF7jagrrmPI=
  dependencies:
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    chalk "^4.0.0"
    jest-message-util "^26.6.2"
    jest-util "^26.6.2"
    slash "^3.0.0"

"@jest/core@^26.6.3":
  version "26.6.3"
  resolved "http://r.npm.sankuai.com/@jest/core/download/@jest/core-26.6.3.tgz#7639fcb3833d748a4656ada54bde193051e45fad"
  integrity sha1-djn8s4M9dIpGVq2lS94ZMFHkX60=
  dependencies:
    "@jest/console" "^26.6.2"
    "@jest/reporters" "^26.6.2"
    "@jest/test-result" "^26.6.2"
    "@jest/transform" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    ansi-escapes "^4.2.1"
    chalk "^4.0.0"
    exit "^0.1.2"
    graceful-fs "^4.2.4"
    jest-changed-files "^26.6.2"
    jest-config "^26.6.3"
    jest-haste-map "^26.6.2"
    jest-message-util "^26.6.2"
    jest-regex-util "^26.0.0"
    jest-resolve "^26.6.2"
    jest-resolve-dependencies "^26.6.3"
    jest-runner "^26.6.3"
    jest-runtime "^26.6.3"
    jest-snapshot "^26.6.2"
    jest-util "^26.6.2"
    jest-validate "^26.6.2"
    jest-watcher "^26.6.2"
    micromatch "^4.0.2"
    p-each-series "^2.1.0"
    rimraf "^3.0.0"
    slash "^3.0.0"
    strip-ansi "^6.0.0"

"@jest/environment@^26.6.2":
  version "26.6.2"
  resolved "http://r.npm.sankuai.com/@jest/environment/download/@jest/environment-26.6.2.tgz#ba364cc72e221e79cc8f0a99555bf5d7577cf92c"
  integrity sha1-ujZMxy4iHnnMjwqZVVv111d8+Sw=
  dependencies:
    "@jest/fake-timers" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    jest-mock "^26.6.2"

"@jest/fake-timers@^24.9.0":
  version "24.9.0"
  resolved "http://r.npm.sankuai.com/@jest/fake-timers/download/@jest/fake-timers-24.9.0.tgz#ba3e6bf0eecd09a636049896434d306636540c93"
  integrity sha1-uj5r8O7NCaY2BJiWQ00wZjZUDJM=
  dependencies:
    "@jest/types" "^24.9.0"
    jest-message-util "^24.9.0"
    jest-mock "^24.9.0"

"@jest/fake-timers@^26.6.2":
  version "26.6.2"
  resolved "http://r.npm.sankuai.com/@jest/fake-timers/download/@jest/fake-timers-26.6.2.tgz#459c329bcf70cee4af4d7e3f3e67848123535aad"
  integrity sha1-RZwym89wzuSvTX4/PmeEgSNTWq0=
  dependencies:
    "@jest/types" "^26.6.2"
    "@sinonjs/fake-timers" "^6.0.1"
    "@types/node" "*"
    jest-message-util "^26.6.2"
    jest-mock "^26.6.2"
    jest-util "^26.6.2"

"@jest/globals@^26.6.2":
  version "26.6.2"
  resolved "http://r.npm.sankuai.com/@jest/globals/download/@jest/globals-26.6.2.tgz#5b613b78a1aa2655ae908eba638cc96a20df720a"
  integrity sha1-W2E7eKGqJlWukI66Y4zJaiDfcgo=
  dependencies:
    "@jest/environment" "^26.6.2"
    "@jest/types" "^26.6.2"
    expect "^26.6.2"

"@jest/reporters@^26.6.2":
  version "26.6.2"
  resolved "http://r.npm.sankuai.com/@jest/reporters/download/@jest/reporters-26.6.2.tgz#1f518b99637a5f18307bd3ecf9275f6882a667f6"
  integrity sha1-H1GLmWN6Xxgwe9Ps+SdfaIKmZ/Y=
  dependencies:
    "@bcoe/v8-coverage" "^0.2.3"
    "@jest/console" "^26.6.2"
    "@jest/test-result" "^26.6.2"
    "@jest/transform" "^26.6.2"
    "@jest/types" "^26.6.2"
    chalk "^4.0.0"
    collect-v8-coverage "^1.0.0"
    exit "^0.1.2"
    glob "^7.1.2"
    graceful-fs "^4.2.4"
    istanbul-lib-coverage "^3.0.0"
    istanbul-lib-instrument "^4.0.3"
    istanbul-lib-report "^3.0.0"
    istanbul-lib-source-maps "^4.0.0"
    istanbul-reports "^3.0.2"
    jest-haste-map "^26.6.2"
    jest-resolve "^26.6.2"
    jest-util "^26.6.2"
    jest-worker "^26.6.2"
    slash "^3.0.0"
    source-map "^0.6.0"
    string-length "^4.0.1"
    terminal-link "^2.0.0"
    v8-to-istanbul "^7.0.0"
  optionalDependencies:
    node-notifier "^8.0.0"

"@jest/schemas@^29.6.3":
  version "29.6.3"
  resolved "http://r.npm.sankuai.com/@jest/schemas/download/@jest/schemas-29.6.3.tgz#430b5ce8a4e0044a7e3819663305a7b3091c8e03"
  integrity sha1-Qwtc6KTgBEp+OBlmMwWnswkcjgM=
  dependencies:
    "@sinclair/typebox" "^0.27.8"

"@jest/source-map@^24.9.0":
  version "24.9.0"
  resolved "http://r.npm.sankuai.com/@jest/source-map/download/@jest/source-map-24.9.0.tgz#0e263a94430be4b41da683ccc1e6bffe2a191714"
  integrity sha1-DiY6lEML5LQdpoPMwea//ioZFxQ=
  dependencies:
    callsites "^3.0.0"
    graceful-fs "^4.1.15"
    source-map "^0.6.0"

"@jest/source-map@^26.6.2":
  version "26.6.2"
  resolved "http://r.npm.sankuai.com/@jest/source-map/download/@jest/source-map-26.6.2.tgz#29af5e1e2e324cafccc936f218309f54ab69d535"
  integrity sha1-Ka9eHi4yTK/MyTbyGDCfVKtp1TU=
  dependencies:
    callsites "^3.0.0"
    graceful-fs "^4.2.4"
    source-map "^0.6.0"

"@jest/test-result@^24.9.0":
  version "24.9.0"
  resolved "http://r.npm.sankuai.com/@jest/test-result/download/@jest/test-result-24.9.0.tgz#11796e8aa9dbf88ea025757b3152595ad06ba0ca"
  integrity sha1-EXluiqnb+I6gJXV7MVJZWtBroMo=
  dependencies:
    "@jest/console" "^24.9.0"
    "@jest/types" "^24.9.0"
    "@types/istanbul-lib-coverage" "^2.0.0"

"@jest/test-result@^26.6.2":
  version "26.6.2"
  resolved "http://r.npm.sankuai.com/@jest/test-result/download/@jest/test-result-26.6.2.tgz#55da58b62df134576cc95476efa5f7949e3f5f18"
  integrity sha1-VdpYti3xNFdsyVR276X3lJ4/Xxg=
  dependencies:
    "@jest/console" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/istanbul-lib-coverage" "^2.0.0"
    collect-v8-coverage "^1.0.0"

"@jest/test-sequencer@^26.6.3":
  version "26.6.3"
  resolved "http://r.npm.sankuai.com/@jest/test-sequencer/download/@jest/test-sequencer-26.6.3.tgz#98e8a45100863886d074205e8ffdc5a7eb582b17"
  integrity sha1-mOikUQCGOIbQdCBej/3Fp+tYKxc=
  dependencies:
    "@jest/test-result" "^26.6.2"
    graceful-fs "^4.2.4"
    jest-haste-map "^26.6.2"
    jest-runner "^26.6.3"
    jest-runtime "^26.6.3"

"@jest/transform@^26.6.2":
  version "26.6.2"
  resolved "http://r.npm.sankuai.com/@jest/transform/download/@jest/transform-26.6.2.tgz#5ac57c5fa1ad17b2aae83e73e45813894dcf2e4b"
  integrity sha1-WsV8X6GtF7Kq6D5z5FgTiU3PLks=
  dependencies:
    "@babel/core" "^7.1.0"
    "@jest/types" "^26.6.2"
    babel-plugin-istanbul "^6.0.0"
    chalk "^4.0.0"
    convert-source-map "^1.4.0"
    fast-json-stable-stringify "^2.0.0"
    graceful-fs "^4.2.4"
    jest-haste-map "^26.6.2"
    jest-regex-util "^26.0.0"
    jest-util "^26.6.2"
    micromatch "^4.0.2"
    pirates "^4.0.1"
    slash "^3.0.0"
    source-map "^0.6.1"
    write-file-atomic "^3.0.0"

"@jest/types@^24.9.0":
  version "24.9.0"
  resolved "http://r.npm.sankuai.com/@jest/types/download/@jest/types-24.9.0.tgz#63cb26cb7500d069e5a389441a7c6ab5e909fc59"
  integrity sha1-Y8smy3UA0Gnlo4lEGnxqtekJ/Fk=
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^1.1.1"
    "@types/yargs" "^13.0.0"

"@jest/types@^25.5.0":
  version "25.5.0"
  resolved "http://r.npm.sankuai.com/@jest/types/download/@jest/types-25.5.0.tgz#4d6a4793f7b9599fc3680877b856a97dbccf2a9d"
  integrity sha1-TWpHk/e5WZ/DaAh3uFapfbzPKp0=
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^1.1.1"
    "@types/yargs" "^15.0.0"
    chalk "^3.0.0"

"@jest/types@^26.6.2":
  version "26.6.2"
  resolved "http://r.npm.sankuai.com/@jest/types/download/@jest/types-26.6.2.tgz#bef5a532030e1d88a2f5a6d933f84e97226ed48e"
  integrity sha1-vvWlMgMOHYii9abZM/hOlyJu1I4=
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^3.0.0"
    "@types/node" "*"
    "@types/yargs" "^15.0.0"
    chalk "^4.0.0"

"@jridgewell/gen-mapping@^0.3.0", "@jridgewell/gen-mapping@^0.3.2":
  version "0.3.3"
  resolved "http://r.npm.sankuai.com/@jridgewell/gen-mapping/download/@jridgewell/gen-mapping-0.3.3.tgz#7e02e6eb5df901aaedb08514203b096614024098"
  integrity sha1-fgLm6135AartsIUUIDsJZhQCQJg=
  dependencies:
    "@jridgewell/set-array" "^1.0.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.9"

"@jridgewell/resolve-uri@3.1.0":
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/@jridgewell/resolve-uri/download/@jridgewell/resolve-uri-3.1.0.tgz#2203b118c157721addfe69d47b70465463066d78"
  integrity sha1-IgOxGMFXchrd/mnUe3BGVGMGbXg=

"@jridgewell/set-array@^1.0.1":
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/@jridgewell/set-array/download/@jridgewell/set-array-1.1.2.tgz#7c6cf998d6d20b914c0a55a91ae928ff25965e72"
  integrity sha1-fGz5mNbSC5FMClWpGuko/yWWXnI=

"@jridgewell/sourcemap-codec@1.4.14":
  version "1.4.14"
  resolved "http://r.npm.sankuai.com/@jridgewell/sourcemap-codec/download/@jridgewell/sourcemap-codec-1.4.14.tgz#add4c98d341472a289190b424efbdb096991bb24"
  integrity sha1-rdTJjTQUcqKJGQtCTvvbCWmRuyQ=

"@jridgewell/sourcemap-codec@^1.4.10":
  version "1.4.15"
  resolved "http://r.npm.sankuai.com/@jridgewell/sourcemap-codec/download/@jridgewell/sourcemap-codec-1.4.15.tgz#d7c6e6755c78567a951e04ab52ef0fd26de59f32"
  integrity sha1-18bmdVx4VnqVHgSrUu8P0m3lnzI=

"@jridgewell/trace-mapping@^0.3.17", "@jridgewell/trace-mapping@^0.3.9":
  version "0.3.18"
  resolved "http://r.npm.sankuai.com/@jridgewell/trace-mapping/download/@jridgewell/trace-mapping-0.3.18.tgz#25783b2086daf6ff1dcb53c9249ae480e4dd4cd6"
  integrity sha1-JXg7IIba9v8dy1PJJJrkgOTdTNY=
  dependencies:
    "@jridgewell/resolve-uri" "3.1.0"
    "@jridgewell/sourcemap-codec" "1.4.14"

"@mfe/bee-foundation-moses@^0.1.4":
  version "0.1.4"
  resolved "http://r.npm.sankuai.com/@mfe/bee-foundation-moses/download/@mfe/bee-foundation-moses-0.1.4.tgz#1bdfec315a0da6483fd2f64e84ecc69c390a67b0"
  integrity sha1-G9/sMVoNpkg/0vZOhOzGnDkKZ7A=
  dependencies:
    "@mfe/bee-foundation-router" "1.2.11"
    "@mfe/cc-api-caller-bee" "0.2.8"
    "@mfe/waimai-mfe-bee-common" "^2.0.7"
    "@mrn/mrn-utils" "^1.5.0"
    "@roo/roo-rn" "^1.0.4"
    lodash.debounce "^4.0.8"
    lodash.throttle "^4.1.1"

"@mfe/bee-foundation-navigation@1.2.1":
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/@mfe/bee-foundation-navigation/download/@mfe/bee-foundation-navigation-1.2.1.tgz#ecd3d71c8bb8923bfba00374fa905078b92fe637"
  integrity sha1-7NPXHIu4kjv7oAN0+pBQeLkv5jc=
  dependencies:
    "@react-navigation/native" "^6.1.6"
    "@react-navigation/native-stack" "^6.9.12"
    react-native-safe-area-context "3.4.1"
    react-native-screens "3.5.0"

"@mfe/bee-foundation-router@1.2.11":
  version "1.2.11"
  resolved "http://r.npm.sankuai.com/@mfe/bee-foundation-router/download/@mfe/bee-foundation-router-1.2.11.tgz#617a6d3a023dbe4062d5c5a7d27fc96914375af9"
  integrity sha1-YXptOgI9vkBi1cWn0n/JaRQ3Wvk=

"@mfe/bee-foundation-utils@^6.0.6":
  version "6.0.6"
  resolved "http://r.npm.sankuai.com/@mfe/bee-foundation-utils/download/@mfe/bee-foundation-utils-6.0.6.tgz#2e37e4f76957147233a3e4965482aa255ad8f8da"
  integrity sha1-Ljfk92lXFHIzo+SWVIKqJVrY+No=
  dependencies:
    "@mfe/react-native-image-zoom" "1.1.2"
    "@mfe/react-native-picker-native" "5.0.0"
    "@mfe/react-native-vector-icons" "4.0.0"
    "@mfe/react-native-video" "2.2.2"
    "@mrn/mrnmap" "4.1119.3"
    "@nibfe/doraemon-api" "^3.0.10"
    geojson "^0.5.0"
    react-native-svg "12.1.0"

"@mfe/cc-api-caller-bee@0.2.8":
  version "0.2.8"
  resolved "http://r.npm.sankuai.com/@mfe/cc-api-caller-bee/download/@mfe/cc-api-caller-bee-0.2.8.tgz#b25407f42ade7cf00f9a0f5dfe0ea33cf32d6c3e"
  integrity sha1-slQH9CrefPAPmg9d/g6jPPMtbD4=
  dependencies:
    "@mfe/cc-api-caller" "^0.2.7"
    query-string "^7.1.1"

"@mfe/cc-api-caller-bee@^0.2.15":
  version "0.2.15"
  resolved "http://r.npm.sankuai.com/@mfe/cc-api-caller-bee/download/@mfe/cc-api-caller-bee-0.2.15.tgz#243bde7ae0366edc00c0045882c0d01ed86f65ae"
  integrity sha1-JDveeuA2btwAwARYgsDQHthvZa4=
  dependencies:
    "@mfe/cc-api-caller" "^0.3.2"
    query-string "^7.1.1"

"@mfe/cc-api-caller@^0.2.7":
  version "0.2.8"
  resolved "http://r.npm.sankuai.com/@mfe/cc-api-caller/download/@mfe/cc-api-caller-0.2.8.tgz#63b6c07d39c1360c50f02ff46a344ab79b9940e6"
  integrity sha1-Y7bAfTnBNgxQ8C/0ajRKt5uZQOY=
  dependencies:
    "@babel/runtime" "^7.18.9"
    ejs "^3.1.6"
    ora "^5.4.0"
  optionalDependencies:
    "@mtfe/yapi2service" "^1.1.5"

"@mfe/cc-api-caller@^0.3.2":
  version "0.3.2"
  resolved "http://r.npm.sankuai.com/@mfe/cc-api-caller/download/@mfe/cc-api-caller-0.3.2.tgz#af856f813bb814bbc31d87aac73199b09133fa1a"
  integrity sha1-r4VvgTu4FLvDHYeqxzGZsJEz+ho=
  dependencies:
    "@babel/runtime" "^7.18.9"
    ejs "^3.1.6"
    ora "^5.4.0"
  optionalDependencies:
    "@mtfe/yapi2service" "^1.1.5"

"@mfe/querystringify@^2.2.0":
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/@mfe/querystringify/download/@mfe/querystringify-2.2.0.tgz#36f71d71f5260e0996936bcbe25ea2a7bf5736c6"
  integrity sha1-NvcdcfUmDgmWk2vL4l6ip79XNsY=

"@mfe/react-native-cookies@^0.0.4":
  version "0.0.4"
  resolved "http://r.npm.sankuai.com/@mfe/react-native-cookies/download/@mfe/react-native-cookies-0.0.4.tgz#bfb1d7add29621470da993a21fa9b674b28a8af6"
  integrity sha1-v7HXrdKWIUcNqZOiH6m2dLKKivY=
  dependencies:
    invariant "^2.1.0"

"@mfe/react-native-device-info@0.10.5", "@mfe/react-native-device-info@^0.10.5":
  version "0.10.5"
  resolved "http://r.npm.sankuai.com/@mfe/react-native-device-info/download/@mfe/react-native-device-info-0.10.5.tgz#015b3e9dc6a694890b1f514ecbddad5d89afe245"
  integrity sha1-AVs+ncamlIkLH1FOy92tXYmv4kU=

"@mfe/react-native-image-zoom@1.1.2":
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/@mfe/react-native-image-zoom/download/@mfe/react-native-image-zoom-1.1.2.tgz#43e30ed86b25a4ec1277f333f2d3a917bd81ab69"
  integrity sha1-Q+MO2GslpOwSd/Mz8tOpF72Bq2k=

"@mfe/react-native-indicator@^1.0.2":
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/@mfe/react-native-indicator/download/@mfe/react-native-indicator-1.0.2.tgz#0eba946b6a7e4ea44703f6f25634a1f7df62455c"
  integrity sha1-DrqUa2p+TqRHA/byVjSh999iRVw=
  dependencies:
    "@mfe/react-native-topview" "^0.3.2"

"@mfe/react-native-picker-native@5.0.0":
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/@mfe/react-native-picker-native/download/@mfe/react-native-picker-native-5.0.0.tgz#45409ca5ba41455aeba407b79d4dc1d85e48f12d"
  integrity sha1-RUCcpbpBRVrrpAe3nU3B2F5I8S0=

"@mfe/react-native-screenshotcatch@0.2.0":
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/@mfe/react-native-screenshotcatch/download/@mfe/react-native-screenshotcatch-0.2.0.tgz#55ef4b62a8885ba01cd4717ca0f95fb1e4bb7970"
  integrity sha1-Ve9LYqiIW6Ac1HF8oPlfseS7eXA=

"@mfe/react-native-topview@^0.3.2":
  version "0.3.4"
  resolved "http://r.npm.sankuai.com/@mfe/react-native-topview/download/@mfe/react-native-topview-0.3.4.tgz#6c285aa3c0e0e530d9a821c570ed348157eca23a"
  integrity sha1-bChao8Dg5TDZqCHFcO00gVfsojo=

"@mfe/react-native-vector-icons@4.0.0":
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/@mfe/react-native-vector-icons/download/@mfe/react-native-vector-icons-4.0.0.tgz#8ed98821bfe15ba58079cf593d78485f1b53e513"
  integrity sha1-jtmIIb/hW6WAec9ZPXhIXxtT5RM=
  dependencies:
    "@react-native-community/toolbar-android" "^0.2.1"
    lodash "^4.0.0"
    yargs "^3.31.0"

"@mfe/react-native-video@2.2.2":
  version "2.2.2"
  resolved "http://r.npm.sankuai.com/@mfe/react-native-video/download/@mfe/react-native-video-2.2.2.tgz#5355603c60ebcc6deff80fe2d4b2065afc403b79"
  integrity sha1-U1VgPGDrzG3v+A/i1LIGWvxAO3k=
  dependencies:
    keymirror "0.1.1"
    prop-types "^15.5.10"

"@mfe/react-native-video@^2.2.2":
  version "2.2.3"
  resolved "http://r.npm.sankuai.com/@mfe/react-native-video/download/@mfe/react-native-video-2.2.3.tgz#a20718ddf16a84c66ece8df9ed760204b831c53e"
  integrity sha1-ogcY3fFqhMZuzo357XYCBLgxxT4=
  dependencies:
    keymirror "0.1.1"
    prop-types "^15.5.10"

"@mfe/url-parse@1.5.10", "@mfe/url-parse@^1.5.10":
  version "1.5.10"
  resolved "http://r.npm.sankuai.com/@mfe/url-parse/download/@mfe/url-parse-1.5.10.tgz#6623fc9b611b2a49a0e6569c7f8f7002c697fd35"
  integrity sha1-ZiP8m2EbKkmg5lacf49wAsaX/TU=
  dependencies:
    "@mfe/querystringify" "^2.2.0"
    requires-port "^1.0.0"

"@mfe/waimai-mfe-bee-common@^2.0.7":
  version "2.0.21"
  resolved "http://r.npm.sankuai.com/@mfe/waimai-mfe-bee-common/download/@mfe/waimai-mfe-bee-common-2.0.21.tgz#2a2e9d008f499d3f13d7282f0780877c1b8ddfb0"
  integrity sha1-Ki6dAI9JnT8T1ygvB4CHfBuN37A=
  dependencies:
    "@mfe/react-native-cookies" "^0.0.4"
    "@mfe/react-native-device-info" "^0.10.5"
    "@mfe/react-native-indicator" "^1.0.2"
    "@mfe/react-native-screenshotcatch" "0.2.0"
    "@mfe/url-parse" "1.5.10"
    "@mrn/mrn-knb" "^0.5.0"
    "@mrn/mrn-utils" "^1.5.0"
    "@react-native-community/netinfo" "^5.9.7"
    "@roo/roo-rn" "^1.0.6"
    "@types/node" "16.9.1"
    events "^3.3.0"
    p-retry "4.2.0"
    react-native-communications "^2.2.1"

"@mfe/waimai-mfe-bee-common@^2.0.77":
  version "2.0.78"
  resolved "http://r.npm.sankuai.com/@mfe/waimai-mfe-bee-common/download/@mfe/waimai-mfe-bee-common-2.0.78.tgz#00b3f9e5bc42f173886ce9a0159fcc07a3847f42"
  integrity sha1-ALP55bxC8XOIbOmgFZ/MB6OEf0I=
  dependencies:
    "@mfe/react-native-cookies" "^0.0.4"
    "@mfe/react-native-device-info" "0.10.5"
    "@mfe/react-native-indicator" "^1.0.2"
    "@mfe/react-native-screenshotcatch" "0.2.0"
    "@mfe/url-parse" "1.5.10"
    "@mrn/mrn-knb" "0.5.1"
    "@mrn/mrn-utils" "1.5.0"
    "@mtfe/msi-business" "1.1.0"
    "@mtfe/msi-mrn" "1.60.0-beta.0"
    "@react-native-community/netinfo" "^5.9.7"
    "@roo/roo-rn" "1.1.3"
    events "^3.3.0"
    p-retry "4.2.0"
    react-native-communications "2.2.1"

"@mrn/dio@^0.0.6":
  version "0.0.6"
  resolved "http://r.npm.sankuai.com/@mrn/dio/download/@mrn/dio-0.0.6.tgz#54a494fd026f26d94a746aaa0b7191da44abf7fc"
  integrity sha1-VKSU/QJvJtlKdGqqC3GR2kSr9/w=

"@mrn/eslint-plugin@^3.0.1":
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/@mrn/eslint-plugin/download/@mrn/eslint-plugin-3.0.1.tgz#50da9570083b4d9ac898d0d58b5eea562fa82746"
  integrity sha1-UNqVcAg7TZrImNDVi17qVi+oJ0Y=
  dependencies:
    "@react-native-community/eslint-config" "^2.0.0"
    "@react-native-community/eslint-plugin" "^1.1.0"

"@mrn/mrn-babel-preset@^3.0.3":
  version "3.0.3"
  resolved "http://r.npm.sankuai.com/@mrn/mrn-babel-preset/download/@mrn/mrn-babel-preset-3.0.3.tgz#19a648a046566ebb20a0c3b712aee774deb50c56"
  integrity sha1-GaZIoEZWbrsgoMO3Eq7ndN61DFY=
  dependencies:
    "@babel/preset-typescript" "^7.10.4"
    babel-plugin-minify-dead-code-elimination "^0.5.1"
    babel-plugin-module-resolver "^4.0.0"
    babel-plugin-transform-define "^2.0.0"
    babel-plugin-transform-remove-console "^6.9.4"
    find-babel-config "^1.2.0"

"@mrn/mrn-base@3.0.47":
  version "3.0.47"
  resolved "http://r.npm.sankuai.com/@mrn/mrn-base/download/@mrn/mrn-base-3.0.47.tgz#56afe813d50a352343a8b7660307b5a6d438cdb9"
  integrity sha1-Vq/oE9UKNSNDqLdmAwe1ptQ4zbk=
  dependencies:
    "@mrn/react-native" "3.0.37"
    create-react-class "^15.7.0"
    react "16.13.1"
    react-redux "7.2.1"
    redux "^4.0.5"
  optionalDependencies:
    "@types/react-redux" "*"

"@mrn/mrn-cli@git+ssh://*******************/~jiangliancheng/mrn-cli.git#v3.0.3-bee1.1":
  version "3.0.3"
  resolved "git+ssh://*******************/~jiangliancheng/mrn-cli.git#4b3836a9a341d4475cdc7a7bf4eef6ace28c950d"
  dependencies:
    "@babel/core" "^7.11.6"
    "@babel/runtime" "^7.11.2"
    "@mrn/eslint-plugin" "^3.0.1"
    "@mrn/mrn-babel-preset" "^3.0.3"
    "@react-native-community/cli" "~4.14.0"
    "@react-native-community/cli-platform-android" "~4.13.0"
    "@react-native-community/cli-platform-ios" "~4.13.0"
    "@svgr/core" "^5.4.0"
    "@types/jest" "^26.0.13"
    "@yarnpkg/lockfile" "^1.1.0"
    archiver "^5.0.2"
    bplist "^0.0.4"
    chalk "^4.1.0"
    commander "^6.1.0"
    decompress "^4.2.1"
    download "^8.0.0"
    eslint "^7.18.0"
    find-root "^1.1.0"
    glob "^7.1.6"
    husky "^4.3.0"
    inquirer "^7.3.3"
    jest "^26.4.2"
    lodash "^4.17.20"
    node-machine-id "^1.1.12"
    ora "^5.1.0"
    portfinder "^1.0.28"
    qrcode-terminal "^0.12.0"
    react-test-renderer "~16.13.1"
    rimraf "^3.0.2"
    rsync "^0.6.1"
    semver "^7.3.2"
    ts-jest "^26.3.0"
  optionalDependencies:
    "@mrn/dio" "^0.0.6"
    typescript "^4.1.3"
    usb "^1.6.3"

"@mrn/mrn-knb@0.5.1", "@mrn/mrn-knb@^0.5.0":
  version "0.5.1"
  resolved "http://r.npm.sankuai.com/@mrn/mrn-knb/download/@mrn/mrn-knb-0.5.1.tgz#3028623b6c704ffdec280b05a143e6117f699e5b"
  integrity sha1-MChiO2xwT/3sKAsFoUPmEX9pnls=
  dependencies:
    "@mtfe/knb-next" "0.3.1"

"@mrn/mrn-utils@1.5.0", "@mrn/mrn-utils@^1.5.0":
  version "1.5.0"
  resolved "http://r.npm.sankuai.com/@mrn/mrn-utils/download/@mrn/mrn-utils-1.5.0.tgz#33909cb8722e453e88f1a5ebb63aed15c44fea44"
  integrity sha1-M5CcuHIuRT6I8aXrtjrtFcRP6kQ=

"@mrn/mrn-webview@^1.0.4":
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/@mrn/mrn-webview/download/@mrn/mrn-webview-1.0.4.tgz#49545ae1fb1d2a0d143171bf4369782f9fdf6f13"
  integrity sha1-SVRa4fsdKg0UMXG/Q2l4L5/fbxM=
  dependencies:
    escape-string-regexp "^2.0.0"
    invariant "^2.2.4"

"@mrn/mrnmap@4.1119.3":
  version "4.1119.3"
  resolved "http://r.npm.sankuai.com/@mrn/mrnmap/download/@mrn/mrnmap-4.1119.3.tgz#774c85f93ee04198def5cfed7af2e7c29fda0533"
  integrity sha1-d0yF+T7gQZje9c/tevLnwp/aBTM=
  dependencies:
    circular-json "^0.5.9"

"@mrn/react-native-linear-gradient@^1.1.3", "@mrn/react-native-linear-gradient@^1.1.7":
  version "1.1.7"
  resolved "http://r.npm.sankuai.com/@mrn/react-native-linear-gradient/download/@mrn/react-native-linear-gradient-1.1.7.tgz#e1f0168dc2b6bd855c0db08e2dc517107cdd4049"
  integrity sha1-4fAWjcK2vYVcDbCOLcUXEHzdQEk=

"@mrn/react-native@3.0.26", "@mrn/react-native@3.0.37":
  version "3.0.26"
  resolved "http://r.npm.sankuai.com/@mrn/react-native/download/@mrn/react-native-3.0.26.tgz#842711a0ad9cd797b07738aea126d171724f136a"
  integrity sha1-hCcRoK2c15ewdziuoSbRcXJPE2o=
  dependencies:
    abort-controller "^3.0.0"
    anser "^1.4.9"
    art "^0.10.3"
    base64-js "^1.1.2"
    event-target-shim "^5.0.1"
    fbjs "^1.0.0"
    fbjs-scripts "^1.1.0"
    invariant "^2.2.4"
    nullthrows "^1.1.1"
    pretty-format "^24.9.0"
    promise "^7.1.1"
    prop-types "^15.7.2"
    react-devtools-core "^4.6.0"
    react-refresh "^0.4.0"
    scheduler "0.19.1"
    stacktrace-parser "^0.1.3"
    use-subscription "^1.0.0"
    whatwg-fetch "^3.0.0"

"@mtfe/knb-core@^1.0.4":
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/@mtfe/knb-core/download/@mtfe/knb-core-1.0.4.tgz#31d3555369dd0675a2f323811451322b985010e8"
  integrity sha1-MdNVU2ndBnWi8yOBFFEyK5hQEOg=

"@mtfe/knb-next@0.3.1":
  version "0.3.1"
  resolved "http://r.npm.sankuai.com/@mtfe/knb-next/download/@mtfe/knb-next-0.3.1.tgz#585e39f93d598f217580134eeff933502a06f87c"
  integrity sha1-WF45+T1ZjyF1gBNO7/kzUCoG+Hw=
  dependencies:
    "@dp/weixin-js-sdk-loader" "^0.4.6"
    "@mtfe/knb-core" "^1.0.4"
    querystring "^0.2.0"

"@mtfe/msi-business@1.1.0":
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/@mtfe/msi-business/download/@mtfe/msi-business-1.1.0.tgz#b6af384f645d13eec82b88b1454354b70eeb32b9"
  integrity sha1-tq84T2RdE+7IK4ixRUNUtw7rMrk=

"@mtfe/msi-mrn@1.60.0-beta.0":
  version "1.60.0-beta.0"
  resolved "http://r.npm.sankuai.com/@mtfe/msi-mrn/download/@mtfe/msi-mrn-1.60.0-beta.0.tgz#e062682679ead39f46932b0fffc37d816b2acaae"
  integrity sha1-4GJoJnnq059GkysP/8N9gWsqyq4=
  dependencies:
    "@mtfe/msi" "1.60.0-beta.0"
    base64-js "^1.5.1"
    eventemitter3 "^4.0.7"

"@mtfe/msi@1.60.0-beta.0":
  version "1.60.0-beta.0"
  resolved "http://r.npm.sankuai.com/@mtfe/msi/download/@mtfe/msi-1.60.0-beta.0.tgz#b0d7ae1f925c0acfda8de210ed8aa1beec1e22fc"
  integrity sha1-sNeuH5JcCs/ajeIQ7YqhvuweIvw=
  dependencies:
    base64-js "^1.5.1"
    blueimp-md5 "^2.19.0"
    eventemitter3 "^4.0.7"

"@mtfe/yapi2service@^1.1.5":
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/@mtfe/yapi2service/download/@mtfe/yapi2service-1.2.0.tgz#b920308457a619687f8f40864e86e00506fb97ae"
  integrity sha1-uSAwhFemGWh/j0CGTobgBQb7l64=
  dependencies:
    axios "^0.24.0"
    chalk "^4.1.2"
    commander "^8.3.0"
    cross-spawn "^7.0.3"
    ejs "3.1.6"
    inquirer "^8.2.0"
    ora "^5.4.1"
    owner "^0.1.0"

"@nibfe/doraemon-api@^3.0.10":
  version "3.0.13"
  resolved "http://r.npm.sankuai.com/@nibfe/doraemon-api/download/@nibfe/doraemon-api-3.0.13.tgz#a4d403ce3d7313d8afb4a9497bed64b6d9599389"
  integrity sha1-pNQDzj1zE9ivtKlJe+1kttlZk4k=

"@nicolo-ribaudo/eslint-scope-5-internals@5.1.1-v1":
  version "5.1.1-v1"
  resolved "http://r.npm.sankuai.com/@nicolo-ribaudo/eslint-scope-5-internals/download/@nicolo-ribaudo/eslint-scope-5-internals-5.1.1-v1.tgz#dbf733a965ca47b1973177dc0bb6c889edcfb129"
  integrity sha1-2/czqWXKR7GXMXfcC7bIie3PsSk=
  dependencies:
    eslint-scope "5.1.1"

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "http://r.npm.sankuai.com/@nodelib/fs.scandir/download/@nodelib/fs.scandir-2.1.5.tgz#7619c2eb21b25483f6d167548b4cfd5a7488c3d5"
  integrity sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@2.0.5", "@nodelib/fs.stat@^2.0.2":
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/@nodelib/fs.stat/download/@nodelib/fs.stat-2.0.5.tgz#5bd262af94e9d25bd1e71b05deed44876a222e8b"
  integrity sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=

"@nodelib/fs.walk@^1.2.3":
  version "1.2.8"
  resolved "http://r.npm.sankuai.com/@nodelib/fs.walk/download/@nodelib/fs.walk-1.2.8.tgz#e95737e8bb6746ddedf69c556953494f196fe69a"
  integrity sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@react-native-community/cli-debugger-ui@^4.13.1":
  version "4.13.1"
  resolved "http://r.npm.sankuai.com/@react-native-community/cli-debugger-ui/download/@react-native-community/cli-debugger-ui-4.13.1.tgz#07de6d4dab80ec49231de1f1fbf658b4ad39b32c"
  integrity sha1-B95tTauA7EkjHeHx+/ZYtK05syw=
  dependencies:
    serve-static "^1.13.1"

"@react-native-community/cli-hermes@^4.13.0":
  version "4.13.0"
  resolved "http://r.npm.sankuai.com/@react-native-community/cli-hermes/download/@react-native-community/cli-hermes-4.13.0.tgz#6243ed9c709dad5e523f1ccd7d21066b32f2899d"
  integrity sha1-YkPtnHCdrV5SPxzNfSEGazLyiZ0=
  dependencies:
    "@react-native-community/cli-platform-android" "^4.13.0"
    "@react-native-community/cli-tools" "^4.13.0"
    chalk "^3.0.0"
    hermes-profile-transformer "^0.0.6"
    ip "^1.1.5"

"@react-native-community/cli-platform-android@^4.13.0", "@react-native-community/cli-platform-android@~4.13.0":
  version "4.13.0"
  resolved "http://r.npm.sankuai.com/@react-native-community/cli-platform-android/download/@react-native-community/cli-platform-android-4.13.0.tgz#922681ec82ee1aadd993598b814df1152118be02"
  integrity sha1-kiaB7ILuGq3Zk1mLgU3xFSEYvgI=
  dependencies:
    "@react-native-community/cli-tools" "^4.13.0"
    chalk "^3.0.0"
    execa "^1.0.0"
    fs-extra "^8.1.0"
    glob "^7.1.3"
    jetifier "^1.6.2"
    lodash "^4.17.15"
    logkitty "^0.7.1"
    slash "^3.0.0"
    xmldoc "^1.1.2"

"@react-native-community/cli-platform-ios@~4.13.0":
  version "4.13.0"
  resolved "http://r.npm.sankuai.com/@react-native-community/cli-platform-ios/download/@react-native-community/cli-platform-ios-4.13.0.tgz#a738915c68cac86df54e578b59a1311ea62b1aef"
  integrity sha1-pziRXGjKyG31TleLWaExHqYrGu8=
  dependencies:
    "@react-native-community/cli-tools" "^4.13.0"
    chalk "^3.0.0"
    glob "^7.1.3"
    js-yaml "^3.13.1"
    lodash "^4.17.15"
    plist "^3.0.1"
    xcode "^2.0.0"

"@react-native-community/cli-server-api@^4.13.1":
  version "4.13.1"
  resolved "http://r.npm.sankuai.com/@react-native-community/cli-server-api/download/@react-native-community/cli-server-api-4.13.1.tgz#bee7ee9702afce848e9d6ca3dcd5669b99b125bd"
  integrity sha1-vufulwKvzoSOnWyj3NVmm5mxJb0=
  dependencies:
    "@react-native-community/cli-debugger-ui" "^4.13.1"
    "@react-native-community/cli-tools" "^4.13.0"
    compression "^1.7.1"
    connect "^3.6.5"
    errorhandler "^1.5.0"
    nocache "^2.1.0"
    pretty-format "^25.1.0"
    serve-static "^1.13.1"
    ws "^1.1.0"

"@react-native-community/cli-tools@^4.13.0":
  version "4.13.0"
  resolved "http://r.npm.sankuai.com/@react-native-community/cli-tools/download/@react-native-community/cli-tools-4.13.0.tgz#b406463d33af16cedc4305a9a9257ed32845cf1b"
  integrity sha1-tAZGPTOvFs7cQwWpqSV+0yhFzxs=
  dependencies:
    chalk "^3.0.0"
    lodash "^4.17.15"
    mime "^2.4.1"
    node-fetch "^2.6.0"
    open "^6.2.0"
    shell-quote "1.6.1"

"@react-native-community/cli-types@^4.10.1":
  version "4.10.1"
  resolved "http://r.npm.sankuai.com/@react-native-community/cli-types/download/@react-native-community/cli-types-4.10.1.tgz#d68a2dcd1649d3b3774823c64e5e9ce55bfbe1c9"
  integrity sha1-1ootzRZJ07N3SCPGTl6c5Vv74ck=

"@react-native-community/cli@~4.14.0":
  version "4.14.0"
  resolved "http://r.npm.sankuai.com/@react-native-community/cli/download/@react-native-community/cli-4.14.0.tgz#bb106a98341bfa2db36060091ff90bfe82ea4f55"
  integrity sha1-uxBqmDQb+i2zYGAJH/kL/oLqT1U=
  dependencies:
    "@hapi/joi" "^15.0.3"
    "@react-native-community/cli-debugger-ui" "^4.13.1"
    "@react-native-community/cli-hermes" "^4.13.0"
    "@react-native-community/cli-server-api" "^4.13.1"
    "@react-native-community/cli-tools" "^4.13.0"
    "@react-native-community/cli-types" "^4.10.1"
    chalk "^3.0.0"
    command-exists "^1.2.8"
    commander "^2.19.0"
    cosmiconfig "^5.1.0"
    deepmerge "^3.2.0"
    envinfo "^7.7.2"
    execa "^1.0.0"
    find-up "^4.1.0"
    fs-extra "^8.1.0"
    glob "^7.1.3"
    graceful-fs "^4.1.3"
    inquirer "^3.0.6"
    leven "^3.1.0"
    lodash "^4.17.15"
    metro "^0.59.0"
    metro-config "^0.59.0"
    metro-core "^0.59.0"
    metro-react-native-babel-transformer "^0.59.0"
    metro-resolver "^0.59.0"
    minimist "^1.2.0"
    mkdirp "^0.5.1"
    node-stream-zip "^1.9.1"
    ora "^3.4.0"
    pretty-format "^25.2.0"
    semver "^6.3.0"
    serve-static "^1.13.1"
    strip-ansi "^5.2.0"
    sudo-prompt "^9.0.0"
    wcwidth "^1.0.1"

"@react-native-community/eslint-config@^2.0.0":
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/@react-native-community/eslint-config/download/@react-native-community/eslint-config-2.0.0.tgz#35dcc529a274803fc4e0a6b3d6c274551fb91774"
  integrity sha1-NdzFKaJ0gD/E4Kaz1sJ0VR+5F3Q=
  dependencies:
    "@react-native-community/eslint-plugin" "^1.1.0"
    "@typescript-eslint/eslint-plugin" "^3.1.0"
    "@typescript-eslint/parser" "^3.1.0"
    babel-eslint "^10.1.0"
    eslint-config-prettier "^6.10.1"
    eslint-plugin-eslint-comments "^3.1.2"
    eslint-plugin-flowtype "2.50.3"
    eslint-plugin-jest "22.4.1"
    eslint-plugin-prettier "3.1.2"
    eslint-plugin-react "^7.20.0"
    eslint-plugin-react-hooks "^4.0.4"
    eslint-plugin-react-native "^3.8.1"
    prettier "^2.0.2"

"@react-native-community/eslint-config@^3.2.0":
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/@react-native-community/eslint-config/download/@react-native-community/eslint-config-3.2.0.tgz#42f677d5fff385bccf1be1d3b8faa8c086cf998d"
  integrity sha1-QvZ31f/zhbzPG+HTuPqowIbPmY0=
  dependencies:
    "@babel/core" "^7.14.0"
    "@babel/eslint-parser" "^7.18.2"
    "@react-native-community/eslint-plugin" "^1.1.0"
    "@typescript-eslint/eslint-plugin" "^5.30.5"
    "@typescript-eslint/parser" "^5.30.5"
    eslint-config-prettier "^8.5.0"
    eslint-plugin-eslint-comments "^3.2.0"
    eslint-plugin-ft-flow "^2.0.1"
    eslint-plugin-jest "^26.5.3"
    eslint-plugin-prettier "^4.2.1"
    eslint-plugin-react "^7.30.1"
    eslint-plugin-react-hooks "^4.6.0"
    eslint-plugin-react-native "^4.0.0"

"@react-native-community/eslint-plugin@^1.1.0":
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/@react-native-community/eslint-plugin/download/@react-native-community/eslint-plugin-1.3.0.tgz#9e558170c106bbafaa1ef502bd8e6d4651012bf9"
  integrity sha1-nlWBcMEGu6+qHvUCvY5tRlEBK/k=

"@react-native-community/netinfo@^5.9.7":
  version "5.9.10"
  resolved "http://r.npm.sankuai.com/@react-native-community/netinfo/download/@react-native-community/netinfo-5.9.10.tgz#97d3a9fa62a3a4838ec7a6ec91cfec5a26e365b6"
  integrity sha1-l9Op+mKjpIOOx6bskc/sWibjZbY=

"@react-native-community/toolbar-android@^0.2.1":
  version "0.2.1"
  resolved "http://r.npm.sankuai.com/@react-native-community/toolbar-android/download/@react-native-community/toolbar-android-0.2.1.tgz#5d021da8f6aef9412c58d5c981a62b91191af3fe"
  integrity sha1-XQIdqPau+UEsWNXJgaYrkRka8/4=

"@react-navigation/core@^6.4.8":
  version "6.4.8"
  resolved "http://r.npm.sankuai.com/@react-navigation/core/download/@react-navigation/core-6.4.8.tgz#a18e106d3c59cdcfc4ce53f7344e219ed35c88ed"
  integrity sha1-oY4QbTxZzc/EzlP3NE4hntNciO0=
  dependencies:
    "@react-navigation/routers" "^6.1.8"
    escape-string-regexp "^4.0.0"
    nanoid "^3.1.23"
    query-string "^7.1.3"
    react-is "^16.13.0"
    use-latest-callback "^0.1.5"

"@react-navigation/elements@^1.3.17":
  version "1.3.17"
  resolved "http://r.npm.sankuai.com/@react-navigation/elements/download/@react-navigation/elements-1.3.17.tgz#9cb95765940f2841916fc71686598c22a3e4067e"
  integrity sha1-nLlXZZQPKEGRb8cWhlmMIqPkBn4=

"@react-navigation/native-stack@^6.9.12":
  version "6.9.12"
  resolved "http://r.npm.sankuai.com/@react-navigation/native-stack/download/@react-navigation/native-stack-6.9.12.tgz#a09fe43ab2fc4c82a1809e3953021d1da4ead85c"
  integrity sha1-oJ/kOrL8TIKhgJ45UwIdHaTq2Fw=
  dependencies:
    "@react-navigation/elements" "^1.3.17"
    warn-once "^0.1.0"

"@react-navigation/native@^6.1.6":
  version "6.1.6"
  resolved "http://r.npm.sankuai.com/@react-navigation/native/download/@react-navigation/native-6.1.6.tgz#84ff5cf85b91f660470fa9407c06c8ee393d5792"
  integrity sha1-hP9c+FuR9mBHD6lAfAbI7jk9V5I=
  dependencies:
    "@react-navigation/core" "^6.4.8"
    escape-string-regexp "^4.0.0"
    fast-deep-equal "^3.1.3"
    nanoid "^3.1.23"

"@react-navigation/routers@^6.1.8":
  version "6.1.8"
  resolved "http://r.npm.sankuai.com/@react-navigation/routers/download/@react-navigation/routers-6.1.8.tgz#ae56b2678dbb5abca5bd7c95d6a8d1abc767cba2"
  integrity sha1-rlayZ427WrylvXyV1qjRq8dny6I=
  dependencies:
    nanoid "^3.1.23"

"@roo/roo-rn@1.1.3", "@roo/roo-rn@^1.0.4", "@roo/roo-rn@^1.0.6":
  version "1.0.6"
  resolved "http://r.npm.sankuai.com/@roo/roo-rn/download/@roo/roo-rn-1.0.6.tgz#a41d4c0004a0932ef970c4ffd1c791aba71b8258"
  integrity sha1-pB1MAASgky75cMT/0ceRq6cbglg=
  dependencies:
    "@mrn/react-native-linear-gradient" "^1.1.3"
    async-validator "^1.10.0"
    date-format "^2.0.0"
    deepmerge "^4.0.0"
    moment "^2.24.0"

"@sinclair/typebox@^0.27.8":
  version "0.27.8"
  resolved "http://r.npm.sankuai.com/@sinclair/typebox/download/@sinclair/typebox-0.27.8.tgz#6667fac16c436b5434a387a34dedb013198f6e6e"
  integrity sha1-Zmf6wWxDa1Q0o4ejTe2wExmPbm4=

"@sindresorhus/is@^0.7.0":
  version "0.7.0"
  resolved "http://r.npm.sankuai.com/@sindresorhus/is/download/@sindresorhus/is-0.7.0.tgz#9a06f4f137ee84d7df0460c1fdb1135ffa6c50fd"
  integrity sha1-mgb08TfuhNffBGDB/bETX/psUP0=

"@sinonjs/commons@^1.7.0":
  version "1.8.6"
  resolved "http://r.npm.sankuai.com/@sinonjs/commons/download/@sinonjs/commons-1.8.6.tgz#80c516a4dc264c2a69115e7578d62581ff455ed9"
  integrity sha1-gMUWpNwmTCppEV51eNYlgf9FXtk=
  dependencies:
    type-detect "4.0.8"

"@sinonjs/fake-timers@^6.0.1":
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/@sinonjs/fake-timers/download/@sinonjs/fake-timers-6.0.1.tgz#293674fccb3262ac782c7aadfdeca86b10c75c40"
  integrity sha1-KTZ0/MsyYqx4LHqt/eyoaxDHXEA=
  dependencies:
    "@sinonjs/commons" "^1.7.0"

"@svgr/babel-plugin-add-jsx-attribute@^5.4.0":
  version "5.4.0"
  resolved "http://r.npm.sankuai.com/@svgr/babel-plugin-add-jsx-attribute/download/@svgr/babel-plugin-add-jsx-attribute-5.4.0.tgz#81ef61947bb268eb9d50523446f9c638fb355906"
  integrity sha1-ge9hlHuyaOudUFI0RvnGOPs1WQY=

"@svgr/babel-plugin-remove-jsx-attribute@^5.4.0":
  version "5.4.0"
  resolved "http://r.npm.sankuai.com/@svgr/babel-plugin-remove-jsx-attribute/download/@svgr/babel-plugin-remove-jsx-attribute-5.4.0.tgz#6b2c770c95c874654fd5e1d5ef475b78a0a962ef"
  integrity sha1-ayx3DJXIdGVP1eHV70dbeKCpYu8=

"@svgr/babel-plugin-remove-jsx-empty-expression@^5.0.1":
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/@svgr/babel-plugin-remove-jsx-empty-expression/download/@svgr/babel-plugin-remove-jsx-empty-expression-5.0.1.tgz#25621a8915ed7ad70da6cea3d0a6dbc2ea933efd"
  integrity sha1-JWIaiRXtetcNps6j0KbbwuqTPv0=

"@svgr/babel-plugin-replace-jsx-attribute-value@^5.0.1":
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/@svgr/babel-plugin-replace-jsx-attribute-value/download/@svgr/babel-plugin-replace-jsx-attribute-value-5.0.1.tgz#0b221fc57f9fcd10e91fe219e2cd0dd03145a897"
  integrity sha1-CyIfxX+fzRDpH+IZ4s0N0DFFqJc=

"@svgr/babel-plugin-svg-dynamic-title@^5.4.0":
  version "5.4.0"
  resolved "http://r.npm.sankuai.com/@svgr/babel-plugin-svg-dynamic-title/download/@svgr/babel-plugin-svg-dynamic-title-5.4.0.tgz#139b546dd0c3186b6e5db4fefc26cb0baea729d7"
  integrity sha1-E5tUbdDDGGtuXbT+/CbLC66nKdc=

"@svgr/babel-plugin-svg-em-dimensions@^5.4.0":
  version "5.4.0"
  resolved "http://r.npm.sankuai.com/@svgr/babel-plugin-svg-em-dimensions/download/@svgr/babel-plugin-svg-em-dimensions-5.4.0.tgz#6543f69526632a133ce5cabab965deeaea2234a0"
  integrity sha1-ZUP2lSZjKhM85cq6uWXe6uoiNKA=

"@svgr/babel-plugin-transform-react-native-svg@^5.4.0":
  version "5.4.0"
  resolved "http://r.npm.sankuai.com/@svgr/babel-plugin-transform-react-native-svg/download/@svgr/babel-plugin-transform-react-native-svg-5.4.0.tgz#00bf9a7a73f1cad3948cdab1f8dfb774750f8c80"
  integrity sha1-AL+aenPxytOUjNqx+N+3dHUPjIA=

"@svgr/babel-plugin-transform-svg-component@^5.5.0":
  version "5.5.0"
  resolved "http://r.npm.sankuai.com/@svgr/babel-plugin-transform-svg-component/download/@svgr/babel-plugin-transform-svg-component-5.5.0.tgz#583a5e2a193e214da2f3afeb0b9e8d3250126b4a"
  integrity sha1-WDpeKhk+IU2i86/rC56NMlASa0o=

"@svgr/babel-preset@^5.5.0":
  version "5.5.0"
  resolved "http://r.npm.sankuai.com/@svgr/babel-preset/download/@svgr/babel-preset-5.5.0.tgz#8af54f3e0a8add7b1e2b0fcd5a882c55393df327"
  integrity sha1-ivVPPgqK3XseKw/NWogsVTk98yc=
  dependencies:
    "@svgr/babel-plugin-add-jsx-attribute" "^5.4.0"
    "@svgr/babel-plugin-remove-jsx-attribute" "^5.4.0"
    "@svgr/babel-plugin-remove-jsx-empty-expression" "^5.0.1"
    "@svgr/babel-plugin-replace-jsx-attribute-value" "^5.0.1"
    "@svgr/babel-plugin-svg-dynamic-title" "^5.4.0"
    "@svgr/babel-plugin-svg-em-dimensions" "^5.4.0"
    "@svgr/babel-plugin-transform-react-native-svg" "^5.4.0"
    "@svgr/babel-plugin-transform-svg-component" "^5.5.0"

"@svgr/core@^5.4.0":
  version "5.5.0"
  resolved "http://r.npm.sankuai.com/@svgr/core/download/@svgr/core-5.5.0.tgz#82e826b8715d71083120fe8f2492ec7d7874a579"
  integrity sha1-gugmuHFdcQgxIP6PJJLsfXh0pXk=
  dependencies:
    "@svgr/plugin-jsx" "^5.5.0"
    camelcase "^6.2.0"
    cosmiconfig "^7.0.0"

"@svgr/hast-util-to-babel-ast@^5.5.0":
  version "5.5.0"
  resolved "http://r.npm.sankuai.com/@svgr/hast-util-to-babel-ast/download/@svgr/hast-util-to-babel-ast-5.5.0.tgz#5ee52a9c2533f73e63f8f22b779f93cd432a5461"
  integrity sha1-XuUqnCUz9z5j+PIrd5+TzUMqVGE=
  dependencies:
    "@babel/types" "^7.12.6"

"@svgr/plugin-jsx@^5.5.0":
  version "5.5.0"
  resolved "http://r.npm.sankuai.com/@svgr/plugin-jsx/download/@svgr/plugin-jsx-5.5.0.tgz#1aa8cd798a1db7173ac043466d7b52236b369000"
  integrity sha1-GqjNeYodtxc6wENGbXtSI2s2kAA=
  dependencies:
    "@babel/core" "^7.12.3"
    "@svgr/babel-preset" "^5.5.0"
    "@svgr/hast-util-to-babel-ast" "^5.5.0"
    svg-parser "^2.0.2"

"@testing-library/react-native@12":
  version "12.9.0"
  resolved "http://r.npm.sankuai.com/@testing-library/react-native/download/@testing-library/react-native-12.9.0.tgz#9c727d9ffec91024be3288ed9376df3673154784"
  integrity sha1-nHJ9n/7JECS+Mojtk3bfNnMVR4Q=
  dependencies:
    jest-matcher-utils "^29.7.0"
    pretty-format "^29.7.0"
    redent "^3.0.0"

"@tootallnate/once@1":
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/@tootallnate/once/download/@tootallnate/once-1.1.2.tgz#ccb91445360179a04e7fe6aff78c00ffc1eeaf82"
  integrity sha1-zLkURTYBeaBOf+av94wA/8Hur4I=

"@types/babel__core@^7.0.0", "@types/babel__core@^7.1.7":
  version "7.20.0"
  resolved "http://r.npm.sankuai.com/@types/babel__core/download/@types/babel__core-7.20.0.tgz#61bc5a4cae505ce98e1e36c5445e4bee060d8891"
  integrity sha1-YbxaTK5QXOmOHjbFRF5L7gYNiJE=
  dependencies:
    "@babel/parser" "^7.20.7"
    "@babel/types" "^7.20.7"
    "@types/babel__generator" "*"
    "@types/babel__template" "*"
    "@types/babel__traverse" "*"

"@types/babel__generator@*":
  version "7.6.4"
  resolved "http://r.npm.sankuai.com/@types/babel__generator/download/@types/babel__generator-7.6.4.tgz#1f20ce4c5b1990b37900b63f050182d28c2439b7"
  integrity sha1-HyDOTFsZkLN5ALY/BQGC0owkObc=
  dependencies:
    "@babel/types" "^7.0.0"

"@types/babel__template@*":
  version "7.4.1"
  resolved "http://r.npm.sankuai.com/@types/babel__template/download/@types/babel__template-7.4.1.tgz#3d1a48fd9d6c0edfd56f2ff578daed48f36c8969"
  integrity sha1-PRpI/Z1sDt/Vby/1eNrtSPNsiWk=
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"

"@types/babel__traverse@*", "@types/babel__traverse@^7.0.4", "@types/babel__traverse@^7.0.6":
  version "7.18.3"
  resolved "http://r.npm.sankuai.com/@types/babel__traverse/download/@types/babel__traverse-7.18.3.tgz#dfc508a85781e5698d5b33443416b6268c4b3e8d"
  integrity sha1-38UIqFeB5WmNWzNENBa2JoxLPo0=
  dependencies:
    "@babel/types" "^7.3.0"

"@types/eslint-visitor-keys@^1.0.0":
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/@types/eslint-visitor-keys/download/@types/eslint-visitor-keys-1.0.0.tgz#1ee30d79544ca84d68d4b3cdb0af4f205663dd2d"
  integrity sha1-HuMNeVRMqE1o1LPNsK9PIFZj3S0=

"@types/graceful-fs@^4.1.2":
  version "4.1.6"
  resolved "http://r.npm.sankuai.com/@types/graceful-fs/download/@types/graceful-fs-4.1.6.tgz#e14b2576a1c25026b7f02ede1de3b84c3a1efeae"
  integrity sha1-4UsldqHCUCa38C7eHeO4TDoe/q4=
  dependencies:
    "@types/node" "*"

"@types/hoist-non-react-statics@^3.3.0":
  version "3.3.1"
  resolved "http://r.npm.sankuai.com/@types/hoist-non-react-statics/download/@types/hoist-non-react-statics-3.3.1.tgz#1124aafe5118cb591977aeb1ceaaed1070eb039f"
  integrity sha1-ESSq/lEYy1kZd66xzqrtEHDrA58=
  dependencies:
    "@types/react" "*"
    hoist-non-react-statics "^3.3.0"

"@types/istanbul-lib-coverage@*", "@types/istanbul-lib-coverage@^2.0.0", "@types/istanbul-lib-coverage@^2.0.1":
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/@types/istanbul-lib-coverage/download/@types/istanbul-lib-coverage-2.0.4.tgz#8467d4b3c087805d63580480890791277ce35c44"
  integrity sha1-hGfUs8CHgF1jWASAiQeRJ3zjXEQ=

"@types/istanbul-lib-report@*":
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/@types/istanbul-lib-report/download/@types/istanbul-lib-report-3.0.0.tgz#c14c24f18ea8190c118ee7562b7ff99a36552686"
  integrity sha1-wUwk8Y6oGQwRjudWK3/5mjZVJoY=
  dependencies:
    "@types/istanbul-lib-coverage" "*"

"@types/istanbul-reports@^1.1.1":
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/@types/istanbul-reports/download/@types/istanbul-reports-1.1.2.tgz#e875cc689e47bce549ec81f3df5e6f6f11cfaeb2"
  integrity sha1-6HXMaJ5HvOVJ7IHz315vbxHPrrI=
  dependencies:
    "@types/istanbul-lib-coverage" "*"
    "@types/istanbul-lib-report" "*"

"@types/istanbul-reports@^3.0.0":
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/@types/istanbul-reports/download/@types/istanbul-reports-3.0.1.tgz#9153fe98bba2bd565a63add9436d6f0d7f8468ff"
  integrity sha1-kVP+mLuivVZaY63ZQ21vDX+EaP8=
  dependencies:
    "@types/istanbul-lib-report" "*"

"@types/jest@^26.0.13":
  version "26.0.24"
  resolved "http://r.npm.sankuai.com/@types/jest/download/@types/jest-26.0.24.tgz#943d11976b16739185913a1936e0de0c4a7d595a"
  integrity sha1-lD0Rl2sWc5GFkToZNuDeDEp9WVo=
  dependencies:
    jest-diff "^26.0.0"
    pretty-format "^26.0.0"

"@types/js-cookie@^2.x.x":
  version "2.2.7"
  resolved "http://r.npm.sankuai.com/@types/js-cookie/download/@types/js-cookie-2.2.7.tgz#226a9e31680835a6188e887f3988e60c04d3f6a3"
  integrity sha1-ImqeMWgINaYYjoh/OYjmDATT9qM=

"@types/json-schema@^7.0.3", "@types/json-schema@^7.0.9":
  version "7.0.11"
  resolved "http://r.npm.sankuai.com/@types/json-schema/download/@types/json-schema-7.0.11.tgz#d421b6c527a3037f7c84433fd2c4229e016863d3"
  integrity sha1-1CG2xSejA398hEM/0sQingFoY9M=

"@types/json5@^0.0.29":
  version "0.0.29"
  resolved "http://r.npm.sankuai.com/@types/json5/download/@types/json5-0.0.29.tgz#ee28707ae94e11d2b827bcbe5270bcea7f3e71ee"
  integrity sha1-7ihweulOEdK4J7y+UnC86n8+ce4=

"@types/lodash@^4.17.0":
  version "4.17.0"
  resolved "http://r.npm.sankuai.com/@types/lodash/download/@types/lodash-4.17.0.tgz#d774355e41f372d5350a4d0714abb48194a489c3"
  integrity sha1-13Q1XkHzctU1Ck0HFKu0gZSkicM=

"@types/node@*", "@types/node@16.9.1":
  version "16.9.1"
  resolved "http://r.npm.sankuai.com/@types/node/download/@types/node-16.9.1.tgz#0611b37db4246c937feef529ddcc018cf8e35708"
  integrity sha1-BhGzfbQkbJN/7vUp3cwBjPjjVwg=

"@types/normalize-package-data@^2.4.0":
  version "2.4.1"
  resolved "http://r.npm.sankuai.com/@types/normalize-package-data/download/@types/normalize-package-data-2.4.1.tgz#d3357479a0fdfdd5907fe67e17e0a85c906e1301"
  integrity sha1-0zV0eaD9/dWQf+Z+F+CoXJBuEwE=

"@types/parse-json@^4.0.0":
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/@types/parse-json/download/@types/parse-json-4.0.0.tgz#2f8bb441434d163b35fb8ffdccd7138927ffb8c0"
  integrity sha1-L4u0QUNNFjs1+4/9zNcTiSf/uMA=

"@types/prettier@^2.0.0":
  version "2.7.2"
  resolved "http://r.npm.sankuai.com/@types/prettier/download/@types/prettier-2.7.2.tgz#6c2324641cc4ba050a8c710b2b251b377581fbf0"
  integrity sha1-bCMkZBzEugUKjHELKyUbN3WB+/A=

"@types/prop-types@*":
  version "15.7.5"
  resolved "http://r.npm.sankuai.com/@types/prop-types/download/@types/prop-types-15.7.5.tgz#5f19d2b85a98e9558036f6a3cacc8819420f05cf"
  integrity sha1-XxnSuFqY6VWANvajysyIGUIPBc8=

"@types/react-redux@*":
  version "7.1.25"
  resolved "http://r.npm.sankuai.com/@types/react-redux/download/@types/react-redux-7.1.25.tgz#de841631205b24f9dfb4967dd4a7901e048f9a88"
  integrity sha1-3oQWMSBbJPnftJZ91KeQHgSPmog=
  dependencies:
    "@types/hoist-non-react-statics" "^3.3.0"
    "@types/react" "*"
    hoist-non-react-statics "^3.3.0"
    redux "^4.0.0"

"@types/react@*", "@types/react@^17.0.43":
  version "17.0.56"
  resolved "http://r.npm.sankuai.com/@types/react/download/@types/react-17.0.56.tgz#16f54a0b0a4820065b8296f1dd6da80791fcf964"
  integrity sha1-FvVKCwpIIAZbgpbx3W2oB5H8+WQ=
  dependencies:
    "@types/prop-types" "*"
    "@types/scheduler" "*"
    csstype "^3.0.2"

"@types/retry@^0.12.0":
  version "0.12.2"
  resolved "http://r.npm.sankuai.com/@types/retry/download/@types/retry-0.12.2.tgz#ed279a64fa438bb69f2480eda44937912bb7480a"
  integrity sha1-7SeaZPpDi7afJIDtpEk3kSu3SAo=

"@types/scheduler@*":
  version "0.16.3"
  resolved "http://r.npm.sankuai.com/@types/scheduler/download/@types/scheduler-0.16.3.tgz#cef09e3ec9af1d63d2a6cc5b383a737e24e6dcf5"
  integrity sha1-zvCePsmvHWPSpsxbODpzfiTm3PU=

"@types/semver@^7.3.12":
  version "7.3.13"
  resolved "http://r.npm.sankuai.com/@types/semver/download/@types/semver-7.3.13.tgz#da4bfd73f49bd541d28920ab0e2bf0ee80f71c91"
  integrity sha1-2kv9c/Sb1UHSiSCrDivw7oD3HJE=

"@types/stack-utils@^1.0.1":
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/@types/stack-utils/download/@types/stack-utils-1.0.1.tgz#0a851d3bd96498fa25c33ab7278ed3bd65f06c3e"
  integrity sha1-CoUdO9lkmPolwzq3J47TvWXwbD4=

"@types/stack-utils@^2.0.0":
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/@types/stack-utils/download/@types/stack-utils-2.0.1.tgz#20f18294f797f2209b5f65c8e3b5c8e8261d127c"
  integrity sha1-IPGClPeX8iCbX2XI47XI6CYdEnw=

"@types/yargs-parser@*":
  version "21.0.0"
  resolved "http://r.npm.sankuai.com/@types/yargs-parser/download/@types/yargs-parser-21.0.0.tgz#0c60e537fa790f5f9472ed2776c2b71ec117351b"
  integrity sha1-DGDlN/p5D1+Ucu0ndsK3HsEXNRs=

"@types/yargs@^13.0.0":
  version "13.0.12"
  resolved "http://r.npm.sankuai.com/@types/yargs/download/@types/yargs-13.0.12.tgz#d895a88c703b78af0465a9de88aa92c61430b092"
  integrity sha1-2JWojHA7eK8EZaneiKqSxhQwsJI=
  dependencies:
    "@types/yargs-parser" "*"

"@types/yargs@^15.0.0":
  version "15.0.15"
  resolved "http://r.npm.sankuai.com/@types/yargs/download/@types/yargs-15.0.15.tgz#e609a2b1ef9e05d90489c2f5f45bbfb2be092158"
  integrity sha1-5gmise+eBdkEicL19Fu/sr4JIVg=
  dependencies:
    "@types/yargs-parser" "*"

"@typescript-eslint/eslint-plugin@^3.1.0":
  version "3.10.1"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/eslint-plugin/download/@typescript-eslint/eslint-plugin-3.10.1.tgz#7e061338a1383f59edc204c605899f93dc2e2c8f"
  integrity sha1-fgYTOKE4P1ntwgTGBYmfk9wuLI8=
  dependencies:
    "@typescript-eslint/experimental-utils" "3.10.1"
    debug "^4.1.1"
    functional-red-black-tree "^1.0.1"
    regexpp "^3.0.0"
    semver "^7.3.2"
    tsutils "^3.17.1"

"@typescript-eslint/eslint-plugin@^5.30.5":
  version "5.57.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/eslint-plugin/download/@typescript-eslint/eslint-plugin-5.57.0.tgz#52c8a7a4512f10e7249ca1e2e61f81c62c34365c"
  integrity sha1-UsinpFEvEOcknKHi5h+Bxiw0Nlw=
  dependencies:
    "@eslint-community/regexpp" "^4.4.0"
    "@typescript-eslint/scope-manager" "5.57.0"
    "@typescript-eslint/type-utils" "5.57.0"
    "@typescript-eslint/utils" "5.57.0"
    debug "^4.3.4"
    grapheme-splitter "^1.0.4"
    ignore "^5.2.0"
    natural-compare-lite "^1.4.0"
    semver "^7.3.7"
    tsutils "^3.21.0"

"@typescript-eslint/experimental-utils@3.10.1":
  version "3.10.1"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/experimental-utils/download/@typescript-eslint/experimental-utils-3.10.1.tgz#e179ffc81a80ebcae2ea04e0332f8b251345a686"
  integrity sha1-4Xn/yBqA68ri6gTgMy+LJRNFpoY=
  dependencies:
    "@types/json-schema" "^7.0.3"
    "@typescript-eslint/types" "3.10.1"
    "@typescript-eslint/typescript-estree" "3.10.1"
    eslint-scope "^5.0.0"
    eslint-utils "^2.0.0"

"@typescript-eslint/parser@^3.1.0":
  version "3.10.1"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/parser/download/@typescript-eslint/parser-3.10.1.tgz#1883858e83e8b442627e1ac6f408925211155467"
  integrity sha1-GIOFjoPotEJifhrG9AiSUhEVVGc=
  dependencies:
    "@types/eslint-visitor-keys" "^1.0.0"
    "@typescript-eslint/experimental-utils" "3.10.1"
    "@typescript-eslint/types" "3.10.1"
    "@typescript-eslint/typescript-estree" "3.10.1"
    eslint-visitor-keys "^1.1.0"

"@typescript-eslint/parser@^5.30.5":
  version "5.58.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/parser/download/@typescript-eslint/parser-5.58.0.tgz#2ac4464cf48bef2e3234cb178ede5af352dddbc6"
  integrity sha1-KsRGTPSL7y4yNMsXjt5a81Ld28Y=
  dependencies:
    "@typescript-eslint/scope-manager" "5.58.0"
    "@typescript-eslint/types" "5.58.0"
    "@typescript-eslint/typescript-estree" "5.58.0"
    debug "^4.3.4"

"@typescript-eslint/scope-manager@5.57.0":
  version "5.57.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/scope-manager/download/@typescript-eslint/scope-manager-5.57.0.tgz#79ccd3fa7bde0758059172d44239e871e087ea36"
  integrity sha1-eczT+nveB1gFkXLUQjnoceCH6jY=
  dependencies:
    "@typescript-eslint/types" "5.57.0"
    "@typescript-eslint/visitor-keys" "5.57.0"

"@typescript-eslint/scope-manager@5.58.0":
  version "5.58.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/scope-manager/download/@typescript-eslint/scope-manager-5.58.0.tgz#5e023a48352afc6a87be6ce3c8e763bc9e2f0bc8"
  integrity sha1-XgI6SDUq/GqHvmzjyOdjvJ4vC8g=
  dependencies:
    "@typescript-eslint/types" "5.58.0"
    "@typescript-eslint/visitor-keys" "5.58.0"

"@typescript-eslint/type-utils@5.57.0":
  version "5.57.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/type-utils/download/@typescript-eslint/type-utils-5.57.0.tgz#98e7531c4e927855d45bd362de922a619b4319f2"
  integrity sha1-mOdTHE6SeFXUW9Ni3pIqYZtDGfI=
  dependencies:
    "@typescript-eslint/typescript-estree" "5.57.0"
    "@typescript-eslint/utils" "5.57.0"
    debug "^4.3.4"
    tsutils "^3.21.0"

"@typescript-eslint/types@3.10.1":
  version "3.10.1"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/types/download/@typescript-eslint/types-3.10.1.tgz#1d7463fa7c32d8a23ab508a803ca2fe26e758727"
  integrity sha1-HXRj+nwy2KI6tQioA8ov4m51hyc=

"@typescript-eslint/types@5.57.0":
  version "5.57.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/types/download/@typescript-eslint/types-5.57.0.tgz#727bfa2b64c73a4376264379cf1f447998eaa132"
  integrity sha1-cnv6K2THOkN2JkN5zx9EeZjqoTI=

"@typescript-eslint/types@5.58.0":
  version "5.58.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/types/download/@typescript-eslint/types-5.58.0.tgz#54c490b8522c18986004df7674c644ffe2ed77d8"
  integrity sha1-VMSQuFIsGJhgBN92dMZE/+Ltd9g=

"@typescript-eslint/typescript-estree@3.10.1":
  version "3.10.1"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/typescript-estree/download/@typescript-eslint/typescript-estree-3.10.1.tgz#fd0061cc38add4fad45136d654408569f365b853"
  integrity sha1-/QBhzDit1PrUUTbWVECFafNluFM=
  dependencies:
    "@typescript-eslint/types" "3.10.1"
    "@typescript-eslint/visitor-keys" "3.10.1"
    debug "^4.1.1"
    glob "^7.1.6"
    is-glob "^4.0.1"
    lodash "^4.17.15"
    semver "^7.3.2"
    tsutils "^3.17.1"

"@typescript-eslint/typescript-estree@5.57.0":
  version "5.57.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/typescript-estree/download/@typescript-eslint/typescript-estree-5.57.0.tgz#ebcd0ee3e1d6230e888d88cddf654252d41e2e40"
  integrity sha1-680O4+HWIw6IjYjN32VCUtQeLkA=
  dependencies:
    "@typescript-eslint/types" "5.57.0"
    "@typescript-eslint/visitor-keys" "5.57.0"
    debug "^4.3.4"
    globby "^11.1.0"
    is-glob "^4.0.3"
    semver "^7.3.7"
    tsutils "^3.21.0"

"@typescript-eslint/typescript-estree@5.58.0":
  version "5.58.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/typescript-estree/download/@typescript-eslint/typescript-estree-5.58.0.tgz#4966e6ff57eaf6e0fce2586497edc097e2ab3e61"
  integrity sha1-SWbm/1fq9uD84lhkl+3Al+KrPmE=
  dependencies:
    "@typescript-eslint/types" "5.58.0"
    "@typescript-eslint/visitor-keys" "5.58.0"
    debug "^4.3.4"
    globby "^11.1.0"
    is-glob "^4.0.3"
    semver "^7.3.7"
    tsutils "^3.21.0"

"@typescript-eslint/utils@5.57.0":
  version "5.57.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/utils/download/@typescript-eslint/utils-5.57.0.tgz#eab8f6563a2ac31f60f3e7024b91bf75f43ecef6"
  integrity sha1-6rj2Vjoqwx9g8+cCS5G/dfQ+zvY=
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@types/json-schema" "^7.0.9"
    "@types/semver" "^7.3.12"
    "@typescript-eslint/scope-manager" "5.57.0"
    "@typescript-eslint/types" "5.57.0"
    "@typescript-eslint/typescript-estree" "5.57.0"
    eslint-scope "^5.1.1"
    semver "^7.3.7"

"@typescript-eslint/utils@^5.10.0":
  version "5.58.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/utils/download/@typescript-eslint/utils-5.58.0.tgz#430d7c95f23ec457b05be5520c1700a0dfd559d5"
  integrity sha1-Qw18lfI+xFewW+VSDBcAoN/VWdU=
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@types/json-schema" "^7.0.9"
    "@types/semver" "^7.3.12"
    "@typescript-eslint/scope-manager" "5.58.0"
    "@typescript-eslint/types" "5.58.0"
    "@typescript-eslint/typescript-estree" "5.58.0"
    eslint-scope "^5.1.1"
    semver "^7.3.7"

"@typescript-eslint/visitor-keys@3.10.1":
  version "3.10.1"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/visitor-keys/download/@typescript-eslint/visitor-keys-3.10.1.tgz#cd4274773e3eb63b2e870ac602274487ecd1e931"
  integrity sha1-zUJ0dz4+tjsuhwrGAidEh+zR6TE=
  dependencies:
    eslint-visitor-keys "^1.1.0"

"@typescript-eslint/visitor-keys@5.57.0":
  version "5.57.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/visitor-keys/download/@typescript-eslint/visitor-keys-5.57.0.tgz#e2b2f4174aff1d15eef887ce3d019ecc2d7a8ac1"
  integrity sha1-4rL0F0r/HRXu+IfOPQGezC16isE=
  dependencies:
    "@typescript-eslint/types" "5.57.0"
    eslint-visitor-keys "^3.3.0"

"@typescript-eslint/visitor-keys@5.58.0":
  version "5.58.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/visitor-keys/download/@typescript-eslint/visitor-keys-5.58.0.tgz#eb9de3a61d2331829e6761ce7fd13061781168b4"
  integrity sha1-653jph0jMYKeZ2HOf9EwYXgRaLQ=
  dependencies:
    "@typescript-eslint/types" "5.58.0"
    eslint-visitor-keys "^3.3.0"

"@utiljs/console@0.1.5":
  version "0.1.5"
  resolved "http://r.npm.sankuai.com/@utiljs/console/download/@utiljs/console-0.1.5.tgz#eca3720104d6fc814c4f71dcf5c2b6acc6ce4956"
  integrity sha1-7KNyAQTW/IFMT3Hc9cK2rMbOSVY=

"@utiljs/extend@0.1.9":
  version "0.1.9"
  resolved "http://r.npm.sankuai.com/@utiljs/extend/download/@utiljs/extend-0.1.9.tgz#6f5980e67a2ab8d895dc595038a7bb8dab39337a"
  integrity sha1-b1mA5noquNiV3FlQOKe7jas5M3o=
  dependencies:
    "@utiljs/is" "0.11.10"

"@utiljs/is@0.11.10":
  version "0.11.10"
  resolved "http://r.npm.sankuai.com/@utiljs/is/download/@utiljs/is-0.11.10.tgz#3b26d426bb9078c6bfd8eacf9aba7cd6d6e1716e"
  integrity sha1-OybUJruQeMa/2OrPmrp81tbhcW4=
  dependencies:
    "@utiljs/string" "0.6.6"
    "@utiljs/type" "0.5.5"

"@utiljs/param@^0.6.11":
  version "0.6.11"
  resolved "http://r.npm.sankuai.com/@utiljs/param/download/@utiljs/param-0.6.11.tgz#a3eb83a4d97c94972fedf75aa1be47d827546dd0"
  integrity sha1-o+uDpNl8lJcv7fdaob5H2CdUbdA=
  dependencies:
    "@utiljs/extend" "0.1.9"
    "@utiljs/type" "0.5.5"

"@utiljs/string@0.6.6":
  version "0.6.6"
  resolved "http://r.npm.sankuai.com/@utiljs/string/download/@utiljs/string-0.6.6.tgz#628571ec5c0ac12bb1ea3526586f9eed774c9b7f"
  integrity sha1-YoVx7FwKwSux6jUmWG+e7XdMm38=
  dependencies:
    "@utiljs/console" "0.1.5"
    "@utiljs/type" "0.5.4"

"@utiljs/type@0.5.4":
  version "0.5.4"
  resolved "http://r.npm.sankuai.com/@utiljs/type/download/@utiljs/type-0.5.4.tgz#9638197892b5d43a1857bbc1c6507c0dd00d18c6"
  integrity sha1-ljgZeJK11DoYV7vBxlB8DdANGMY=

"@utiljs/type@0.5.5":
  version "0.5.5"
  resolved "http://r.npm.sankuai.com/@utiljs/type/download/@utiljs/type-0.5.5.tgz#5bc799133a85118fffbffaf1c6b5d14b0ca2e8cc"
  integrity sha1-W8eZEzqFEY//v/rxxrXRSwyi6Mw=

"@yarnpkg/lockfile@^1.1.0":
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/@yarnpkg/lockfile/download/@yarnpkg/lockfile-1.1.0.tgz#e77a97fbd345b76d83245edcd17d393b1b41fb31"
  integrity sha1-53qX+9NFt22DJF7c0X05OxtB+zE=

abab@^2.0.3, abab@^2.0.5:
  version "2.0.6"
  resolved "http://r.npm.sankuai.com/abab/download/abab-2.0.6.tgz#41b80f2c871d19686216b82309231cfd3cb3d291"
  integrity sha1-QbgPLIcdGWhiFrgjCSMc/Tyz0pE=

abort-controller@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/abort-controller/download/abort-controller-3.0.0.tgz#eaf54d53b62bae4138e809ca225c8439a6efb392"
  integrity sha1-6vVNU7YrrkE46AnKIlyEOabvs5I=
  dependencies:
    event-target-shim "^5.0.0"

absolute-path@^0.0.0:
  version "0.0.0"
  resolved "http://r.npm.sankuai.com/absolute-path/download/absolute-path-0.0.0.tgz#a78762fbdadfb5297be99b15d35a785b2f095bf7"
  integrity sha1-p4di+9rftSl76ZsV01p4Wy8JW/c=

accepts@~1.3.5, accepts@~1.3.7:
  version "1.3.8"
  resolved "http://r.npm.sankuai.com/accepts/download/accepts-1.3.8.tgz#0bf0be125b67014adcb0b0921e62db7bffe16b2e"
  integrity sha1-C/C+EltnAUrcsLCSHmLbe//hay4=
  dependencies:
    mime-types "~2.1.34"
    negotiator "0.6.3"

acorn-globals@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/acorn-globals/download/acorn-globals-6.0.0.tgz#46cdd39f0f8ff08a876619b55f5ac8a6dc770b45"
  integrity sha1-Rs3Tnw+P8IqHZhm1X1rIptx3C0U=
  dependencies:
    acorn "^7.1.1"
    acorn-walk "^7.1.1"

acorn-jsx@^5.3.1:
  version "5.3.2"
  resolved "http://r.npm.sankuai.com/acorn-jsx/download/acorn-jsx-5.3.2.tgz#7ed5bb55908b3b2f1bc55c6af1653bada7f07937"
  integrity sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=

acorn-walk@^7.1.1:
  version "7.2.0"
  resolved "http://r.npm.sankuai.com/acorn-walk/download/acorn-walk-7.2.0.tgz#0de889a601203909b0fbe07b8938dc21d2e967bc"
  integrity sha1-DeiJpgEgOQmw++B7iTjcIdLpZ7w=

acorn@^7.1.1, acorn@^7.4.0:
  version "7.4.1"
  resolved "http://r.npm.sankuai.com/acorn/download/acorn-7.4.1.tgz#feaed255973d2e77555b83dbc08851a6c63520fa"
  integrity sha1-/q7SVZc9LndVW4PbwIhRpsY1IPo=

acorn@^8.2.4:
  version "8.8.2"
  resolved "http://r.npm.sankuai.com/acorn/download/acorn-8.8.2.tgz#1b2f25db02af965399b9776b0c2c391276d37c4a"
  integrity sha1-Gy8l2wKvllOZuXdrDCw5EnbTfEo=

agent-base@6:
  version "6.0.2"
  resolved "http://r.npm.sankuai.com/agent-base/download/agent-base-6.0.2.tgz#49fff58577cfee3f37176feab4c22e00f86d7f77"
  integrity sha1-Sf/1hXfP7j83F2/qtMIuAPhtf3c=
  dependencies:
    debug "4"

aggregate-error@^3.0.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/aggregate-error/download/aggregate-error-3.1.0.tgz#92670ff50f5359bdb7a3e0d40d0ec30c5737687a"
  integrity sha1-kmcP9Q9TWb23o+DUDQ7DDFc3aHo=
  dependencies:
    clean-stack "^2.0.0"
    indent-string "^4.0.0"

ahooks-v3-count@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/ahooks-v3-count/download/ahooks-v3-count-1.0.0.tgz#ddeb392e009ad6e748905b3cbf63a9fd8262ca80"
  integrity sha1-3es5LgCa1udIkFs8v2Op/YJiyoA=

ahooks@^3.7.8:
  version "3.7.8"
  resolved "http://r.npm.sankuai.com/ahooks/download/ahooks-3.7.8.tgz#3fa3c491cd153e884a32b0c4192fc72cf84c4332"
  integrity sha1-P6PEkc0VPohKMrDEGS/HLPhMQzI=
  dependencies:
    "@babel/runtime" "^7.21.0"
    "@types/js-cookie" "^2.x.x"
    ahooks-v3-count "^1.0.0"
    dayjs "^1.9.1"
    intersection-observer "^0.12.0"
    js-cookie "^2.x.x"
    lodash "^4.17.21"
    resize-observer-polyfill "^1.5.1"
    screenfull "^5.0.0"
    tslib "^2.4.1"

ajv@^6.10.0, ajv@^6.12.4:
  version "6.12.6"
  resolved "http://r.npm.sankuai.com/ajv/download/ajv-6.12.6.tgz#baf5a62e802b07d977034586f8c3baf5adf26df4"
  integrity sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ajv@^8.0.1:
  version "8.12.0"
  resolved "http://r.npm.sankuai.com/ajv/download/ajv-8.12.0.tgz#d1a0527323e22f53562c567c00991577dfbe19d1"
  integrity sha1-0aBScyPiL1NWLFZ8AJkVd9++GdE=
  dependencies:
    fast-deep-equal "^3.1.1"
    json-schema-traverse "^1.0.0"
    require-from-string "^2.0.2"
    uri-js "^4.2.2"

anser@^1.4.9:
  version "1.4.10"
  resolved "http://r.npm.sankuai.com/anser/download/anser-1.4.10.tgz#befa3eddf282684bd03b63dcda3927aef8c2e35b"
  integrity sha1-vvo+3fKCaEvQO2Pc2jknrvjC41s=

ansi-colors@^1.0.1:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/ansi-colors/download/ansi-colors-1.1.0.tgz#6374b4dd5d4718ff3ce27a671a3b1cad077132a9"
  integrity sha1-Y3S03V1HGP884npnGjscrQdxMqk=
  dependencies:
    ansi-wrap "^0.1.0"

ansi-colors@^4.1.1:
  version "4.1.3"
  resolved "http://r.npm.sankuai.com/ansi-colors/download/ansi-colors-4.1.3.tgz#37611340eb2243e70cc604cad35d63270d48781b"
  integrity sha1-N2ETQOsiQ+cMxgTK011jJw1IeBs=

ansi-cyan@^0.1.1:
  version "0.1.1"
  resolved "http://r.npm.sankuai.com/ansi-cyan/download/ansi-cyan-0.1.1.tgz#538ae528af8982f28ae30d86f2f17456d2609873"
  integrity sha1-U4rlKK+JgvKK4w2G8vF0VtJgmHM=
  dependencies:
    ansi-wrap "0.1.0"

ansi-escapes@^3.0.0:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/ansi-escapes/download/ansi-escapes-3.2.0.tgz#8780b98ff9dbf5638152d1f1fe5c1d7b4442976b"
  integrity sha1-h4C5j/nb9WOBUtHx/lwde0RCl2s=

ansi-escapes@^4.2.1, ansi-escapes@^4.3.0:
  version "4.3.2"
  resolved "http://r.npm.sankuai.com/ansi-escapes/download/ansi-escapes-4.3.2.tgz#6b2291d1db7d98b6521d5f1efa42d0f3a9feb65e"
  integrity sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4=
  dependencies:
    type-fest "^0.21.3"

ansi-fragments@^0.2.1:
  version "0.2.1"
  resolved "http://r.npm.sankuai.com/ansi-fragments/download/ansi-fragments-0.2.1.tgz#24409c56c4cc37817c3d7caa99d8969e2de5a05e"
  integrity sha1-JECcVsTMN4F8PXyqmdiWni3loF4=
  dependencies:
    colorette "^1.0.7"
    slice-ansi "^2.0.0"
    strip-ansi "^5.0.0"

ansi-gray@^0.1.1:
  version "0.1.1"
  resolved "http://r.npm.sankuai.com/ansi-gray/download/ansi-gray-0.1.1.tgz#2962cf54ec9792c48510a3deb524436861ef7251"
  integrity sha1-KWLPVOyXksSFEKPetSRDaGHvclE=
  dependencies:
    ansi-wrap "0.1.0"

ansi-red@^0.1.1:
  version "0.1.1"
  resolved "http://r.npm.sankuai.com/ansi-red/download/ansi-red-0.1.1.tgz#8c638f9d1080800a353c9c28c8a81ca4705d946c"
  integrity sha1-jGOPnRCAgAo1PJwoyKgcpHBdlGw=
  dependencies:
    ansi-wrap "0.1.0"

ansi-regex@^2.0.0:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-2.1.1.tgz#c3b33ab5ee360d86e0e628f0468ae7ef27d654df"
  integrity sha1-w7M6te42DYbg5ijwRorn7yfWVN8=

ansi-regex@^3.0.0:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-3.0.1.tgz#123d6479e92ad45ad897d4054e3c7ca7db4944e1"
  integrity sha1-Ej1keekq1FrYl9QFTjx8p9tJROE=

ansi-regex@^4.0.0, ansi-regex@^4.1.0:
  version "4.1.1"
  resolved "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-4.1.1.tgz#164daac87ab2d6f6db3a29875e2d1766582dabed"
  integrity sha1-Fk2qyHqy1vbbOimHXi0XZlgtq+0=

ansi-regex@^5.0.0, ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-5.0.1.tgz#082cb2c89c9fe8659a311a53bd6a4dc5301db304"
  integrity sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=

ansi-regex@^6.0.1:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-6.0.1.tgz#3183e38fae9a65d7cb5e53945cd5897d0260a06a"
  integrity sha1-MYPjj66aZdfLXlOUXNWJfQJgoGo=

ansi-styles@^3.2.0, ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-3.2.1.tgz#41fbb20243e50b12be0f04b8dedbf07520ce841d"
  integrity sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-4.3.0.tgz#edd803628ae71c04c85ae7a0906edad34b648937"
  integrity sha1-7dgDYornHATIWuegkG7a00tkiTc=
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^5.0.0:
  version "5.2.0"
  resolved "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-5.2.0.tgz#07449690ad45777d1924ac2abb2fc8895dba836b"
  integrity sha1-B0SWkK1Fd30ZJKwquy/IiV26g2s=

ansi-styles@^6.0.0:
  version "6.2.1"
  resolved "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-6.2.1.tgz#0e62320cf99c21afff3b3012192546aacbfb05c5"
  integrity sha1-DmIyDPmcIa//OzASGSVGqsv7BcU=

ansi-wrap@0.1.0, ansi-wrap@^0.1.0:
  version "0.1.0"
  resolved "http://r.npm.sankuai.com/ansi-wrap/download/ansi-wrap-0.1.0.tgz#a82250ddb0015e9a27ca82e82ea603bbfa45efaf"
  integrity sha1-qCJQ3bABXponyoLoLqYDu/pF768=

anymatch@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/anymatch/download/anymatch-2.0.0.tgz#bcb24b4f37934d9aa7ac17b4adaf89e7c76ef2eb"
  integrity sha1-vLJLTzeTTZqnrBe0ra+J58du8us=
  dependencies:
    micromatch "^3.1.4"
    normalize-path "^2.1.1"

anymatch@^3.0.3:
  version "3.1.3"
  resolved "http://r.npm.sankuai.com/anymatch/download/anymatch-3.1.3.tgz#790c58b19ba1720a84205b57c618d5ad8524973e"
  integrity sha1-eQxYsZuhcgqEIFtXxhjVrYUklz4=
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

archive-type@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/archive-type/download/archive-type-4.0.0.tgz#f92e72233056dfc6969472749c267bdb046b1d70"
  integrity sha1-+S5yIzBW38aWlHJ0nCZ72wRrHXA=
  dependencies:
    file-type "^4.2.0"

archiver-utils@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/archiver-utils/download/archiver-utils-2.1.0.tgz#e8a460e94b693c3e3da182a098ca6285ba9249e2"
  integrity sha1-6KRg6UtpPD49oYKgmMpihbqSSeI=
  dependencies:
    glob "^7.1.4"
    graceful-fs "^4.2.0"
    lazystream "^1.0.0"
    lodash.defaults "^4.2.0"
    lodash.difference "^4.5.0"
    lodash.flatten "^4.4.0"
    lodash.isplainobject "^4.0.6"
    lodash.union "^4.6.0"
    normalize-path "^3.0.0"
    readable-stream "^2.0.0"

archiver@^5.0.2:
  version "5.3.1"
  resolved "http://r.npm.sankuai.com/archiver/download/archiver-5.3.1.tgz#21e92811d6f09ecfce649fbefefe8c79e57cbbb6"
  integrity sha1-IekoEdbwns/OZJ++/v6MeeV8u7Y=
  dependencies:
    archiver-utils "^2.1.0"
    async "^3.2.3"
    buffer-crc32 "^0.2.1"
    readable-stream "^3.6.0"
    readdir-glob "^1.0.0"
    tar-stream "^2.2.0"
    zip-stream "^4.1.0"

argparse@^1.0.7:
  version "1.0.10"
  resolved "http://r.npm.sankuai.com/argparse/download/argparse-1.0.10.tgz#bcd6791ea5ae09725e17e5ad988134cd40b3d911"
  integrity sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=
  dependencies:
    sprintf-js "~1.0.2"

arr-diff@^1.0.1:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/arr-diff/download/arr-diff-1.1.0.tgz#687c32758163588fef7de7b36fabe495eb1a399a"
  integrity sha1-aHwydYFjWI/vfeezb6vklesaOZo=
  dependencies:
    arr-flatten "^1.0.1"
    array-slice "^0.2.3"

arr-diff@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/arr-diff/download/arr-diff-4.0.0.tgz#d6461074febfec71e7e15235761a329a5dc7c520"
  integrity sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=

arr-flatten@^1.0.1, arr-flatten@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/arr-flatten/download/arr-flatten-1.1.0.tgz#36048bbff4e7b47e136644316c99669ea5ae91f1"
  integrity sha1-NgSLv/TntH4TZkQxbJlmnqWukfE=

arr-union@^2.0.1:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/arr-union/download/arr-union-2.1.0.tgz#20f9eab5ec70f5c7d215b1077b1c39161d292c7d"
  integrity sha1-IPnqtexw9cfSFbEHexw5Fh0pLH0=

arr-union@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/arr-union/download/arr-union-3.1.0.tgz#e39b09aea9def866a8f206e288af63919bae39c4"
  integrity sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ=

array-buffer-byte-length@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/array-buffer-byte-length/download/array-buffer-byte-length-1.0.0.tgz#fabe8bc193fea865f317fe7807085ee0dee5aead"
  integrity sha1-+r6LwZP+qGXzF/54Bwhe4N7lrq0=
  dependencies:
    call-bind "^1.0.2"
    is-array-buffer "^3.0.1"

array-buffer-byte-length@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/array-buffer-byte-length/download/array-buffer-byte-length-1.0.1.tgz#1e5583ec16763540a27ae52eed99ff899223568f"
  integrity sha1-HlWD7BZ2NUCieuUu7Zn/iZIjVo8=
  dependencies:
    call-bind "^1.0.5"
    is-array-buffer "^3.0.4"

array-filter@~0.0.0:
  version "0.0.1"
  resolved "http://r.npm.sankuai.com/array-filter/download/array-filter-0.0.1.tgz#7da8cf2e26628ed732803581fd21f67cacd2eeec"
  integrity sha1-fajPLiZijtcygDWB/SH2fKzS7uw=

array-includes@^3.1.5, array-includes@^3.1.6:
  version "3.1.6"
  resolved "http://r.npm.sankuai.com/array-includes/download/array-includes-3.1.6.tgz#9e9e720e194f198266ba9e18c29e6a9b0e4b225f"
  integrity sha1-np5yDhlPGYJmup4Ywp5qmw5LIl8=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"
    get-intrinsic "^1.1.3"
    is-string "^1.0.7"

array-includes@^3.1.7:
  version "3.1.8"
  resolved "http://r.npm.sankuai.com/array-includes/download/array-includes-3.1.8.tgz#5e370cbe172fdd5dd6530c1d4aadda25281ba97d"
  integrity sha1-XjcMvhcv3V3WUwwdSq3aJSgbqX0=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-object-atoms "^1.0.0"
    get-intrinsic "^1.2.4"
    is-string "^1.0.7"

array-map@~0.0.0:
  version "0.0.0"
  resolved "http://r.npm.sankuai.com/array-map/download/array-map-0.0.0.tgz#88a2bab73d1cf7bcd5c1b118a003f66f665fa662"
  integrity sha1-iKK6tz0c97zVwbEYoAP2b2ZfpmI=

array-reduce@~0.0.0:
  version "0.0.0"
  resolved "http://r.npm.sankuai.com/array-reduce/download/array-reduce-0.0.0.tgz#173899d3ffd1c7d9383e4479525dbe278cab5f2b"
  integrity sha1-FziZ0//Rx9k4PkR5Ul2+J4yrXys=

array-slice@^0.2.3:
  version "0.2.3"
  resolved "http://r.npm.sankuai.com/array-slice/download/array-slice-0.2.3.tgz#dd3cfb80ed7973a75117cdac69b0b99ec86186f5"
  integrity sha1-3Tz7gO15c6dRF82sabC5nshhhvU=

array-union@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/array-union/download/array-union-2.1.0.tgz#b798420adbeb1de828d84acd8a2e23d3efe85e8d"
  integrity sha1-t5hCCtvrHego2ErNii4j0+/oXo0=

array-unique@^0.3.2:
  version "0.3.2"
  resolved "http://r.npm.sankuai.com/array-unique/download/array-unique-0.3.2.tgz#a894b75d4bc4f6cd679ef3244a9fd8f46ae2d428"
  integrity sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg=

array.prototype.findlastindex@^1.2.3:
  version "1.2.5"
  resolved "http://r.npm.sankuai.com/array.prototype.findlastindex/download/array.prototype.findlastindex-1.2.5.tgz#8c35a755c72908719453f87145ca011e39334d0d"
  integrity sha1-jDWnVccpCHGUU/hxRcoBHjkzTQ0=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    es-shim-unscopables "^1.0.2"

array.prototype.flat@^1.3.2:
  version "1.3.2"
  resolved "http://r.npm.sankuai.com/array.prototype.flat/download/array.prototype.flat-1.3.2.tgz#1476217df8cff17d72ee8f3ba06738db5b387d18"
  integrity sha1-FHYhffjP8X1y7o87oGc421s4fRg=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    es-shim-unscopables "^1.0.0"

array.prototype.flatmap@^1.3.1:
  version "1.3.1"
  resolved "http://r.npm.sankuai.com/array.prototype.flatmap/download/array.prototype.flatmap-1.3.1.tgz#1aae7903c2100433cb8261cd4ed310aab5c4a183"
  integrity sha1-Gq55A8IQBDPLgmHNTtMQqrXEoYM=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"
    es-shim-unscopables "^1.0.0"

array.prototype.flatmap@^1.3.2:
  version "1.3.2"
  resolved "http://r.npm.sankuai.com/array.prototype.flatmap/download/array.prototype.flatmap-1.3.2.tgz#c9a7c6831db8e719d6ce639190146c24bbd3e527"
  integrity sha1-yafGgx245xnWzmORkBRsJLvT5Sc=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    es-shim-unscopables "^1.0.0"

array.prototype.tosorted@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/array.prototype.tosorted/download/array.prototype.tosorted-1.1.1.tgz#ccf44738aa2b5ac56578ffda97c03fd3e23dd532"
  integrity sha1-zPRHOKorWsVleP/al8A/0+I91TI=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"
    es-shim-unscopables "^1.0.0"
    get-intrinsic "^1.1.3"

arraybuffer.prototype.slice@^1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/arraybuffer.prototype.slice/download/arraybuffer.prototype.slice-1.0.3.tgz#097972f4255e41bc3425e37dc3f6421cf9aefde6"
  integrity sha1-CXly9CVeQbw0JeN9w/ZCHPmu/eY=
  dependencies:
    array-buffer-byte-length "^1.0.1"
    call-bind "^1.0.5"
    define-properties "^1.2.1"
    es-abstract "^1.22.3"
    es-errors "^1.2.1"
    get-intrinsic "^1.2.3"
    is-array-buffer "^3.0.4"
    is-shared-array-buffer "^1.0.2"

art@^0.10.3:
  version "0.10.3"
  resolved "http://r.npm.sankuai.com/art/download/art-0.10.3.tgz#b01d84a968ccce6208df55a733838c96caeeaea2"
  integrity sha1-sB2EqWjMzmII31WnM4OMlsrurqI=

asap@~2.0.3:
  version "2.0.6"
  resolved "http://r.npm.sankuai.com/asap/download/asap-2.0.6.tgz#e50347611d7e690943208bbdafebcbc2fb866d46"
  integrity sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY=

assign-symbols@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/assign-symbols/download/assign-symbols-1.0.0.tgz#59667f41fadd4f20ccbc2bb96b8d4f7f78ec0367"
  integrity sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c=

astral-regex@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/astral-regex/download/astral-regex-1.0.0.tgz#6c8c3fb827dd43ee3918f27b82782ab7658a6fd9"
  integrity sha1-bIw/uCfdQ+45GPJ7gngqt2WKb9k=

astral-regex@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/astral-regex/download/astral-regex-2.0.0.tgz#483143c567aeed4785759c0865786dc77d7d2e31"
  integrity sha1-SDFDxWeu7UeFdZwIZXhtx319LjE=

async-validator@^1.10.0:
  version "1.12.2"
  resolved "http://r.npm.sankuai.com/async-validator/download/async-validator-1.12.2.tgz#beae671e7174d2938b7b4b69d2fb7e722b7fd72c"
  integrity sha1-vq5nHnF00pOLe0tp0vt+cit/1yw=

async@^2.4.0, async@^2.6.4:
  version "2.6.4"
  resolved "http://r.npm.sankuai.com/async/download/async-2.6.4.tgz#706b7ff6084664cd7eae713f6f965433b5504221"
  integrity sha1-cGt/9ghGZM1+rnE/b5ZUM7VQQiE=
  dependencies:
    lodash "^4.17.14"

async@^3.2.3:
  version "3.2.4"
  resolved "http://r.npm.sankuai.com/async/download/async-3.2.4.tgz#2d22e00f8cddeb5fde5dd33522b56d1cf569a81c"
  integrity sha1-LSLgD4zd61/eXdM1IrVtHPVpqBw=

asynckit@^0.4.0:
  version "0.4.0"
  resolved "http://r.npm.sankuai.com/asynckit/download/asynckit-0.4.0.tgz#c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79"
  integrity sha1-x57Zf380y48robyXkLzDZkdLS3k=

atob@^2.1.2:
  version "2.1.2"
  resolved "http://r.npm.sankuai.com/atob/download/atob-2.1.2.tgz#6d9517eb9e030d2436666651e86bd9f6f13533c9"
  integrity sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k=

available-typed-arrays@^1.0.5:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/available-typed-arrays/download/available-typed-arrays-1.0.5.tgz#92f95616501069d07d10edb2fc37d3e1c65123b7"
  integrity sha1-kvlWFlAQadB9EO2y/DfT4cZRI7c=

available-typed-arrays@^1.0.7:
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/available-typed-arrays/download/available-typed-arrays-1.0.7.tgz#a5cc375d6a03c2efc87a553f3e0b1522def14846"
  integrity sha1-pcw3XWoDwu/IelU/PgsVIt7xSEY=
  dependencies:
    possible-typed-array-names "^1.0.0"

axios@^0.24.0:
  version "0.24.0"
  resolved "http://r.npm.sankuai.com/axios/download/axios-0.24.0.tgz#804e6fa1e4b9c5288501dd9dff56a7a0940d20d6"
  integrity sha1-gE5voeS5xSiFAd2d/1anoJQNINY=
  dependencies:
    follow-redirects "^1.14.4"

babel-eslint@^10.1.0:
  version "10.1.0"
  resolved "http://r.npm.sankuai.com/babel-eslint/download/babel-eslint-10.1.0.tgz#6968e568a910b78fb3779cdd8b6ac2f479943232"
  integrity sha1-aWjlaKkQt4+zd5zdi2rC9HmUMjI=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@babel/parser" "^7.7.0"
    "@babel/traverse" "^7.7.0"
    "@babel/types" "^7.7.0"
    eslint-visitor-keys "^1.0.0"
    resolve "^1.12.0"

babel-helper-evaluate-path@^0.5.0:
  version "0.5.0"
  resolved "http://r.npm.sankuai.com/babel-helper-evaluate-path/download/babel-helper-evaluate-path-0.5.0.tgz#a62fa9c4e64ff7ea5cea9353174ef023a900a67c"
  integrity sha1-pi+pxOZP9+pc6pNTF07wI6kApnw=

babel-helper-mark-eval-scopes@^0.4.3:
  version "0.4.3"
  resolved "http://r.npm.sankuai.com/babel-helper-mark-eval-scopes/download/babel-helper-mark-eval-scopes-0.4.3.tgz#d244a3bef9844872603ffb46e22ce8acdf551562"
  integrity sha1-0kSjvvmESHJgP/tG4izorN9VFWI=

babel-helper-remove-or-void@^0.4.3:
  version "0.4.3"
  resolved "http://r.npm.sankuai.com/babel-helper-remove-or-void/download/babel-helper-remove-or-void-0.4.3.tgz#a4f03b40077a0ffe88e45d07010dee241ff5ae60"
  integrity sha1-pPA7QAd6D/6I5F0HAQ3uJB/1rmA=

babel-jest@^26.6.3:
  version "26.6.3"
  resolved "http://r.npm.sankuai.com/babel-jest/download/babel-jest-26.6.3.tgz#d87d25cb0037577a0c89f82e5755c5d293c01056"
  integrity sha1-2H0lywA3V3oMifguV1XF0pPAEFY=
  dependencies:
    "@jest/transform" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/babel__core" "^7.1.7"
    babel-plugin-istanbul "^6.0.0"
    babel-preset-jest "^26.6.2"
    chalk "^4.0.0"
    graceful-fs "^4.2.4"
    slash "^3.0.0"

babel-plugin-istanbul@^6.0.0:
  version "6.1.1"
  resolved "http://r.npm.sankuai.com/babel-plugin-istanbul/download/babel-plugin-istanbul-6.1.1.tgz#fa88ec59232fd9b4e36dbbc540a8ec9a9b47da73"
  integrity sha1-+ojsWSMv2bTjbbvFQKjsmptH2nM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@istanbuljs/load-nyc-config" "^1.0.0"
    "@istanbuljs/schema" "^0.1.2"
    istanbul-lib-instrument "^5.0.4"
    test-exclude "^6.0.0"

babel-plugin-jest-hoist@^26.6.2:
  version "26.6.2"
  resolved "http://r.npm.sankuai.com/babel-plugin-jest-hoist/download/babel-plugin-jest-hoist-26.6.2.tgz#8185bd030348d254c6d7dd974355e6a28b21e62d"
  integrity sha1-gYW9AwNI0lTG192XQ1Xmoosh5i0=
  dependencies:
    "@babel/template" "^7.3.3"
    "@babel/types" "^7.3.3"
    "@types/babel__core" "^7.0.0"
    "@types/babel__traverse" "^7.0.6"

babel-plugin-minify-dead-code-elimination@^0.5.1:
  version "0.5.2"
  resolved "http://r.npm.sankuai.com/babel-plugin-minify-dead-code-elimination/download/babel-plugin-minify-dead-code-elimination-0.5.2.tgz#f386ceec77a80cc4e76022a04c21b7d68e0aa5eb"
  integrity sha1-84bO7HeoDMTnYCKgTCG31o4Kpes=
  dependencies:
    babel-helper-evaluate-path "^0.5.0"
    babel-helper-mark-eval-scopes "^0.4.3"
    babel-helper-remove-or-void "^0.4.3"
    lodash "^4.17.11"

babel-plugin-module-resolver@^4.0.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/babel-plugin-module-resolver/download/babel-plugin-module-resolver-4.1.0.tgz#22a4f32f7441727ec1fbf4967b863e1e3e9f33e2"
  integrity sha1-IqTzL3RBcn7B+/SWe4Y+Hj6fM+I=
  dependencies:
    find-babel-config "^1.2.0"
    glob "^7.1.6"
    pkg-up "^3.1.0"
    reselect "^4.0.0"
    resolve "^1.13.1"

babel-plugin-polyfill-corejs2@^0.3.3:
  version "0.3.3"
  resolved "http://r.npm.sankuai.com/babel-plugin-polyfill-corejs2/download/babel-plugin-polyfill-corejs2-0.3.3.tgz#5d1bd3836d0a19e1b84bbf2d9640ccb6f951c122"
  integrity sha1-XRvTg20KGeG4S78tlkDMtvlRwSI=
  dependencies:
    "@babel/compat-data" "^7.17.7"
    "@babel/helper-define-polyfill-provider" "^0.3.3"
    semver "^6.1.1"

babel-plugin-polyfill-corejs3@^0.6.0:
  version "0.6.0"
  resolved "http://r.npm.sankuai.com/babel-plugin-polyfill-corejs3/download/babel-plugin-polyfill-corejs3-0.6.0.tgz#56ad88237137eade485a71b52f72dbed57c6230a"
  integrity sha1-Vq2II3E36t5IWnG1L3Lb7VfGIwo=
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.3.3"
    core-js-compat "^3.25.1"

babel-plugin-polyfill-regenerator@^0.4.1:
  version "0.4.1"
  resolved "http://r.npm.sankuai.com/babel-plugin-polyfill-regenerator/download/babel-plugin-polyfill-regenerator-0.4.1.tgz#390f91c38d90473592ed43351e801a9d3e0fd747"
  integrity sha1-OQ+Rw42QRzWS7UM1HoAanT4P10c=
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.3.3"

babel-plugin-syntax-trailing-function-commas@^7.0.0-beta.0:
  version "7.0.0-beta.0"
  resolved "http://r.npm.sankuai.com/babel-plugin-syntax-trailing-function-commas/download/babel-plugin-syntax-trailing-function-commas-7.0.0-beta.0.tgz#aa213c1435e2bffeb6fca842287ef534ad05d5cf"
  integrity sha1-qiE8FDXiv/62/KhCKH71NK0F1c8=

babel-plugin-transform-define@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/babel-plugin-transform-define/download/babel-plugin-transform-define-2.1.0.tgz#32d2e97cf40cffc1b809756a258b611e72036e41"
  integrity sha1-MtLpfPQM/8G4CXVqJYthHnIDbkE=
  dependencies:
    lodash "^4.17.11"
    traverse "0.6.6"

babel-plugin-transform-remove-console@^6.9.4:
  version "6.9.4"
  resolved "http://r.npm.sankuai.com/babel-plugin-transform-remove-console/download/babel-plugin-transform-remove-console-6.9.4.tgz#b980360c067384e24b357a588d807d3c83527780"
  integrity sha1-uYA2DAZzhOJLNXpYjYB9PINSd4A=

babel-preset-current-node-syntax@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/babel-preset-current-node-syntax/download/babel-preset-current-node-syntax-1.0.1.tgz#b4399239b89b2a011f9ddbe3e4f401fc40cff73b"
  integrity sha1-tDmSObibKgEfndvj5PQB/EDP9zs=
  dependencies:
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-bigint" "^7.8.3"
    "@babel/plugin-syntax-class-properties" "^7.8.3"
    "@babel/plugin-syntax-import-meta" "^7.8.3"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.8.3"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.8.3"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-top-level-await" "^7.8.3"

babel-preset-fbjs@^3.2.0, babel-preset-fbjs@^3.3.0:
  version "3.4.0"
  resolved "http://r.npm.sankuai.com/babel-preset-fbjs/download/babel-preset-fbjs-3.4.0.tgz#38a14e5a7a3b285a3f3a86552d650dca5cf6111c"
  integrity sha1-OKFOWno7KFo/OoZVLWUNylz2ERw=
  dependencies:
    "@babel/plugin-proposal-class-properties" "^7.0.0"
    "@babel/plugin-proposal-object-rest-spread" "^7.0.0"
    "@babel/plugin-syntax-class-properties" "^7.0.0"
    "@babel/plugin-syntax-flow" "^7.0.0"
    "@babel/plugin-syntax-jsx" "^7.0.0"
    "@babel/plugin-syntax-object-rest-spread" "^7.0.0"
    "@babel/plugin-transform-arrow-functions" "^7.0.0"
    "@babel/plugin-transform-block-scoped-functions" "^7.0.0"
    "@babel/plugin-transform-block-scoping" "^7.0.0"
    "@babel/plugin-transform-classes" "^7.0.0"
    "@babel/plugin-transform-computed-properties" "^7.0.0"
    "@babel/plugin-transform-destructuring" "^7.0.0"
    "@babel/plugin-transform-flow-strip-types" "^7.0.0"
    "@babel/plugin-transform-for-of" "^7.0.0"
    "@babel/plugin-transform-function-name" "^7.0.0"
    "@babel/plugin-transform-literals" "^7.0.0"
    "@babel/plugin-transform-member-expression-literals" "^7.0.0"
    "@babel/plugin-transform-modules-commonjs" "^7.0.0"
    "@babel/plugin-transform-object-super" "^7.0.0"
    "@babel/plugin-transform-parameters" "^7.0.0"
    "@babel/plugin-transform-property-literals" "^7.0.0"
    "@babel/plugin-transform-react-display-name" "^7.0.0"
    "@babel/plugin-transform-react-jsx" "^7.0.0"
    "@babel/plugin-transform-shorthand-properties" "^7.0.0"
    "@babel/plugin-transform-spread" "^7.0.0"
    "@babel/plugin-transform-template-literals" "^7.0.0"
    babel-plugin-syntax-trailing-function-commas "^7.0.0-beta.0"

babel-preset-jest@^26.6.2:
  version "26.6.2"
  resolved "http://r.npm.sankuai.com/babel-preset-jest/download/babel-preset-jest-26.6.2.tgz#747872b1171df032252426586881d62d31798fee"
  integrity sha1-dHhysRcd8DIlJCZYaIHWLTF5j+4=
  dependencies:
    babel-plugin-jest-hoist "^26.6.2"
    babel-preset-current-node-syntax "^1.0.0"

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/balanced-match/download/balanced-match-1.0.2.tgz#e83e3a7e3f300b34cb9d87f615fa0cbf357690ee"
  integrity sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=

base64-js@^1.1.2, base64-js@^1.3.1, base64-js@^1.5.1:
  version "1.5.1"
  resolved "http://r.npm.sankuai.com/base64-js/download/base64-js-1.5.1.tgz#1b1b440160a5bf7ad40b650f095963481903930a"
  integrity sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=

base@^0.11.1:
  version "0.11.2"
  resolved "http://r.npm.sankuai.com/base/download/base-0.11.2.tgz#7bde5ced145b6d551a90db87f83c558b4eb48a8f"
  integrity sha1-e95c7RRbbVUakNuH+DxVi060io8=
  dependencies:
    cache-base "^1.0.1"
    class-utils "^0.3.5"
    component-emitter "^1.2.1"
    define-property "^1.0.0"
    isobject "^3.0.1"
    mixin-deep "^1.2.0"
    pascalcase "^0.1.1"

big-integer@1.6.x:
  version "1.6.51"
  resolved "http://r.npm.sankuai.com/big-integer/download/big-integer-1.6.51.tgz#0df92a5d9880560d3ff2d5fd20245c889d130686"
  integrity sha1-DfkqXZiAVg0/8tX9ICRciJ0TBoY=

bindings@^1.5.0:
  version "1.5.0"
  resolved "http://r.npm.sankuai.com/bindings/download/bindings-1.5.0.tgz#10353c9e945334bc0511a6d90b38fbc7c9c504df"
  integrity sha1-EDU8npRTNLwFEabZCzj7x8nFBN8=
  dependencies:
    file-uri-to-path "1.0.0"

bl@^1.0.0:
  version "1.2.3"
  resolved "http://r.npm.sankuai.com/bl/download/bl-1.2.3.tgz#1e8dd80142eac80d7158c9dccc047fb620e035e7"
  integrity sha1-Ho3YAULqyA1xWMnczAR/tiDgNec=
  dependencies:
    readable-stream "^2.3.5"
    safe-buffer "^5.1.1"

bl@^4.0.3, bl@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/bl/download/bl-4.1.0.tgz#451535264182bec2fbbc83a62ab98cf11d9f7b3a"
  integrity sha1-RRU1JkGCvsL7vIOmKrmM8R2fezo=
  dependencies:
    buffer "^5.5.0"
    inherits "^2.0.4"
    readable-stream "^3.4.0"

blueimp-md5@^2.19.0:
  version "2.19.0"
  resolved "http://r.npm.sankuai.com/blueimp-md5/download/blueimp-md5-2.19.0.tgz#b53feea5498dcb53dc6ec4b823adb84b729c4af0"
  integrity sha1-tT/upUmNy1PcbsS4I624S3KcSvA=

boolbase@^1.0.0, boolbase@~1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/boolbase/download/boolbase-1.0.0.tgz#68dff5fbe60c51eb37725ea9e3ed310dcc1e776e"
  integrity sha1-aN/1++YMUes3cl6p4+0xDcwed24=

bplist-creator@0.1.0:
  version "0.1.0"
  resolved "http://r.npm.sankuai.com/bplist-creator/download/bplist-creator-0.1.0.tgz#018a2d1b587f769e379ef5519103730f8963ba1e"
  integrity sha1-AYotG1h/dp43nvVRkQNzD4ljuh4=
  dependencies:
    stream-buffers "2.2.x"

bplist-creator@~0.0.2:
  version "0.0.8"
  resolved "http://r.npm.sankuai.com/bplist-creator/download/bplist-creator-0.0.8.tgz#56b2a6e79e9aec3fc33bf831d09347d73794e79c"
  integrity sha1-VrKm556a7D/DO/gx0JNH1zeU55w=
  dependencies:
    stream-buffers "~2.2.0"

bplist-parser@0.3.1:
  version "0.3.1"
  resolved "http://r.npm.sankuai.com/bplist-parser/download/bplist-parser-0.3.1.tgz#e1c90b2ca2a9f9474cc72f6862bbf3fee8341fd1"
  integrity sha1-4ckLLKKp+UdMxy9oYrvz/ug0H9E=
  dependencies:
    big-integer "1.6.x"

bplist-parser@~0.0.4:
  version "0.0.6"
  resolved "http://r.npm.sankuai.com/bplist-parser/download/bplist-parser-0.0.6.tgz#38da3471817df9d44ab3892e27707bbbd75a11b9"
  integrity sha1-ONo0cYF9+dRKs4kuJ3B7u9daEbk=

bplist@^0.0.4:
  version "0.0.4"
  resolved "http://r.npm.sankuai.com/bplist/download/bplist-0.0.4.tgz#bc8d552ed56ff8a2992c4f9093c2b5ab8259d8dc"
  integrity sha1-vI1VLtVv+KKZLE+Qk8K1q4JZ2Nw=
  dependencies:
    bplist-creator "~0.0.2"
    bplist-parser "~0.0.4"

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "http://r.npm.sankuai.com/brace-expansion/download/brace-expansion-1.1.11.tgz#3c7fcbf529d87226f3d2f52b966ff5271eb441dd"
  integrity sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

brace-expansion@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/brace-expansion/download/brace-expansion-2.0.1.tgz#1edc459e0f0c548486ecf9fc99f2221364b9a0ae"
  integrity sha1-HtxFng8MVISG7Pn8mfIiE2S5oK4=
  dependencies:
    balanced-match "^1.0.0"

braces@^2.3.1:
  version "2.3.2"
  resolved "http://r.npm.sankuai.com/braces/download/braces-2.3.2.tgz#5979fd3f14cd531565e5fa2df1abfff1dfaee729"
  integrity sha1-WXn9PxTNUxVl5fot8av/8d+u5yk=
  dependencies:
    arr-flatten "^1.1.0"
    array-unique "^0.3.2"
    extend-shallow "^2.0.1"
    fill-range "^4.0.0"
    isobject "^3.0.1"
    repeat-element "^1.1.2"
    snapdragon "^0.8.1"
    snapdragon-node "^2.0.1"
    split-string "^3.0.2"
    to-regex "^3.0.1"

braces@^3.0.2:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/braces/download/braces-3.0.2.tgz#3454e1a462ee8d599e236df336cd9ea4f8afe107"
  integrity sha1-NFThpGLujVmeI23zNs2epPiv4Qc=
  dependencies:
    fill-range "^7.0.1"

browser-process-hrtime@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/browser-process-hrtime/download/browser-process-hrtime-1.0.0.tgz#3c9b4b7d782c8121e56f10106d84c0d0ffc94626"
  integrity sha1-PJtLfXgsgSHlbxAQbYTA0P/JRiY=

browserslist@^4.21.3, browserslist@^4.21.5:
  version "4.21.5"
  resolved "http://r.npm.sankuai.com/browserslist/download/browserslist-4.21.5.tgz#75c5dae60063ee641f977e00edd3cfb2fb7af6a7"
  integrity sha1-dcXa5gBj7mQfl34A7dPPsvt69qc=
  dependencies:
    caniuse-lite "^1.0.30001449"
    electron-to-chromium "^1.4.284"
    node-releases "^2.0.8"
    update-browserslist-db "^1.0.10"

bs-logger@0.x:
  version "0.2.6"
  resolved "http://r.npm.sankuai.com/bs-logger/download/bs-logger-0.2.6.tgz#eb7d365307a72cf974cc6cda76b68354ad336bd8"
  integrity sha1-6302UwenLPl0zGzadraDVK0za9g=
  dependencies:
    fast-json-stable-stringify "2.x"

bser@2.1.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/bser/download/bser-2.1.1.tgz#e6787da20ece9d07998533cfd9de6f5c38f4bc05"
  integrity sha1-5nh9og7OnQeZhTPP2d5vXDj0vAU=
  dependencies:
    node-int64 "^0.4.0"

buffer-alloc-unsafe@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/buffer-alloc-unsafe/download/buffer-alloc-unsafe-1.1.0.tgz#bd7dc26ae2972d0eda253be061dba992349c19f0"
  integrity sha1-vX3CauKXLQ7aJTvgYdupkjScGfA=

buffer-alloc@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/buffer-alloc/download/buffer-alloc-1.2.0.tgz#890dd90d923a873e08e10e5fd51a57e5b7cce0ec"
  integrity sha1-iQ3ZDZI6hz4I4Q5f1RpX5bfM4Ow=
  dependencies:
    buffer-alloc-unsafe "^1.1.0"
    buffer-fill "^1.0.0"

buffer-crc32@^0.2.1, buffer-crc32@^0.2.13, buffer-crc32@~0.2.3:
  version "0.2.13"
  resolved "http://r.npm.sankuai.com/buffer-crc32/download/buffer-crc32-0.2.13.tgz#0d333e3f00eac50aa1454abd30ef8c2a5d9a7242"
  integrity sha1-DTM+PwDqxQqhRUq9MO+MKl2ackI=

buffer-fill@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/buffer-fill/download/buffer-fill-1.0.0.tgz#f8f78b76789888ef39f205cd637f68e702122b2c"
  integrity sha1-+PeLdniYiO858gXNY39o5wISKyw=

buffer-from@1.x, buffer-from@^1.0.0:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/buffer-from/download/buffer-from-1.1.2.tgz#2b146a6fd72e80b4f55d255f35ed59a3a9a41bd5"
  integrity sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=

buffer@^5.2.1, buffer@^5.5.0:
  version "5.7.1"
  resolved "http://r.npm.sankuai.com/buffer/download/buffer-5.7.1.tgz#ba62e7c13133053582197160851a8f648e99eed0"
  integrity sha1-umLnwTEzBTWCGXFghRqPZI6Z7tA=
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.1.13"

bytes@3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/bytes/download/bytes-3.0.0.tgz#d32815404d689699f85a4ea4fa8755dd13a96048"
  integrity sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg=

cache-base@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/cache-base/download/cache-base-1.0.1.tgz#0a7f46416831c8b662ee36fe4e7c59d76f666ab2"
  integrity sha1-Cn9GQWgxyLZi7jb+TnxZ129marI=
  dependencies:
    collection-visit "^1.0.0"
    component-emitter "^1.2.1"
    get-value "^2.0.6"
    has-value "^1.0.0"
    isobject "^3.0.1"
    set-value "^2.0.0"
    to-object-path "^0.3.0"
    union-value "^1.0.0"
    unset-value "^1.0.0"

cacheable-request@^2.1.1:
  version "2.1.4"
  resolved "http://r.npm.sankuai.com/cacheable-request/download/cacheable-request-2.1.4.tgz#0d808801b6342ad33c91df9d0b44dc09b91e5c3d"
  integrity sha1-DYCIAbY0KtM8kd+dC0TcCbkeXD0=
  dependencies:
    clone-response "1.0.2"
    get-stream "3.0.0"
    http-cache-semantics "3.8.1"
    keyv "3.0.0"
    lowercase-keys "1.0.0"
    normalize-url "2.0.1"
    responselike "1.0.2"

call-bind@^1.0.0, call-bind@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/call-bind/download/call-bind-1.0.2.tgz#b1d4e89e688119c3c9a903ad30abb2f6a919be3c"
  integrity sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw=
  dependencies:
    function-bind "^1.1.1"
    get-intrinsic "^1.0.2"

call-bind@^1.0.5, call-bind@^1.0.6, call-bind@^1.0.7:
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/call-bind/download/call-bind-1.0.7.tgz#06016599c40c56498c18769d2730be242b6fa3b9"
  integrity sha1-BgFlmcQMVkmMGHadJzC+JCtvo7k=
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    set-function-length "^1.2.1"

caller-callsite@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/caller-callsite/download/caller-callsite-2.0.0.tgz#847e0fce0a223750a9a027c54b33731ad3154134"
  integrity sha1-hH4PzgoiN1CpoCfFSzNzGtMVQTQ=
  dependencies:
    callsites "^2.0.0"

caller-path@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/caller-path/download/caller-path-2.0.0.tgz#468f83044e369ab2010fac5f06ceee15bb2cb1f4"
  integrity sha1-Ro+DBE42mrIBD6xfBs7uFbsssfQ=
  dependencies:
    caller-callsite "^2.0.0"

callsites@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/callsites/download/callsites-2.0.0.tgz#06eb84f00eea413da86affefacbffb36093b3c50"
  integrity sha1-BuuE8A7qQT2oav/vrL/7Ngk7PFA=

callsites@^3.0.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/callsites/download/callsites-3.1.0.tgz#b3630abd8943432f54b3f0519238e33cd7df2f73"
  integrity sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=

camelcase@^2.0.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/camelcase/download/camelcase-2.1.1.tgz#7c1d16d679a1bbe59ca02cacecfb011e201f5a1f"
  integrity sha1-fB0W1nmhu+WcoCys7PsBHiAfWh8=

camelcase@^5.0.0, camelcase@^5.3.1:
  version "5.3.1"
  resolved "http://r.npm.sankuai.com/camelcase/download/camelcase-5.3.1.tgz#e3c9b31569e106811df242f715725a1f4c494320"
  integrity sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=

camelcase@^6.0.0, camelcase@^6.2.0:
  version "6.3.0"
  resolved "http://r.npm.sankuai.com/camelcase/download/camelcase-6.3.0.tgz#5685b95eb209ac9c0c177467778c9c84df58ba9a"
  integrity sha1-VoW5XrIJrJwMF3Rnd4ychN9Yupo=

camelize@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/camelize/download/camelize-1.0.1.tgz#89b7e16884056331a35d6b5ad064332c91daa6c3"
  integrity sha1-ibfhaIQFYzGjXWta0GQzLJHapsM=

caniuse-lite@^1.0.30001449:
  version "1.0.30001477"
  resolved "http://r.npm.sankuai.com/caniuse-lite/download/caniuse-lite-1.0.30001477.tgz#a2ffb2276258233034bbb869d4558b02658a511e"
  integrity sha1-ov+yJ2JYIzA0u7hp1FWLAmWKUR4=

capture-exit@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/capture-exit/download/capture-exit-2.0.0.tgz#fb953bfaebeb781f62898239dabb426d08a509a4"
  integrity sha1-+5U7+uvreB9iiYI52rtCbQilCaQ=
  dependencies:
    rsvp "^4.8.4"

chalk@^2.0.0, chalk@^2.0.1, chalk@^2.4.1, chalk@^2.4.2:
  version "2.4.2"
  resolved "http://r.npm.sankuai.com/chalk/download/chalk-2.4.2.tgz#cd42541677a54333cf541a49108c1432b44c9424"
  integrity sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/chalk/download/chalk-3.0.0.tgz#3f73c2bf526591f574cc492c51e2456349f844e4"
  integrity sha1-P3PCv1JlkfV0zEksUeJFY0n4ROQ=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chalk@^4.0.0, chalk@^4.0.2, chalk@^4.1.0, chalk@^4.1.1, chalk@^4.1.2:
  version "4.1.2"
  resolved "http://r.npm.sankuai.com/chalk/download/chalk-4.1.2.tgz#aac4e2b7734a740867aeb16bf02aad556a1e7a01"
  integrity sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

char-regex@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/char-regex/download/char-regex-1.0.2.tgz#d744358226217f981ed58f479b1d6bcc29545dcf"
  integrity sha1-10Q1giYhf5ge1Y9Hmx1rzClUXc8=

chardet@^0.4.0:
  version "0.4.2"
  resolved "http://r.npm.sankuai.com/chardet/download/chardet-0.4.2.tgz#b5473b33dc97c424e5d98dc87d55d4d8a29c8bf2"
  integrity sha1-tUc7M9yXxCTl2Y3IfVXU2KKci/I=

chardet@^0.7.0:
  version "0.7.0"
  resolved "http://r.npm.sankuai.com/chardet/download/chardet-0.7.0.tgz#90094849f0937f2eedc2425d0d28a9e5f0cbad9e"
  integrity sha1-kAlISfCTfy7twkJdDSip5fDLrZ4=

ci-info@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/ci-info/download/ci-info-2.0.0.tgz#67a9e964be31a51e15e5010d58e6f12834002f46"
  integrity sha1-Z6npZL4xpR4V5QENWObxKDQAL0Y=

circular-json@^0.5.9:
  version "0.5.9"
  resolved "http://r.npm.sankuai.com/circular-json/download/circular-json-0.5.9.tgz#932763ae88f4f7dead7a0d09c8a51a4743a53b1d"
  integrity sha1-kydjroj0996teg0JyKUaR0OlOx0=

cjs-module-lexer@^0.6.0:
  version "0.6.0"
  resolved "http://r.npm.sankuai.com/cjs-module-lexer/download/cjs-module-lexer-0.6.0.tgz#4186fcca0eae175970aee870b9fe2d6cf8d5655f"
  integrity sha1-QYb8yg6uF1lwruhwuf4tbPjVZV8=

class-utils@^0.3.5:
  version "0.3.6"
  resolved "http://r.npm.sankuai.com/class-utils/download/class-utils-0.3.6.tgz#f93369ae8b9a7ce02fd41faad0ca83033190c463"
  integrity sha1-+TNprouafOAv1B+q0MqDAzGQxGM=
  dependencies:
    arr-union "^3.1.0"
    define-property "^0.2.5"
    isobject "^3.0.0"
    static-extend "^0.1.1"

clean-stack@^2.0.0:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/clean-stack/download/clean-stack-2.2.0.tgz#ee8472dbb129e727b31e8a10a427dee9dfe4008b"
  integrity sha1-7oRy27Ep5yezHooQpCfe6d/kAIs=

cli-cursor@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/cli-cursor/download/cli-cursor-2.1.0.tgz#b35dac376479facc3e94747d41d0d0f5238ffcb5"
  integrity sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU=
  dependencies:
    restore-cursor "^2.0.0"

cli-cursor@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/cli-cursor/download/cli-cursor-3.1.0.tgz#264305a7ae490d1d03bf0c9ba7c925d1753af307"
  integrity sha1-JkMFp65JDR0Dvwybp8kl0XU68wc=
  dependencies:
    restore-cursor "^3.1.0"

cli-spinners@^2.0.0, cli-spinners@^2.5.0:
  version "2.8.0"
  resolved "http://r.npm.sankuai.com/cli-spinners/download/cli-spinners-2.8.0.tgz#e97a3e2bd00e6d85aa0c13d7f9e3ce236f7787fc"
  integrity sha1-6Xo+K9AObYWqDBPX+ePOI293h/w=

cli-truncate@3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/cli-truncate/download/cli-truncate-3.1.0.tgz#3f23ab12535e3d73e839bb43e73c9de487db1389"
  integrity sha1-PyOrElNePXPoObtD5zyd5IfbE4k=
  dependencies:
    slice-ansi "^5.0.0"
    string-width "^5.0.0"

cli-truncate@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/cli-truncate/download/cli-truncate-2.1.0.tgz#c39e28bf05edcde5be3b98992a22deed5a2b93c7"
  integrity sha1-w54ovwXtzeW+O5iZKiLe7Vork8c=
  dependencies:
    slice-ansi "^3.0.0"
    string-width "^4.2.0"

cli-width@^2.0.0:
  version "2.2.1"
  resolved "http://r.npm.sankuai.com/cli-width/download/cli-width-2.2.1.tgz#b0433d0b4e9c847ef18868a4ef16fd5fc8271c48"
  integrity sha1-sEM9C06chH7xiGik7xb9X8gnHEg=

cli-width@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/cli-width/download/cli-width-3.0.0.tgz#a2f48437a2caa9a22436e794bf071ec9e61cedf6"
  integrity sha1-ovSEN6LKqaIkNueUvwceyeYc7fY=

cliui@^3.0.3:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/cliui/download/cliui-3.2.0.tgz#120601537a916d29940f934da3b48d585a39213d"
  integrity sha1-EgYBU3qRbSmUD5NNo7SNWFo5IT0=
  dependencies:
    string-width "^1.0.1"
    strip-ansi "^3.0.1"
    wrap-ansi "^2.0.0"

cliui@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/cliui/download/cliui-5.0.0.tgz#deefcfdb2e800784aa34f46fa08e06851c7bbbc5"
  integrity sha1-3u/P2y6AB4SqNPRvoI4GhRx7u8U=
  dependencies:
    string-width "^3.1.0"
    strip-ansi "^5.2.0"
    wrap-ansi "^5.1.0"

cliui@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/cliui/download/cliui-6.0.0.tgz#511d702c0c4e41ca156d7d0e96021f23e13225b1"
  integrity sha1-UR1wLAxOQcoVbX0OlgIfI+EyJbE=
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.0"
    wrap-ansi "^6.2.0"

clone-deep@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/clone-deep/download/clone-deep-4.0.1.tgz#c19fd9bdbbf85942b4fd979c84dcf7d5f07c2387"
  integrity sha1-wZ/Zvbv4WUK0/ZechNz31fB8I4c=
  dependencies:
    is-plain-object "^2.0.4"
    kind-of "^6.0.2"
    shallow-clone "^3.0.0"

clone-response@1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/clone-response/download/clone-response-1.0.2.tgz#d1dc973920314df67fbeb94223b4ee350239e96b"
  integrity sha1-0dyXOSAxTfZ/vrlCI7TuNQI56Ws=
  dependencies:
    mimic-response "^1.0.0"

clone@^1.0.2:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/clone/download/clone-1.0.4.tgz#da309cc263df15994c688ca902179ca3c7cd7c7e"
  integrity sha1-2jCcwmPfFZlMaIypAheco8fNfH4=

co@^4.6.0:
  version "4.6.0"
  resolved "http://r.npm.sankuai.com/co/download/co-4.6.0.tgz#6ea6bdf3d853ae54ccb8e47bfa0bf3f9031fb184"
  integrity sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=

code-point-at@^1.0.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/code-point-at/download/code-point-at-1.1.0.tgz#0d070b4d043a5bea33a2f1a40e2edb3d9a4ccf77"
  integrity sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c=

collect-v8-coverage@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/collect-v8-coverage/download/collect-v8-coverage-1.0.1.tgz#cc2c8e94fc18bbdffe64d6534570c8a673b27f59"
  integrity sha1-zCyOlPwYu9/+ZNZTRXDIpnOyf1k=

collection-visit@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/collection-visit/download/collection-visit-1.0.0.tgz#4bc0373c164bc3291b4d368c829cf1a80a59dca0"
  integrity sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=
  dependencies:
    map-visit "^1.0.0"
    object-visit "^1.0.0"

color-convert@^1.9.0:
  version "1.9.3"
  resolved "http://r.npm.sankuai.com/color-convert/download/color-convert-1.9.3.tgz#bb71850690e1f136567de629d2d5471deda4c1e8"
  integrity sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/color-convert/download/color-convert-2.0.1.tgz#72d3a68d598c9bdb3af2ad1e84f21d896abd4de3"
  integrity sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=
  dependencies:
    color-name "~1.1.4"

color-name@1.1.3:
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/color-name/download/color-name-1.1.3.tgz#a7d0558bd89c42f795dd42328f740831ca53bc25"
  integrity sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=

color-name@~1.1.4:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/color-name/download/color-name-1.1.4.tgz#c2a09a87acbde69543de6f63fa3995c826c536a2"
  integrity sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=

color-support@^1.1.3:
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/color-support/download/color-support-1.1.3.tgz#93834379a1cc9a0c61f82f52f0d04322251bd5a2"
  integrity sha1-k4NDeaHMmgxh+C9S8NBDIiUb1aI=

colorette@^1.0.7:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/colorette/download/colorette-1.4.0.tgz#5190fbb87276259a86ad700bff2c6d6faa3fca40"
  integrity sha1-UZD7uHJ2JZqGrXAL/yxtb6o/ykA=

colorette@^2.0.16:
  version "2.0.19"
  resolved "http://r.npm.sankuai.com/colorette/download/colorette-2.0.19.tgz#cdf044f47ad41a0f4b56b3a0d5b4e6e1a2d5a798"
  integrity sha1-zfBE9HrUGg9LVrOg1bTm4aLVp5g=

combined-stream@^1.0.8:
  version "1.0.8"
  resolved "http://r.npm.sankuai.com/combined-stream/download/combined-stream-1.0.8.tgz#c3d45a8b34fd730631a110a8a2520682b31d5a7f"
  integrity sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=
  dependencies:
    delayed-stream "~1.0.0"

command-exists@^1.2.8:
  version "1.2.9"
  resolved "http://r.npm.sankuai.com/command-exists/download/command-exists-1.2.9.tgz#c50725af3808c8ab0260fd60b01fbfa25b954f69"
  integrity sha1-xQclrzgIyKsCYP1gsB+/oluVT2k=

commander@^2.19.0, commander@^2.8.1:
  version "2.20.3"
  resolved "http://r.npm.sankuai.com/commander/download/commander-2.20.3.tgz#fd485e84c03eb4881c20722ba48035e8531aeb33"
  integrity sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=

commander@^6.1.0:
  version "6.2.1"
  resolved "http://r.npm.sankuai.com/commander/download/commander-6.2.1.tgz#0792eb682dfbc325999bb2b84fddddba110ac73c"
  integrity sha1-B5LraC37wyWZm7K4T93duhEKxzw=

commander@^8.3.0:
  version "8.3.0"
  resolved "http://r.npm.sankuai.com/commander/download/commander-8.3.0.tgz#4837ea1b2da67b9c616a67afbb0fafee567bca66"
  integrity sha1-SDfqGy2me5xhamevuw+v7lZ7ymY=

commander@~2.13.0:
  version "2.13.0"
  resolved "http://r.npm.sankuai.com/commander/download/commander-2.13.0.tgz#6964bca67685df7c1f1430c584f07d7597885b9c"
  integrity sha1-aWS8pnaF33wfFDDFhPB9dZeIW5w=

commondir@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/commondir/download/commondir-1.0.1.tgz#ddd800da0c66127393cca5950ea968a3aaf1253b"
  integrity sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=

compare-versions@^3.6.0:
  version "3.6.0"
  resolved "http://r.npm.sankuai.com/compare-versions/download/compare-versions-3.6.0.tgz#1a5689913685e5a87637b8d3ffca75514ec41d62"
  integrity sha1-GlaJkTaF5ah2N7jT/8p1UU7EHWI=

component-emitter@^1.2.1:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/component-emitter/download/component-emitter-1.3.0.tgz#16e4070fba8ae29b679f2215853ee181ab2eabc0"
  integrity sha1-FuQHD7qK4ptnnyIVhT7hgasuq8A=

compress-commons@^4.1.0:
  version "4.1.1"
  resolved "http://r.npm.sankuai.com/compress-commons/download/compress-commons-4.1.1.tgz#df2a09a7ed17447642bad10a85cc9a19e5c42a7d"
  integrity sha1-3yoJp+0XRHZCutEKhcyaGeXEKn0=
  dependencies:
    buffer-crc32 "^0.2.13"
    crc32-stream "^4.0.2"
    normalize-path "^3.0.0"
    readable-stream "^3.6.0"

compressible@~2.0.16:
  version "2.0.18"
  resolved "http://r.npm.sankuai.com/compressible/download/compressible-2.0.18.tgz#af53cca6b070d4c3c0750fbd77286a6d7cc46fba"
  integrity sha1-r1PMprBw1MPAdQ+9dyhqbXzEb7o=
  dependencies:
    mime-db ">= 1.43.0 < 2"

compression@^1.7.1:
  version "1.7.4"
  resolved "http://r.npm.sankuai.com/compression/download/compression-1.7.4.tgz#95523eff170ca57c29a0ca41e6fe131f41e5bb8f"
  integrity sha1-lVI+/xcMpXwpoMpB5v4TH0Hlu48=
  dependencies:
    accepts "~1.3.5"
    bytes "3.0.0"
    compressible "~2.0.16"
    debug "2.6.9"
    on-headers "~1.0.2"
    safe-buffer "5.1.2"
    vary "~1.1.2"

concat-map@0.0.1:
  version "0.0.1"
  resolved "http://r.npm.sankuai.com/concat-map/download/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

concat-stream@^1.6.0:
  version "1.6.2"
  resolved "http://r.npm.sankuai.com/concat-stream/download/concat-stream-1.6.2.tgz#904bdf194cd3122fc675c77fc4ac3d4ff0fd1a34"
  integrity sha1-kEvfGUzTEi/Gdcd/xKw9T/D9GjQ=
  dependencies:
    buffer-from "^1.0.0"
    inherits "^2.0.3"
    readable-stream "^2.2.2"
    typedarray "^0.0.6"

connect@^3.6.5:
  version "3.7.0"
  resolved "http://r.npm.sankuai.com/connect/download/connect-3.7.0.tgz#5d49348910caa5e07a01800b030d0c35f20484f8"
  integrity sha1-XUk0iRDKpeB6AYALAw0MNfIEhPg=
  dependencies:
    debug "2.6.9"
    finalhandler "1.1.2"
    parseurl "~1.3.3"
    utils-merge "1.0.1"

content-disposition@^0.5.2:
  version "0.5.4"
  resolved "http://r.npm.sankuai.com/content-disposition/download/content-disposition-0.5.4.tgz#8b82b4efac82512a02bb0b1dcec9d2c5e8eb5bfe"
  integrity sha1-i4K076yCUSoCuwsdzsnSxejrW/4=
  dependencies:
    safe-buffer "5.2.1"

convert-source-map@^1.4.0, convert-source-map@^1.6.0, convert-source-map@^1.7.0:
  version "1.9.0"
  resolved "http://r.npm.sankuai.com/convert-source-map/download/convert-source-map-1.9.0.tgz#7faae62353fb4213366d0ca98358d22e8368b05f"
  integrity sha1-f6rmI1P7QhM2bQypg1jSLoNosF8=

copy-descriptor@^0.1.0:
  version "0.1.1"
  resolved "http://r.npm.sankuai.com/copy-descriptor/download/copy-descriptor-0.1.1.tgz#676f6eb3c39997c2ee1ac3a924fd6124748f578d"
  integrity sha1-Z29us8OZl8LuGsOpJP1hJHSPV40=

core-js-compat@^3.25.1:
  version "3.30.0"
  resolved "http://r.npm.sankuai.com/core-js-compat/download/core-js-compat-3.30.0.tgz#99aa2789f6ed2debfa1df3232784126ee97f4d80"
  integrity sha1-maonifbtLev6HfMjJ4QSbul/TYA=
  dependencies:
    browserslist "^4.21.5"

core-js@^2.4.1:
  version "2.6.12"
  resolved "http://r.npm.sankuai.com/core-js/download/core-js-2.6.12.tgz#d9333dfa7b065e347cc5682219d6f690859cc2ec"
  integrity sha1-2TM9+nsGXjR8xWgiGdb2kIWcwuw=

core-util-is@~1.0.0:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/core-util-is/download/core-util-is-1.0.3.tgz#a6042d3634c2b27e9328f837b965fac83808db85"
  integrity sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U=

cosmiconfig@^5.0.5, cosmiconfig@^5.1.0:
  version "5.2.1"
  resolved "http://r.npm.sankuai.com/cosmiconfig/download/cosmiconfig-5.2.1.tgz#040f726809c591e77a17c0a3626ca45b4f168b1a"
  integrity sha1-BA9yaAnFked6F8CjYmykW08Wixo=
  dependencies:
    import-fresh "^2.0.0"
    is-directory "^0.3.1"
    js-yaml "^3.13.1"
    parse-json "^4.0.0"

cosmiconfig@^7.0.0, cosmiconfig@^7.0.1:
  version "7.1.0"
  resolved "http://r.npm.sankuai.com/cosmiconfig/download/cosmiconfig-7.1.0.tgz#1443b9afa596b670082ea46cbd8f6a62b84635f6"
  integrity sha1-FEO5r6WWtnAILqRsvY9qYrhGNfY=
  dependencies:
    "@types/parse-json" "^4.0.0"
    import-fresh "^3.2.1"
    parse-json "^5.0.0"
    path-type "^4.0.0"
    yaml "^1.10.0"

crc-32@^1.2.0:
  version "1.2.2"
  resolved "http://r.npm.sankuai.com/crc-32/download/crc-32-1.2.2.tgz#3cad35a934b8bf71f25ca524b6da51fb7eace2ff"
  integrity sha1-PK01qTS4v3HyXKUkttpR+36s4v8=

crc32-stream@^4.0.2:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/crc32-stream/download/crc32-stream-4.0.2.tgz#c922ad22b38395abe9d3870f02fa8134ed709007"
  integrity sha1-ySKtIrODlavp04cPAvqBNO1wkAc=
  dependencies:
    crc-32 "^1.2.0"
    readable-stream "^3.4.0"

create-react-class@^15.7.0:
  version "15.7.0"
  resolved "http://r.npm.sankuai.com/create-react-class/download/create-react-class-15.7.0.tgz#7499d7ca2e69bb51d13faf59bd04f0c65a1d6c1e"
  integrity sha1-dJnXyi5pu1HRP69ZvQTwxlodbB4=
  dependencies:
    loose-envify "^1.3.1"
    object-assign "^4.1.1"

cross-spawn@^5.1.0:
  version "5.1.0"
  resolved "http://r.npm.sankuai.com/cross-spawn/download/cross-spawn-5.1.0.tgz#e8bd0efee58fcff6f8f94510a0a554bbfa235449"
  integrity sha1-6L0O/uWPz/b4+UUQoKVUu/ojVEk=
  dependencies:
    lru-cache "^4.0.1"
    shebang-command "^1.2.0"
    which "^1.2.9"

cross-spawn@^6.0.0:
  version "6.0.5"
  resolved "http://r.npm.sankuai.com/cross-spawn/download/cross-spawn-6.0.5.tgz#4a5ec7c64dfae22c3a14124dbacdee846d80cbc4"
  integrity sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q=
  dependencies:
    nice-try "^1.0.4"
    path-key "^2.0.1"
    semver "^5.5.0"
    shebang-command "^1.2.0"
    which "^1.2.9"

cross-spawn@^7.0.0, cross-spawn@^7.0.2, cross-spawn@^7.0.3:
  version "7.0.3"
  resolved "http://r.npm.sankuai.com/cross-spawn/download/cross-spawn-7.0.3.tgz#f73a85b9d5d41d045551c177e2882d4ac85728a6"
  integrity sha1-9zqFudXUHQRVUcF34ogtSshXKKY=
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

css-color-keywords@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/css-color-keywords/download/css-color-keywords-1.0.0.tgz#fea2616dc676b2962686b3af8dbdbe180b244e05"
  integrity sha1-/qJhbcZ2spYmhrOvjb2+GAskTgU=

css-select@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/css-select/download/css-select-2.1.0.tgz#6a34653356635934a81baca68d0255432105dbef"
  integrity sha1-ajRlM1ZjWTSoG6ymjQJVQyEF2+8=
  dependencies:
    boolbase "^1.0.0"
    css-what "^3.2.1"
    domutils "^1.7.0"
    nth-check "^1.0.2"

css-to-react-native@^3.0.0:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/css-to-react-native/download/css-to-react-native-3.2.0.tgz#cdd8099f71024e149e4f6fe17a7d46ecd55f1e32"
  integrity sha1-zdgJn3ECThSeT2/hen1G7NVfHjI=
  dependencies:
    camelize "^1.0.0"
    css-color-keywords "^1.0.0"
    postcss-value-parser "^4.0.2"

css-tree@^1.0.0-alpha.39:
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/css-tree/download/css-tree-1.1.3.tgz#eb4870fb6fd7707327ec95c2ff2ab09b5e8db91d"
  integrity sha1-60hw+2/XcHMn7JXC/yqwm16NuR0=
  dependencies:
    mdn-data "2.0.14"
    source-map "^0.6.1"

css-what@^3.2.1:
  version "3.4.2"
  resolved "http://r.npm.sankuai.com/css-what/download/css-what-3.4.2.tgz#ea7026fcb01777edbde52124e21f327e7ae950e4"
  integrity sha1-6nAm/LAXd+295SEk4h8yfnrpUOQ=

cssom@^0.4.4:
  version "0.4.4"
  resolved "http://r.npm.sankuai.com/cssom/download/cssom-0.4.4.tgz#5a66cf93d2d0b661d80bf6a44fb65f5c2e4e0a10"
  integrity sha1-WmbPk9LQtmHYC/akT7ZfXC5OChA=

cssom@~0.3.6:
  version "0.3.8"
  resolved "http://r.npm.sankuai.com/cssom/download/cssom-0.3.8.tgz#9f1276f5b2b463f2114d3f2c75250af8c1a36f4a"
  integrity sha1-nxJ29bK0Y/IRTT8sdSUK+MGjb0o=

cssstyle@^2.3.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/cssstyle/download/cssstyle-2.3.0.tgz#ff665a0ddbdc31864b09647f34163443d90b0852"
  integrity sha1-/2ZaDdvcMYZLCWR/NBY0Q9kLCFI=
  dependencies:
    cssom "~0.3.6"

csstype@^3.0.2:
  version "3.1.2"
  resolved "http://r.npm.sankuai.com/csstype/download/csstype-3.1.2.tgz#1d4bf9d572f11c14031f0436e1c10bc1f571f50b"
  integrity sha1-HUv51XLxHBQDHwQ24cELwfVx9Qs=

data-urls@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/data-urls/download/data-urls-2.0.0.tgz#156485a72963a970f5d5821aaf642bef2bf2db9b"
  integrity sha1-FWSFpyljqXD11YIar2Qr7yvy25s=
  dependencies:
    abab "^2.0.3"
    whatwg-mimetype "^2.3.0"
    whatwg-url "^8.0.0"

data-view-buffer@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/data-view-buffer/download/data-view-buffer-1.0.1.tgz#8ea6326efec17a2e42620696e671d7d5a8bc66b2"
  integrity sha1-jqYybv7Bei5CYgaW5nHX1ai8ZrI=
  dependencies:
    call-bind "^1.0.6"
    es-errors "^1.3.0"
    is-data-view "^1.0.1"

data-view-byte-length@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/data-view-byte-length/download/data-view-byte-length-1.0.1.tgz#90721ca95ff280677eb793749fce1011347669e2"
  integrity sha1-kHIcqV/ygGd+t5N0n84QETR2aeI=
  dependencies:
    call-bind "^1.0.7"
    es-errors "^1.3.0"
    is-data-view "^1.0.1"

data-view-byte-offset@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/data-view-byte-offset/download/data-view-byte-offset-1.0.0.tgz#5e0bbfb4828ed2d1b9b400cd8a7d119bca0ff18a"
  integrity sha1-Xgu/tIKO0tG5tADNin0Rm8oP8Yo=
  dependencies:
    call-bind "^1.0.6"
    es-errors "^1.3.0"
    is-data-view "^1.0.1"

date-format@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/date-format/download/date-format-2.1.0.tgz#31d5b5ea211cf5fd764cd38baf9d033df7e125cf"
  integrity sha1-MdW16iEc9f12TNOLr50DPffhJc8=

dayjs@^1.11.10, dayjs@^1.9.1:
  version "1.11.10"
  resolved "http://r.npm.sankuai.com/dayjs/download/dayjs-1.11.10.tgz#68acea85317a6e164457d6d6947564029a6a16a0"
  integrity sha1-aKzqhTF6bhZEV9bWlHVkAppqFqA=

dayjs@^1.8.15:
  version "1.11.7"
  resolved "http://r.npm.sankuai.com/dayjs/download/dayjs-1.11.7.tgz#4b296922642f70999544d1144a2c25730fce63e2"
  integrity sha1-SylpImQvcJmVRNEUSiwlcw/OY+I=

debug@2.6.9, debug@^2.2.0, debug@^2.3.3:
  version "2.6.9"
  resolved "http://r.npm.sankuai.com/debug/download/debug-2.6.9.tgz#5d128515df134ff327e90a4c93f4e077a536341f"
  integrity sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=
  dependencies:
    ms "2.0.0"

debug@4, debug@^4.0.1, debug@^4.1.0, debug@^4.1.1, debug@^4.3.2, debug@^4.3.4:
  version "4.3.4"
  resolved "http://r.npm.sankuai.com/debug/download/debug-4.3.4.tgz#1319f6579357f2338d3337d2cdd4914bb5dcc865"
  integrity sha1-Exn2V5NX8jONMzfSzdSRS7XcyGU=
  dependencies:
    ms "2.1.2"

debug@^3.2.7:
  version "3.2.7"
  resolved "http://r.npm.sankuai.com/debug/download/debug-3.2.7.tgz#72580b7e9145fb39b6676f9c5e5fb100b934179a"
  integrity sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=
  dependencies:
    ms "^2.1.1"

decamelize@^1.1.1, decamelize@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/decamelize/download/decamelize-1.2.0.tgz#f6534d15148269b20352e7bee26f501f9a191290"
  integrity sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=

decimal.js@^10.2.1:
  version "10.4.3"
  resolved "http://r.npm.sankuai.com/decimal.js/download/decimal.js-10.4.3.tgz#1044092884d245d1b7f65725fa4ad4c6f781cc23"
  integrity sha1-EEQJKITSRdG39lcl+krUxveBzCM=

decode-uri-component@^0.2.0, decode-uri-component@^0.2.2:
  version "0.2.2"
  resolved "http://r.npm.sankuai.com/decode-uri-component/download/decode-uri-component-0.2.2.tgz#e69dbe25d37941171dd540e024c444cd5188e1e9"
  integrity sha1-5p2+JdN5QRcd1UDgJMREzVGI4ek=

decompress-response@^3.3.0:
  version "3.3.0"
  resolved "http://r.npm.sankuai.com/decompress-response/download/decompress-response-3.3.0.tgz#80a4dd323748384bfa248083622aedec982adff3"
  integrity sha1-gKTdMjdIOEv6JICDYirt7Jgq3/M=
  dependencies:
    mimic-response "^1.0.0"

decompress-tar@^4.0.0, decompress-tar@^4.1.0, decompress-tar@^4.1.1:
  version "4.1.1"
  resolved "http://r.npm.sankuai.com/decompress-tar/download/decompress-tar-4.1.1.tgz#718cbd3fcb16209716e70a26b84e7ba4592e5af1"
  integrity sha1-cYy9P8sWIJcW5womuE57pFkuWvE=
  dependencies:
    file-type "^5.2.0"
    is-stream "^1.1.0"
    tar-stream "^1.5.2"

decompress-tarbz2@^4.0.0:
  version "4.1.1"
  resolved "http://r.npm.sankuai.com/decompress-tarbz2/download/decompress-tarbz2-4.1.1.tgz#3082a5b880ea4043816349f378b56c516be1a39b"
  integrity sha1-MIKluIDqQEOBY0nzeLVsUWvho5s=
  dependencies:
    decompress-tar "^4.1.0"
    file-type "^6.1.0"
    is-stream "^1.1.0"
    seek-bzip "^1.0.5"
    unbzip2-stream "^1.0.9"

decompress-targz@^4.0.0:
  version "4.1.1"
  resolved "http://r.npm.sankuai.com/decompress-targz/download/decompress-targz-4.1.1.tgz#c09bc35c4d11f3de09f2d2da53e9de23e7ce1eee"
  integrity sha1-wJvDXE0R894J8tLaU+neI+fOHu4=
  dependencies:
    decompress-tar "^4.1.1"
    file-type "^5.2.0"
    is-stream "^1.1.0"

decompress-unzip@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/decompress-unzip/download/decompress-unzip-4.0.1.tgz#deaaccdfd14aeaf85578f733ae8210f9b4848f69"
  integrity sha1-3qrM39FK6vhVePczroIQ+bSEj2k=
  dependencies:
    file-type "^3.8.0"
    get-stream "^2.2.0"
    pify "^2.3.0"
    yauzl "^2.4.2"

decompress@^4.2.1:
  version "4.2.1"
  resolved "http://r.npm.sankuai.com/decompress/download/decompress-4.2.1.tgz#007f55cc6a62c055afa37c07eb6a4ee1b773f118"
  integrity sha1-AH9VzGpiwFWvo3wH62pO4bdz8Rg=
  dependencies:
    decompress-tar "^4.0.0"
    decompress-tarbz2 "^4.0.0"
    decompress-targz "^4.0.0"
    decompress-unzip "^4.0.1"
    graceful-fs "^4.1.10"
    make-dir "^1.0.0"
    pify "^2.3.0"
    strip-dirs "^2.0.0"

deep-is@^0.1.3, deep-is@~0.1.3:
  version "0.1.4"
  resolved "http://r.npm.sankuai.com/deep-is/download/deep-is-0.1.4.tgz#a6f2dce612fadd2ef1f519b73551f17e85199831"
  integrity sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=

deepmerge@^3.2.0:
  version "3.3.0"
  resolved "http://r.npm.sankuai.com/deepmerge/download/deepmerge-3.3.0.tgz#d3c47fd6f3a93d517b14426b0628a17b0125f5f7"
  integrity sha1-08R/1vOpPVF7FEJrBiihewEl9fc=

deepmerge@^4.0.0, deepmerge@^4.2.2:
  version "4.3.1"
  resolved "http://r.npm.sankuai.com/deepmerge/download/deepmerge-4.3.1.tgz#44b5f2147cd3b00d4b56137685966f26fd25dd4a"
  integrity sha1-RLXyFHzTsA1LVhN2hZZvJv0l3Uo=

defaults@^1.0.3:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/defaults/download/defaults-1.0.4.tgz#b0b02062c1e2aa62ff5d9528f0f98baa90978d7a"
  integrity sha1-sLAgYsHiqmL/XZUo8PmLqpCXjXo=
  dependencies:
    clone "^1.0.2"

define-data-property@^1.0.1, define-data-property@^1.1.4:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/define-data-property/download/define-data-property-1.1.4.tgz#894dc141bb7d3060ae4366f6a0107e68fbe48c5e"
  integrity sha1-iU3BQbt9MGCuQ2b2oBB+aPvkjF4=
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    gopd "^1.0.1"

define-properties@^1.1.3, define-properties@^1.1.4:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/define-properties/download/define-properties-1.2.0.tgz#52988570670c9eacedd8064f4a990f2405849bd5"
  integrity sha1-UpiFcGcMnqzt2AZPSpkPJAWEm9U=
  dependencies:
    has-property-descriptors "^1.0.0"
    object-keys "^1.1.1"

define-properties@^1.2.0, define-properties@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/define-properties/download/define-properties-1.2.1.tgz#10781cc616eb951a80a034bafcaa7377f6af2b6c"
  integrity sha1-EHgcxhbrlRqAoDS6/Kpzd/avK2w=
  dependencies:
    define-data-property "^1.0.1"
    has-property-descriptors "^1.0.0"
    object-keys "^1.1.1"

define-property@^0.2.5:
  version "0.2.5"
  resolved "http://r.npm.sankuai.com/define-property/download/define-property-0.2.5.tgz#c35b1ef918ec3c990f9a5bc57be04aacec5c8116"
  integrity sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=
  dependencies:
    is-descriptor "^0.1.0"

define-property@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/define-property/download/define-property-1.0.0.tgz#769ebaaf3f4a63aad3af9e8d304c9bbe79bfb0e6"
  integrity sha1-dp66rz9KY6rTr56NMEybvnm/sOY=
  dependencies:
    is-descriptor "^1.0.0"

define-property@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/define-property/download/define-property-2.0.2.tgz#d459689e8d654ba77e02a817f8710d702cb16e9d"
  integrity sha1-1Flono1lS6d+AqgX+HENcCyxbp0=
  dependencies:
    is-descriptor "^1.0.2"
    isobject "^3.0.1"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/delayed-stream/download/delayed-stream-1.0.0.tgz#df3ae199acadfb7d440aaae0b29e2272b24ec619"
  integrity sha1-3zrhmayt+31ECqrgsp4icrJOxhk=

denodeify@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/denodeify/download/denodeify-1.2.1.tgz#3a36287f5034e699e7577901052c2e6c94251631"
  integrity sha1-OjYof1A05pnnV3kBBSwubJQlFjE=

depd@2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/depd/download/depd-2.0.0.tgz#b696163cc757560d09cf22cc8fad1571b79e76df"
  integrity sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8=

dequal@^2.0.3:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/dequal/download/dequal-2.0.3.tgz#2644214f1997d39ed0ee0ece72335490a7ac67be"
  integrity sha1-JkQhTxmX057Q7g7OcjNUkKesZ74=

destroy@1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/destroy/download/destroy-1.2.0.tgz#4803735509ad8be552934c67df614f94e66fa015"
  integrity sha1-SANzVQmti+VSk0xn32FPlOZvoBU=

detect-newline@^3.0.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/detect-newline/download/detect-newline-3.1.0.tgz#576f5dfc63ae1a192ff192d8ad3af6308991b651"
  integrity sha1-V29d/GOuGhkv8ZLYrTr2MImRtlE=

diff-sequences@^26.6.2:
  version "26.6.2"
  resolved "http://r.npm.sankuai.com/diff-sequences/download/diff-sequences-26.6.2.tgz#48ba99157de1923412eed41db6b6d4aa9ca7c0b1"
  integrity sha1-SLqZFX3hkjQS7tQdtrbUqpynwLE=

diff-sequences@^29.6.3:
  version "29.6.3"
  resolved "http://r.npm.sankuai.com/diff-sequences/download/diff-sequences-29.6.3.tgz#4deaf894d11407c51efc8418012f9e70b84ea921"
  integrity sha1-Ter4lNEUB8Ue/IQYAS+ecLhOqSE=

dir-glob@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/dir-glob/download/dir-glob-3.0.1.tgz#56dbf73d992a4a93ba1584f4534063fd2e41717f"
  integrity sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8=
  dependencies:
    path-type "^4.0.0"

doctrine@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/doctrine/download/doctrine-2.1.0.tgz#5cd01fc101621b42c4cd7f5d1a66243716d3f39d"
  integrity sha1-XNAfwQFiG0LEzX9dGmYkNxbT850=
  dependencies:
    esutils "^2.0.2"

doctrine@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/doctrine/download/doctrine-3.0.0.tgz#addebead72a6574db783639dc87a121773973961"
  integrity sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=
  dependencies:
    esutils "^2.0.2"

dom-serializer@0:
  version "0.2.2"
  resolved "http://r.npm.sankuai.com/dom-serializer/download/dom-serializer-0.2.2.tgz#1afb81f533717175d478655debc5e332d9f9bb51"
  integrity sha1-GvuB9TNxcXXUeGVd68XjMtn5u1E=
  dependencies:
    domelementtype "^2.0.1"
    entities "^2.0.0"

domelementtype@1:
  version "1.3.1"
  resolved "http://r.npm.sankuai.com/domelementtype/download/domelementtype-1.3.1.tgz#d048c44b37b0d10a7f2a3d5fee3f4333d790481f"
  integrity sha1-0EjESzew0Qp/Kj1f7j9DM9eQSB8=

domelementtype@^2.0.1:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/domelementtype/download/domelementtype-2.3.0.tgz#5c45e8e869952626331d7aab326d01daf65d589d"
  integrity sha1-XEXo6GmVJiYzHXqrMm0B2vZdWJ0=

domexception@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/domexception/download/domexception-2.0.1.tgz#fb44aefba793e1574b0af6aed2801d057529f304"
  integrity sha1-+0Su+6eT4VdLCvau0oAdBXUp8wQ=
  dependencies:
    webidl-conversions "^5.0.0"

domutils@^1.7.0:
  version "1.7.0"
  resolved "http://r.npm.sankuai.com/domutils/download/domutils-1.7.0.tgz#56ea341e834e06e6748af7a1cb25da67ea9f8c2a"
  integrity sha1-Vuo0HoNOBuZ0ivehyyXaZ+qfjCo=
  dependencies:
    dom-serializer "0"
    domelementtype "1"

download@^8.0.0:
  version "8.0.0"
  resolved "http://r.npm.sankuai.com/download/download/download-8.0.0.tgz#afc0b309730811731aae9f5371c9f46be73e51b1"
  integrity sha1-r8CzCXMIEXMarp9Tccn0a+c+UbE=
  dependencies:
    archive-type "^4.0.0"
    content-disposition "^0.5.2"
    decompress "^4.2.1"
    ext-name "^5.0.0"
    file-type "^11.1.0"
    filenamify "^3.0.0"
    get-stream "^4.1.0"
    got "^8.3.1"
    make-dir "^2.1.0"
    p-event "^2.1.0"
    pify "^4.0.1"

duplexer3@^0.1.4:
  version "0.1.5"
  resolved "http://r.npm.sankuai.com/duplexer3/download/duplexer3-0.1.5.tgz#0b5e4d7bad5de8901ea4440624c8e1d20099217e"
  integrity sha1-C15Ne61d6JAepEQGJMjh0gCZIX4=

eastasianwidth@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/eastasianwidth/download/eastasianwidth-0.2.0.tgz#696ce2ec0aa0e6ea93a397ffcf24aa7840c827cb"
  integrity sha1-aWzi7Aqg5uqTo5f/zySqeEDIJ8s=

ee-first@1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/ee-first/download/ee-first-1.1.1.tgz#590c61156b0ae2f4f0255732a158b266bc56b21d"
  integrity sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=

ejs@3.1.6:
  version "3.1.6"
  resolved "http://r.npm.sankuai.com/ejs/download/ejs-3.1.6.tgz#5bfd0a0689743bb5268b3550cceeebbc1702822a"
  integrity sha1-W/0KBol0O7UmizVQzO7rvBcCgio=
  dependencies:
    jake "^10.6.1"

ejs@^3.1.6:
  version "3.1.9"
  resolved "http://r.npm.sankuai.com/ejs/download/ejs-3.1.9.tgz#03c9e8777fe12686a9effcef22303ca3d8eeb361"
  integrity sha1-A8nod3/hJoap7/zvIjA8o9jus2E=
  dependencies:
    jake "^10.8.5"

electron-to-chromium@^1.4.284:
  version "1.4.355"
  resolved "http://r.npm.sankuai.com/electron-to-chromium/download/electron-to-chromium-1.4.355.tgz#539484310e5a94133bc3df8ba4b6a9dde7a506a4"
  integrity sha1-U5SEMQ5alBM7w9+LpLap3eelBqQ=

emittery@^0.7.1:
  version "0.7.2"
  resolved "http://r.npm.sankuai.com/emittery/download/emittery-0.7.2.tgz#25595908e13af0f5674ab419396e2fb394cdfa82"
  integrity sha1-JVlZCOE68PVnSrQZOW4vs5TN+oI=

emoji-regex@^7.0.1:
  version "7.0.3"
  resolved "http://r.npm.sankuai.com/emoji-regex/download/emoji-regex-7.0.3.tgz#933a04052860c85e83c122479c4748a8e4c72156"
  integrity sha1-kzoEBShgyF6DwSJHnEdIqOTHIVY=

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "http://r.npm.sankuai.com/emoji-regex/download/emoji-regex-8.0.0.tgz#e818fd69ce5ccfcb404594f842963bf53164cc37"
  integrity sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=

emoji-regex@^9.2.2:
  version "9.2.2"
  resolved "http://r.npm.sankuai.com/emoji-regex/download/emoji-regex-9.2.2.tgz#840c8803b0d8047f4ff0cf963176b32d4ef3ed72"
  integrity sha1-hAyIA7DYBH9P8M+WMXazLU7z7XI=

encodeurl@~1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/encodeurl/download/encodeurl-1.0.2.tgz#ad3ff4c86ec2d029322f5a02c3a9a606c95b3f59"
  integrity sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=

encoding@^0.1.11:
  version "0.1.13"
  resolved "http://r.npm.sankuai.com/encoding/download/encoding-0.1.13.tgz#56574afdd791f54a8e9b2785c0582a2d26210fa9"
  integrity sha1-VldK/deR9UqOmyeFwFgqLSYhD6k=
  dependencies:
    iconv-lite "^0.6.2"

end-of-stream@^1.0.0, end-of-stream@^1.1.0, end-of-stream@^1.4.1:
  version "1.4.4"
  resolved "http://r.npm.sankuai.com/end-of-stream/download/end-of-stream-1.4.4.tgz#5ae64a5f45057baf3626ec14da0ca5e4b2431eb0"
  integrity sha1-WuZKX0UFe682JuwU2gyl5LJDHrA=
  dependencies:
    once "^1.4.0"

enquirer@^2.3.5:
  version "2.3.6"
  resolved "http://r.npm.sankuai.com/enquirer/download/enquirer-2.3.6.tgz#2a7fe5dd634a1e4125a975ec994ff5456dc3734d"
  integrity sha1-Kn/l3WNKHkElqXXsmU/1RW3Dc00=
  dependencies:
    ansi-colors "^4.1.1"

entities@^2.0.0:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/entities/download/entities-2.2.0.tgz#098dc90ebb83d8dffa089d55256b351d34c4da55"
  integrity sha1-CY3JDruD2N/6CJ1VJWs1HTTE2lU=

entities@~2.0.0:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/entities/download/entities-2.0.3.tgz#5c487e5742ab93c15abb5da22759b8590ec03b7f"
  integrity sha1-XEh+V0Krk8Fau12iJ1m4WQ7AO38=

envinfo@^7.7.2:
  version "7.8.1"
  resolved "http://r.npm.sankuai.com/envinfo/download/envinfo-7.8.1.tgz#06377e3e5f4d379fea7ac592d5ad8927e0c4d475"
  integrity sha1-Bjd+Pl9NN5/qesWS1a2JJ+DE1HU=

error-ex@^1.3.1:
  version "1.3.2"
  resolved "http://r.npm.sankuai.com/error-ex/download/error-ex-1.3.2.tgz#b4ac40648107fdcdcfae242f428bea8a14d4f1bf"
  integrity sha1-tKxAZIEH/c3PriQvQovqihTU8b8=
  dependencies:
    is-arrayish "^0.2.1"

error-stack-parser@^2.0.6:
  version "2.1.4"
  resolved "http://r.npm.sankuai.com/error-stack-parser/download/error-stack-parser-2.1.4.tgz#229cb01cdbfa84440bfa91876285b94680188286"
  integrity sha1-IpywHNv6hEQL+pGHYoW5RoAYgoY=
  dependencies:
    stackframe "^1.3.4"

errorhandler@^1.5.0:
  version "1.5.1"
  resolved "http://r.npm.sankuai.com/errorhandler/download/errorhandler-1.5.1.tgz#b9ba5d17cf90744cd1e851357a6e75bf806a9a91"
  integrity sha1-ubpdF8+QdEzR6FE1em51v4BqmpE=
  dependencies:
    accepts "~1.3.7"
    escape-html "~1.0.3"

es-abstract@^1.19.0, es-abstract@^1.20.4:
  version "1.21.2"
  resolved "http://r.npm.sankuai.com/es-abstract/download/es-abstract-1.21.2.tgz#a56b9695322c8a185dc25975aa3b8ec31d0e7eff"
  integrity sha1-pWuWlTIsihhdwll1qjuOwx0Ofv8=
  dependencies:
    array-buffer-byte-length "^1.0.0"
    available-typed-arrays "^1.0.5"
    call-bind "^1.0.2"
    es-set-tostringtag "^2.0.1"
    es-to-primitive "^1.2.1"
    function.prototype.name "^1.1.5"
    get-intrinsic "^1.2.0"
    get-symbol-description "^1.0.0"
    globalthis "^1.0.3"
    gopd "^1.0.1"
    has "^1.0.3"
    has-property-descriptors "^1.0.0"
    has-proto "^1.0.1"
    has-symbols "^1.0.3"
    internal-slot "^1.0.5"
    is-array-buffer "^3.0.2"
    is-callable "^1.2.7"
    is-negative-zero "^2.0.2"
    is-regex "^1.1.4"
    is-shared-array-buffer "^1.0.2"
    is-string "^1.0.7"
    is-typed-array "^1.1.10"
    is-weakref "^1.0.2"
    object-inspect "^1.12.3"
    object-keys "^1.1.1"
    object.assign "^4.1.4"
    regexp.prototype.flags "^1.4.3"
    safe-regex-test "^1.0.0"
    string.prototype.trim "^1.2.7"
    string.prototype.trimend "^1.0.6"
    string.prototype.trimstart "^1.0.6"
    typed-array-length "^1.0.4"
    unbox-primitive "^1.0.2"
    which-typed-array "^1.1.9"

es-abstract@^1.22.1, es-abstract@^1.22.3, es-abstract@^1.23.0, es-abstract@^1.23.2:
  version "1.23.3"
  resolved "http://r.npm.sankuai.com/es-abstract/download/es-abstract-1.23.3.tgz#8f0c5a35cd215312573c5a27c87dfd6c881a0aa0"
  integrity sha1-jwxaNc0hUxJXPFonyH39bIgaCqA=
  dependencies:
    array-buffer-byte-length "^1.0.1"
    arraybuffer.prototype.slice "^1.0.3"
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.7"
    data-view-buffer "^1.0.1"
    data-view-byte-length "^1.0.1"
    data-view-byte-offset "^1.0.0"
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    es-set-tostringtag "^2.0.3"
    es-to-primitive "^1.2.1"
    function.prototype.name "^1.1.6"
    get-intrinsic "^1.2.4"
    get-symbol-description "^1.0.2"
    globalthis "^1.0.3"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"
    has-proto "^1.0.3"
    has-symbols "^1.0.3"
    hasown "^2.0.2"
    internal-slot "^1.0.7"
    is-array-buffer "^3.0.4"
    is-callable "^1.2.7"
    is-data-view "^1.0.1"
    is-negative-zero "^2.0.3"
    is-regex "^1.1.4"
    is-shared-array-buffer "^1.0.3"
    is-string "^1.0.7"
    is-typed-array "^1.1.13"
    is-weakref "^1.0.2"
    object-inspect "^1.13.1"
    object-keys "^1.1.1"
    object.assign "^4.1.5"
    regexp.prototype.flags "^1.5.2"
    safe-array-concat "^1.1.2"
    safe-regex-test "^1.0.3"
    string.prototype.trim "^1.2.9"
    string.prototype.trimend "^1.0.8"
    string.prototype.trimstart "^1.0.8"
    typed-array-buffer "^1.0.2"
    typed-array-byte-length "^1.0.1"
    typed-array-byte-offset "^1.0.2"
    typed-array-length "^1.0.6"
    unbox-primitive "^1.0.2"
    which-typed-array "^1.1.15"

es-define-property@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/es-define-property/download/es-define-property-1.0.0.tgz#c7faefbdff8b2696cf5f46921edfb77cc4ba3845"
  integrity sha1-x/rvvf+LJpbPX0aSHt+3fMS6OEU=
  dependencies:
    get-intrinsic "^1.2.4"

es-errors@^1.2.1, es-errors@^1.3.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/es-errors/download/es-errors-1.3.0.tgz#05f75a25dab98e4fb1dcd5e1472c0546d5057c8f"
  integrity sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8=

es-object-atoms@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/es-object-atoms/download/es-object-atoms-1.0.0.tgz#ddb55cd47ac2e240701260bc2a8e31ecb643d941"
  integrity sha1-3bVc1HrC4kBwEmC8Ko4x7LZD2UE=
  dependencies:
    es-errors "^1.3.0"

es-set-tostringtag@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/es-set-tostringtag/download/es-set-tostringtag-2.0.1.tgz#338d502f6f674301d710b80c8592de8a15f09cd8"
  integrity sha1-M41QL29nQwHXELgMhZLeihXwnNg=
  dependencies:
    get-intrinsic "^1.1.3"
    has "^1.0.3"
    has-tostringtag "^1.0.0"

es-set-tostringtag@^2.0.3:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/es-set-tostringtag/download/es-set-tostringtag-2.0.3.tgz#8bb60f0a440c2e4281962428438d58545af39777"
  integrity sha1-i7YPCkQMLkKBliQoQ41YVFrzl3c=
  dependencies:
    get-intrinsic "^1.2.4"
    has-tostringtag "^1.0.2"
    hasown "^2.0.1"

es-shim-unscopables@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/es-shim-unscopables/download/es-shim-unscopables-1.0.0.tgz#702e632193201e3edf8713635d083d378e510241"
  integrity sha1-cC5jIZMgHj7fhxNjXQg9N45RAkE=
  dependencies:
    has "^1.0.3"

es-shim-unscopables@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/es-shim-unscopables/download/es-shim-unscopables-1.0.2.tgz#1f6942e71ecc7835ed1c8a83006d8771a63a3763"
  integrity sha1-H2lC5x7MeDXtHIqDAG2HcaY6N2M=
  dependencies:
    hasown "^2.0.0"

es-to-primitive@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/es-to-primitive/download/es-to-primitive-1.2.1.tgz#e55cd4c9cdc188bcefb03b366c736323fc5c898a"
  integrity sha1-5VzUyc3BiLzvsDs2bHNjI/xciYo=
  dependencies:
    is-callable "^1.1.4"
    is-date-object "^1.0.1"
    is-symbol "^1.0.2"

escalade@^3.1.1:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/escalade/download/escalade-3.1.1.tgz#d8cfdc7000965c5a0174b4a82eaa5c0552742e40"
  integrity sha1-2M/ccACWXFoBdLSoLqpcBVJ0LkA=

escape-html@~1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/escape-html/download/escape-html-1.0.3.tgz#0258eae4d3d0c0974de1c169188ef0051d1d1988"
  integrity sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=

escape-string-regexp@2.0.0, escape-string-regexp@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/escape-string-regexp/download/escape-string-regexp-2.0.0.tgz#a30304e99daa32e23b2fd20f51babd07cffca344"
  integrity sha1-owME6Z2qMuI7L9IPUbq9B8/8o0Q=

escape-string-regexp@^1.0.2, escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz#1b61c0562190a8dff6ae3bb2cf0200ca130b86d4"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

escape-string-regexp@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/escape-string-regexp/download/escape-string-regexp-4.0.0.tgz#14ba83a5d373e3d311e5afca29cf5bfad965bf34"
  integrity sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=

escodegen@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/escodegen/download/escodegen-2.0.0.tgz#5e32b12833e8aa8fa35e1bf0befa89380484c7dd"
  integrity sha1-XjKxKDPoqo+jXhvwvvqJOASEx90=
  dependencies:
    esprima "^4.0.1"
    estraverse "^5.2.0"
    esutils "^2.0.2"
    optionator "^0.8.1"
  optionalDependencies:
    source-map "~0.6.1"

eslint-config-prettier@^6.10.1:
  version "6.15.0"
  resolved "http://r.npm.sankuai.com/eslint-config-prettier/download/eslint-config-prettier-6.15.0.tgz#7f93f6cb7d45a92f1537a70ecc06366e1ac6fed9"
  integrity sha1-f5P2y31FqS8VN6cOzAY2bhrG/tk=
  dependencies:
    get-stdin "^6.0.0"

eslint-config-prettier@^8.5.0:
  version "8.8.0"
  resolved "http://r.npm.sankuai.com/eslint-config-prettier/download/eslint-config-prettier-8.8.0.tgz#bfda738d412adc917fd7b038857110efe98c9348"
  integrity sha1-v9pzjUEq3JF/17A4hXEQ7+mMk0g=

eslint-import-resolver-node@^0.3.9:
  version "0.3.9"
  resolved "http://r.npm.sankuai.com/eslint-import-resolver-node/download/eslint-import-resolver-node-0.3.9.tgz#d4eaac52b8a2e7c3cd1903eb00f7e053356118ac"
  integrity sha1-1OqsUrii58PNGQPrAPfgUzVhGKw=
  dependencies:
    debug "^3.2.7"
    is-core-module "^2.13.0"
    resolve "^1.22.4"

eslint-module-utils@^2.8.0:
  version "2.8.1"
  resolved "http://r.npm.sankuai.com/eslint-module-utils/download/eslint-module-utils-2.8.1.tgz#52f2404300c3bd33deece9d7372fb337cc1d7c34"
  integrity sha1-UvJAQwDDvTPe7OnXNy+zN8wdfDQ=
  dependencies:
    debug "^3.2.7"

eslint-plugin-eslint-comments@^3.1.2, eslint-plugin-eslint-comments@^3.2.0:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/eslint-plugin-eslint-comments/download/eslint-plugin-eslint-comments-3.2.0.tgz#9e1cd7b4413526abb313933071d7aba05ca12ffa"
  integrity sha1-nhzXtEE1JquzE5MwcderoFyhL/o=
  dependencies:
    escape-string-regexp "^1.0.5"
    ignore "^5.0.5"

eslint-plugin-flowtype@2.50.3:
  version "2.50.3"
  resolved "http://r.npm.sankuai.com/eslint-plugin-flowtype/download/eslint-plugin-flowtype-2.50.3.tgz#61379d6dce1d010370acd6681740fd913d68175f"
  integrity sha1-YTedbc4dAQNwrNZoF0D9kT1oF18=
  dependencies:
    lodash "^4.17.10"

eslint-plugin-ft-flow@^2.0.1:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/eslint-plugin-ft-flow/download/eslint-plugin-ft-flow-2.0.3.tgz#3b3c113c41902bcbacf0e22b536debcfc3c819e8"
  integrity sha1-OzwRPEGQK8us8OIrU23rz8PIGeg=
  dependencies:
    lodash "^4.17.21"
    string-natural-compare "^3.0.1"

eslint-plugin-ft-flow@^3.0.10:
  version "3.0.10"
  resolved "http://r.npm.sankuai.com/eslint-plugin-ft-flow/download/eslint-plugin-ft-flow-3.0.10.tgz#28556f826db97a275991eb456a14b1cb4bbe02fb"
  integrity sha1-KFVvgm25eidZketFahSxy0u+Avs=
  dependencies:
    lodash "^4.17.21"
    string-natural-compare "^3.0.1"

eslint-plugin-import@^2.29.1:
  version "2.29.1"
  resolved "http://r.npm.sankuai.com/eslint-plugin-import/download/eslint-plugin-import-2.29.1.tgz#d45b37b5ef5901d639c15270d74d46d161150643"
  integrity sha1-1Fs3te9ZAdY5wVJw101G0WEVBkM=
  dependencies:
    array-includes "^3.1.7"
    array.prototype.findlastindex "^1.2.3"
    array.prototype.flat "^1.3.2"
    array.prototype.flatmap "^1.3.2"
    debug "^3.2.7"
    doctrine "^2.1.0"
    eslint-import-resolver-node "^0.3.9"
    eslint-module-utils "^2.8.0"
    hasown "^2.0.0"
    is-core-module "^2.13.1"
    is-glob "^4.0.3"
    minimatch "^3.1.2"
    object.fromentries "^2.0.7"
    object.groupby "^1.0.1"
    object.values "^1.1.7"
    semver "^6.3.1"
    tsconfig-paths "^3.15.0"

eslint-plugin-jest@22.4.1:
  version "22.4.1"
  resolved "http://r.npm.sankuai.com/eslint-plugin-jest/download/eslint-plugin-jest-22.4.1.tgz#a5fd6f7a2a41388d16f527073b778013c5189a9c"
  integrity sha1-pf1veipBOI0W9ScHO3eAE8UYmpw=

eslint-plugin-jest@^26.5.3:
  version "26.9.0"
  resolved "http://r.npm.sankuai.com/eslint-plugin-jest/download/eslint-plugin-jest-26.9.0.tgz#7931c31000b1c19e57dbfb71bbf71b817d1bf949"
  integrity sha1-eTHDEACxwZ5X2/txu/cbgX0b+Uk=
  dependencies:
    "@typescript-eslint/utils" "^5.10.0"

eslint-plugin-prettier@3.1.2:
  version "3.1.2"
  resolved "http://r.npm.sankuai.com/eslint-plugin-prettier/download/eslint-plugin-prettier-3.1.2.tgz#432e5a667666ab84ce72f945c72f77d996a5c9ba"
  integrity sha1-Qy5aZnZmq4TOcvlFxy932Zalybo=
  dependencies:
    prettier-linter-helpers "^1.0.0"

eslint-plugin-prettier@^4.2.1:
  version "4.2.1"
  resolved "http://r.npm.sankuai.com/eslint-plugin-prettier/download/eslint-plugin-prettier-4.2.1.tgz#651cbb88b1dab98bfd42f017a12fa6b2d993f94b"
  integrity sha1-ZRy7iLHauYv9QvAXoS+mstmT+Us=
  dependencies:
    prettier-linter-helpers "^1.0.0"

eslint-plugin-react-hooks@^4.0.4, eslint-plugin-react-hooks@^4.6.0:
  version "4.6.0"
  resolved "http://r.npm.sankuai.com/eslint-plugin-react-hooks/download/eslint-plugin-react-hooks-4.6.0.tgz#4c3e697ad95b77e93f8646aaa1630c1ba607edd3"
  integrity sha1-TD5petlbd+k/hkaqoWMMG6YH7dM=

eslint-plugin-react-native-globals@^0.1.1:
  version "0.1.2"
  resolved "http://r.npm.sankuai.com/eslint-plugin-react-native-globals/download/eslint-plugin-react-native-globals-0.1.2.tgz#ee1348bc2ceb912303ce6bdbd22e2f045ea86ea2"
  integrity sha1-7hNIvCzrkSMDzmvb0i4vBF6obqI=

eslint-plugin-react-native@^3.8.1:
  version "3.11.0"
  resolved "http://r.npm.sankuai.com/eslint-plugin-react-native/download/eslint-plugin-react-native-3.11.0.tgz#c73b0886abb397867e5e6689d3a6a418682e6bac"
  integrity sha1-xzsIhquzl4Z+XmaJ06akGGgua6w=
  dependencies:
    "@babel/traverse" "^7.7.4"
    eslint-plugin-react-native-globals "^0.1.1"

eslint-plugin-react-native@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/eslint-plugin-react-native/download/eslint-plugin-react-native-4.0.0.tgz#eec41984abe4970bdd7c6082dff7a98a5e34d0bb"
  integrity sha1-7sQZhKvklwvdfGCC3/epil400Ls=
  dependencies:
    "@babel/traverse" "^7.7.4"
    eslint-plugin-react-native-globals "^0.1.1"

eslint-plugin-react@^7.20.0, eslint-plugin-react@^7.30.1:
  version "7.32.2"
  resolved "http://r.npm.sankuai.com/eslint-plugin-react/download/eslint-plugin-react-7.32.2.tgz#e71f21c7c265ebce01bcbc9d0955170c55571f10"
  integrity sha1-5x8hx8Jl684BvLydCVUXDFVXHxA=
  dependencies:
    array-includes "^3.1.6"
    array.prototype.flatmap "^1.3.1"
    array.prototype.tosorted "^1.1.1"
    doctrine "^2.1.0"
    estraverse "^5.3.0"
    jsx-ast-utils "^2.4.1 || ^3.0.0"
    minimatch "^3.1.2"
    object.entries "^1.1.6"
    object.fromentries "^2.0.6"
    object.hasown "^1.1.2"
    object.values "^1.1.6"
    prop-types "^15.8.1"
    resolve "^2.0.0-next.4"
    semver "^6.3.0"
    string.prototype.matchall "^4.0.8"

eslint-plugin-unused-imports@3:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/eslint-plugin-unused-imports/download/eslint-plugin-unused-imports-3.2.0.tgz#63a98c9ad5f622cd9f830f70bc77739f25ccfe0d"
  integrity sha1-Y6mMmtX2Is2fgw9wvHdznyXM/g0=
  dependencies:
    eslint-rule-composer "^0.3.0"

eslint-rule-composer@^0.3.0:
  version "0.3.0"
  resolved "http://r.npm.sankuai.com/eslint-rule-composer/download/eslint-rule-composer-0.3.0.tgz#79320c927b0c5c0d3d3d2b76c8b4a488f25bbaf9"
  integrity sha1-eTIMknsMXA09PSt2yLSkiPJbuvk=

eslint-scope@5.1.1, eslint-scope@^5.0.0, eslint-scope@^5.1.1:
  version "5.1.1"
  resolved "http://r.npm.sankuai.com/eslint-scope/download/eslint-scope-5.1.1.tgz#e786e59a66cb92b3f6c1fb0d508aab174848f48c"
  integrity sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^4.1.1"

eslint-utils@^2.0.0, eslint-utils@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/eslint-utils/download/eslint-utils-2.1.0.tgz#d2de5e03424e707dc10c74068ddedae708741b27"
  integrity sha1-0t5eA0JOcH3BDHQGjd7a5wh0Gyc=
  dependencies:
    eslint-visitor-keys "^1.1.0"

eslint-visitor-keys@^1.0.0, eslint-visitor-keys@^1.1.0, eslint-visitor-keys@^1.3.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/eslint-visitor-keys/download/eslint-visitor-keys-1.3.0.tgz#30ebd1ef7c2fdff01c3a4f151044af25fab0523e"
  integrity sha1-MOvR73wv3/AcOk8VEESvJfqwUj4=

eslint-visitor-keys@^2.0.0, eslint-visitor-keys@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/eslint-visitor-keys/download/eslint-visitor-keys-2.1.0.tgz#f65328259305927392c938ed44eb0a5c9b2bd303"
  integrity sha1-9lMoJZMFknOSyTjtROsKXJsr0wM=

eslint-visitor-keys@^3.3.0:
  version "3.4.0"
  resolved "http://r.npm.sankuai.com/eslint-visitor-keys/download/eslint-visitor-keys-3.4.0.tgz#c7f0f956124ce677047ddbc192a68f999454dedc"
  integrity sha1-x/D5VhJM5ncEfdvBkqaPmZRU3tw=

eslint@^7.18.0:
  version "7.32.0"
  resolved "http://r.npm.sankuai.com/eslint/download/eslint-7.32.0.tgz#c6d328a14be3fb08c8d1d21e12c02fdb7a2a812d"
  integrity sha1-xtMooUvj+wjI0dIeEsAv23oqgS0=
  dependencies:
    "@babel/code-frame" "7.12.11"
    "@eslint/eslintrc" "^0.4.3"
    "@humanwhocodes/config-array" "^0.5.0"
    ajv "^6.10.0"
    chalk "^4.0.0"
    cross-spawn "^7.0.2"
    debug "^4.0.1"
    doctrine "^3.0.0"
    enquirer "^2.3.5"
    escape-string-regexp "^4.0.0"
    eslint-scope "^5.1.1"
    eslint-utils "^2.1.0"
    eslint-visitor-keys "^2.0.0"
    espree "^7.3.1"
    esquery "^1.4.0"
    esutils "^2.0.2"
    fast-deep-equal "^3.1.3"
    file-entry-cache "^6.0.1"
    functional-red-black-tree "^1.0.1"
    glob-parent "^5.1.2"
    globals "^13.6.0"
    ignore "^4.0.6"
    import-fresh "^3.0.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    js-yaml "^3.13.1"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.4.1"
    lodash.merge "^4.6.2"
    minimatch "^3.0.4"
    natural-compare "^1.4.0"
    optionator "^0.9.1"
    progress "^2.0.0"
    regexpp "^3.1.0"
    semver "^7.2.1"
    strip-ansi "^6.0.0"
    strip-json-comments "^3.1.0"
    table "^6.0.9"
    text-table "^0.2.0"
    v8-compile-cache "^2.0.3"

espree@^7.3.0, espree@^7.3.1:
  version "7.3.1"
  resolved "http://r.npm.sankuai.com/espree/download/espree-7.3.1.tgz#f2df330b752c6f55019f8bd89b7660039c1bbbb6"
  integrity sha1-8t8zC3Usb1UBn4vYm3ZgA5wbu7Y=
  dependencies:
    acorn "^7.4.0"
    acorn-jsx "^5.3.1"
    eslint-visitor-keys "^1.3.0"

esprima@^4.0.0, esprima@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/esprima/download/esprima-4.0.1.tgz#13b04cdb3e6c5d19df91ab6987a8695619b0aa71"
  integrity sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=

esquery@^1.4.0:
  version "1.5.0"
  resolved "http://r.npm.sankuai.com/esquery/download/esquery-1.5.0.tgz#6ce17738de8577694edd7361c57182ac8cb0db0b"
  integrity sha1-bOF3ON6Fd2lO3XNhxXGCrIyw2ws=
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/esrecurse/download/esrecurse-4.3.0.tgz#7ad7964d679abb28bee72cec63758b1c5d2c9921"
  integrity sha1-eteWTWeauyi+5yzsY3WLHF0smSE=
  dependencies:
    estraverse "^5.2.0"

estraverse@^4.1.1:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/estraverse/download/estraverse-4.3.0.tgz#398ad3f3c5a24948be7725e83d11a7de28cdbd1d"
  integrity sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=

estraverse@^5.1.0, estraverse@^5.2.0, estraverse@^5.3.0:
  version "5.3.0"
  resolved "http://r.npm.sankuai.com/estraverse/download/estraverse-5.3.0.tgz#2eea5290702f26ab8fe5370370ff86c965d21123"
  integrity sha1-LupSkHAvJquP5TcDcP+GyWXSESM=

esutils@^2.0.2:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/esutils/download/esutils-2.0.3.tgz#74d2eb4de0b8da1293711910d50775b9b710ef64"
  integrity sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=

etag@~1.8.1:
  version "1.8.1"
  resolved "http://r.npm.sankuai.com/etag/download/etag-1.8.1.tgz#41ae2eeb65efa62268aebfea83ac7d79299b0887"
  integrity sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=

event-target-shim@^5.0.0, event-target-shim@^5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/event-target-shim/download/event-target-shim-5.0.1.tgz#5d4d3ebdf9583d63a5333ce2deb7480ab2b05789"
  integrity sha1-XU0+vflYPWOlMzzi3rdICrKwV4k=

eventemitter3@^3.0.0:
  version "3.1.2"
  resolved "http://r.npm.sankuai.com/eventemitter3/download/eventemitter3-3.1.2.tgz#2d3d48f9c346698fce83a85d7d664e98535df6e7"
  integrity sha1-LT1I+cNGaY/Og6hdfWZOmFNd9uc=

eventemitter3@^4.0.7:
  version "4.0.7"
  resolved "http://r.npm.sankuai.com/eventemitter3/download/eventemitter3-4.0.7.tgz#2de9b68f6528d5644ef5c59526a1b4a07306169f"
  integrity sha1-Lem2j2Uo1WRO9cWVJqG0oHMGFp8=

events@^3.3.0:
  version "3.3.0"
  resolved "http://r.npm.sankuai.com/events/download/events-3.3.0.tgz#31a95ad0a924e2d2c419a813aeb2c4e878ea7400"
  integrity sha1-Mala0Kkk4tLEGagTrrLE6HjqdAA=

exec-sh@^0.3.2:
  version "0.3.6"
  resolved "http://r.npm.sankuai.com/exec-sh/download/exec-sh-0.3.6.tgz#ff264f9e325519a60cb5e273692943483cca63bc"
  integrity sha1-/yZPnjJVGaYMteJzaSlDSDzKY7w=

execa@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/execa/download/execa-1.0.0.tgz#c6236a5bb4df6d6f15e88e7f017798216749ddd8"
  integrity sha1-xiNqW7TfbW8V6I5/AXeYIWdJ3dg=
  dependencies:
    cross-spawn "^6.0.0"
    get-stream "^4.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

execa@^4.0.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/execa/download/execa-4.1.0.tgz#4e5491ad1572f2f17a77d388c6c857135b22847a"
  integrity sha1-TlSRrRVy8vF6d9OIxshXE1sihHo=
  dependencies:
    cross-spawn "^7.0.0"
    get-stream "^5.0.0"
    human-signals "^1.1.1"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.0"
    onetime "^5.1.0"
    signal-exit "^3.0.2"
    strip-final-newline "^2.0.0"

execa@^5.1.1:
  version "5.1.1"
  resolved "http://r.npm.sankuai.com/execa/download/execa-5.1.1.tgz#f80ad9cbf4298f7bd1d4c9555c21e93741c411dd"
  integrity sha1-+ArZy/Qpj3vR1MlVXCHpN0HEEd0=
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.0"
    human-signals "^2.1.0"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.1"
    onetime "^5.1.2"
    signal-exit "^3.0.3"
    strip-final-newline "^2.0.0"

exit@^0.1.2:
  version "0.1.2"
  resolved "http://r.npm.sankuai.com/exit/download/exit-0.1.2.tgz#0632638f8d877cc82107d30a0fff1a17cba1cd0c"
  integrity sha1-BjJjj42HfMghB9MKD/8aF8uhzQw=

expand-brackets@^2.1.4:
  version "2.1.4"
  resolved "http://r.npm.sankuai.com/expand-brackets/download/expand-brackets-2.1.4.tgz#b77735e315ce30f6b6eff0f83b04151a22449622"
  integrity sha1-t3c14xXOMPa27/D4OwQVGiJEliI=
  dependencies:
    debug "^2.3.3"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    posix-character-classes "^0.1.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

expect@^26.6.2:
  version "26.6.2"
  resolved "http://r.npm.sankuai.com/expect/download/expect-26.6.2.tgz#c6b996bf26bf3fe18b67b2d0f51fc981ba934417"
  integrity sha1-xrmWvya/P+GLZ7LQ9R/JgbqTRBc=
  dependencies:
    "@jest/types" "^26.6.2"
    ansi-styles "^4.0.0"
    jest-get-type "^26.3.0"
    jest-matcher-utils "^26.6.2"
    jest-message-util "^26.6.2"
    jest-regex-util "^26.0.0"

ext-list@^2.0.0:
  version "2.2.2"
  resolved "http://r.npm.sankuai.com/ext-list/download/ext-list-2.2.2.tgz#0b98e64ed82f5acf0f2931babf69212ef52ddd37"
  integrity sha1-C5jmTtgvWs8PKTG6v2khLvUt3Tc=
  dependencies:
    mime-db "^1.28.0"

ext-name@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/ext-name/download/ext-name-5.0.0.tgz#70781981d183ee15d13993c8822045c506c8f0a6"
  integrity sha1-cHgZgdGD7hXROZPIgiBFxQbI8KY=
  dependencies:
    ext-list "^2.0.0"
    sort-keys-length "^1.0.0"

extend-shallow@^1.1.2:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/extend-shallow/download/extend-shallow-1.1.4.tgz#19d6bf94dfc09d76ba711f39b872d21ff4dd9071"
  integrity sha1-Gda/lN/AnXa6cR85uHLSH/TdkHE=
  dependencies:
    kind-of "^1.1.0"

extend-shallow@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/extend-shallow/download/extend-shallow-2.0.1.tgz#51af7d614ad9a9f610ea1bafbb989d6b1c56890f"
  integrity sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=
  dependencies:
    is-extendable "^0.1.0"

extend-shallow@^3.0.0, extend-shallow@^3.0.2:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/extend-shallow/download/extend-shallow-3.0.2.tgz#26a71aaf073b39fb2127172746131c2704028db8"
  integrity sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=
  dependencies:
    assign-symbols "^1.0.0"
    is-extendable "^1.0.1"

external-editor@^2.0.4:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/external-editor/download/external-editor-2.2.0.tgz#045511cfd8d133f3846673d1047c154e214ad3d5"
  integrity sha1-BFURz9jRM/OEZnPRBHwVTiFK09U=
  dependencies:
    chardet "^0.4.0"
    iconv-lite "^0.4.17"
    tmp "^0.0.33"

external-editor@^3.0.3:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/external-editor/download/external-editor-3.1.0.tgz#cb03f740befae03ea4d283caed2741a83f335495"
  integrity sha1-ywP3QL764D6k0oPK7SdBqD8zVJU=
  dependencies:
    chardet "^0.7.0"
    iconv-lite "^0.4.24"
    tmp "^0.0.33"

extglob@^2.0.4:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/extglob/download/extglob-2.0.4.tgz#ad00fe4dc612a9232e8718711dc5cb5ab0285543"
  integrity sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM=
  dependencies:
    array-unique "^0.3.2"
    define-property "^1.0.0"
    expand-brackets "^2.1.4"
    extend-shallow "^2.0.1"
    fragment-cache "^0.2.1"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

fancy-log@^1.3.2:
  version "1.3.3"
  resolved "http://r.npm.sankuai.com/fancy-log/download/fancy-log-1.3.3.tgz#dbc19154f558690150a23953a0adbd035be45fc7"
  integrity sha1-28GRVPVYaQFQojlToK29A1vkX8c=
  dependencies:
    ansi-gray "^0.1.1"
    color-support "^1.1.3"
    parse-node-version "^1.0.0"
    time-stamp "^1.0.0"

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "http://r.npm.sankuai.com/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz#3a7d56b559d6cbc3eb512325244e619a65c6c525"
  integrity sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=

fast-diff@^1.1.2:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/fast-diff/download/fast-diff-1.2.0.tgz#73ee11982d86caaf7959828d519cfe927fac5f03"
  integrity sha1-c+4RmC2Gyq95WYKNUZz+kn+sXwM=

fast-glob@^3.2.9:
  version "3.2.12"
  resolved "http://r.npm.sankuai.com/fast-glob/download/fast-glob-3.2.12.tgz#7f39ec99c2e6ab030337142da9e0c18f37afae80"
  integrity sha1-fznsmcLmqwMDNxQtqeDBjzevroA=
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.4"

fast-json-stable-stringify@2.x, fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz#874bf69c6f404c2b5d99c481341399fd55892633"
  integrity sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=

fast-levenshtein@^2.0.6, fast-levenshtein@~2.0.6:
  version "2.0.6"
  resolved "http://r.npm.sankuai.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz#3d8a5c66883a16a30ca8643e851f19baa7797917"
  integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=

fastq@^1.6.0:
  version "1.15.0"
  resolved "http://r.npm.sankuai.com/fastq/download/fastq-1.15.0.tgz#d04d07c6a2a68fe4599fea8d2e103a937fae6b3a"
  integrity sha1-0E0HxqKmj+RZn+qNLhA6k3+uazo=
  dependencies:
    reusify "^1.0.4"

fb-watchman@^2.0.0:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/fb-watchman/download/fb-watchman-2.0.2.tgz#e9524ee6b5c77e9e5001af0f85f3adbb8623255c"
  integrity sha1-6VJO5rXHfp5QAa8PhfOtu4YjJVw=
  dependencies:
    bser "2.1.1"

fbjs-css-vars@^1.0.0:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/fbjs-css-vars/download/fbjs-css-vars-1.0.2.tgz#216551136ae02fe255932c3ec8775f18e2c078b8"
  integrity sha1-IWVRE2rgL+JVkyw+yHdfGOLAeLg=

fbjs-scripts@^1.1.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/fbjs-scripts/download/fbjs-scripts-1.2.0.tgz#069a0c0634242d10031c6460ef1fccefcdae8b27"
  integrity sha1-BpoMBjQkLRADHGRg7x/M782uiyc=
  dependencies:
    "@babel/core" "^7.0.0"
    ansi-colors "^1.0.1"
    babel-preset-fbjs "^3.2.0"
    core-js "^2.4.1"
    cross-spawn "^5.1.0"
    fancy-log "^1.3.2"
    object-assign "^4.0.1"
    plugin-error "^0.1.2"
    semver "^5.1.0"
    through2 "^2.0.0"

fbjs@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/fbjs/download/fbjs-1.0.0.tgz#52c215e0883a3c86af2a7a776ed51525ae8e0a5a"
  integrity sha1-UsIV4Ig6PIavKnp3btUVJa6OClo=
  dependencies:
    core-js "^2.4.1"
    fbjs-css-vars "^1.0.0"
    isomorphic-fetch "^2.1.1"
    loose-envify "^1.0.0"
    object-assign "^4.1.0"
    promise "^7.1.1"
    setimmediate "^1.0.5"
    ua-parser-js "^0.7.18"

fd-slicer@~1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/fd-slicer/download/fd-slicer-1.1.0.tgz#25c7c89cb1f9077f8891bbe61d8f390eae256f1e"
  integrity sha1-JcfInLH5B3+IkbvmHY85Dq4lbx4=
  dependencies:
    pend "~1.2.0"

figures@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/figures/download/figures-2.0.0.tgz#3ab1a2d2a62c8bfb431a0c94cb797a2fce27c962"
  integrity sha1-OrGi0qYsi/tDGgyUy3l6L84nyWI=
  dependencies:
    escape-string-regexp "^1.0.5"

figures@^3.0.0:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/figures/download/figures-3.2.0.tgz#625c18bd293c604dc4a8ddb2febf0c88341746af"
  integrity sha1-YlwYvSk8YE3EqN2y/r8MiDQXRq8=
  dependencies:
    escape-string-regexp "^1.0.5"

file-entry-cache@^6.0.1:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/file-entry-cache/download/file-entry-cache-6.0.1.tgz#211b2dd9659cb0394b073e7323ac3c933d522027"
  integrity sha1-IRst2WWcsDlLBz5zI6w8kz1SICc=
  dependencies:
    flat-cache "^3.0.4"

file-type@^11.1.0:
  version "11.1.0"
  resolved "http://r.npm.sankuai.com/file-type/download/file-type-11.1.0.tgz#93780f3fed98b599755d846b99a1617a2ad063b8"
  integrity sha1-k3gPP+2YtZl1XYRrmaFheirQY7g=

file-type@^3.8.0:
  version "3.9.0"
  resolved "http://r.npm.sankuai.com/file-type/download/file-type-3.9.0.tgz#257a078384d1db8087bc449d107d52a52672b9e9"
  integrity sha1-JXoHg4TR24CHvESdEH1SpSZyuek=

file-type@^4.2.0:
  version "4.4.0"
  resolved "http://r.npm.sankuai.com/file-type/download/file-type-4.4.0.tgz#1b600e5fca1fbdc6e80c0a70c71c8dba5f7906c5"
  integrity sha1-G2AOX8ofvcboDApwxxyNul95BsU=

file-type@^5.2.0:
  version "5.2.0"
  resolved "http://r.npm.sankuai.com/file-type/download/file-type-5.2.0.tgz#2ddbea7c73ffe36368dfae49dc338c058c2b8ad6"
  integrity sha1-LdvqfHP/42No365J3DOMBYwritY=

file-type@^6.1.0:
  version "6.2.0"
  resolved "http://r.npm.sankuai.com/file-type/download/file-type-6.2.0.tgz#e50cd75d356ffed4e306dc4f5bcf52a79903a919"
  integrity sha1-5QzXXTVv/tTjBtxPW89Sp5kDqRk=

file-uri-to-path@1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/file-uri-to-path/download/file-uri-to-path-1.0.0.tgz#553a7b8446ff6f684359c445f1e37a05dacc33dd"
  integrity sha1-VTp7hEb/b2hDWcRF8eN6BdrMM90=

filelist@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/filelist/download/filelist-1.0.4.tgz#f78978a1e944775ff9e62e744424f215e58352b5"
  integrity sha1-94l4oelEd1/55i50RCTyFeWDUrU=
  dependencies:
    minimatch "^5.0.1"

filename-reserved-regex@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/filename-reserved-regex/download/filename-reserved-regex-2.0.0.tgz#abf73dfab735d045440abfea2d91f389ebbfa229"
  integrity sha1-q/c9+rc10EVECr/qLZHzieu/oik=

filenamify@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/filenamify/download/filenamify-3.0.0.tgz#9603eb688179f8c5d40d828626dcbb92c3a4672c"
  integrity sha1-lgPraIF5+MXUDYKGJty7ksOkZyw=
  dependencies:
    filename-reserved-regex "^2.0.0"
    strip-outer "^1.0.0"
    trim-repeated "^1.0.0"

fill-range@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/fill-range/download/fill-range-4.0.0.tgz#d544811d428f98eb06a63dc402d2403c328c38f7"
  integrity sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=
  dependencies:
    extend-shallow "^2.0.1"
    is-number "^3.0.0"
    repeat-string "^1.6.1"
    to-regex-range "^2.1.0"

fill-range@^7.0.1:
  version "7.0.1"
  resolved "http://r.npm.sankuai.com/fill-range/download/fill-range-7.0.1.tgz#1919a6a7c75fe38b2c7c77e5198535da9acdda40"
  integrity sha1-GRmmp8df44ssfHflGYU12prN2kA=
  dependencies:
    to-regex-range "^5.0.1"

filter-obj@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/filter-obj/download/filter-obj-1.1.0.tgz#9b311112bc6c6127a16e016c6c5d7f19e0805c5b"
  integrity sha1-mzERErxsYSehbgFsbF1/GeCAXFs=

finalhandler@1.1.2:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/finalhandler/download/finalhandler-1.1.2.tgz#b7e7d000ffd11938d0fdb053506f6ebabe9f587d"
  integrity sha1-t+fQAP/RGTjQ/bBTUG9uur6fWH0=
  dependencies:
    debug "2.6.9"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    on-finished "~2.3.0"
    parseurl "~1.3.3"
    statuses "~1.5.0"
    unpipe "~1.0.0"

find-babel-config@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/find-babel-config/download/find-babel-config-1.2.0.tgz#a9b7b317eb5b9860cda9d54740a8c8337a2283a2"
  integrity sha1-qbezF+tbmGDNqdVHQKjIM3oig6I=
  dependencies:
    json5 "^0.5.1"
    path-exists "^3.0.0"

find-cache-dir@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/find-cache-dir/download/find-cache-dir-2.1.0.tgz#8d0f94cd13fe43c6c7c261a0d86115ca918c05f7"
  integrity sha1-jQ+UzRP+Q8bHwmGg2GEVypGMBfc=
  dependencies:
    commondir "^1.0.1"
    make-dir "^2.0.0"
    pkg-dir "^3.0.0"

find-root@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/find-root/download/find-root-1.1.0.tgz#abcfc8ba76f708c42a97b3d685b7e9450bfb9ce4"
  integrity sha1-q8/Iunb3CMQql7PWhbfpRQv7nOQ=

find-up@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/find-up/download/find-up-3.0.0.tgz#49169f1d7993430646da61ecc5ae355c21c97b73"
  integrity sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=
  dependencies:
    locate-path "^3.0.0"

find-up@^4.0.0, find-up@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/find-up/download/find-up-4.1.0.tgz#97afe7d6cdc0bc5928584b7c8d7b16e8a9aa5d19"
  integrity sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

find-up@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/find-up/download/find-up-5.0.0.tgz#4c92819ecb7083561e4f4a240a86be5198f536fc"
  integrity sha1-TJKBnstwg1YeT0okCoa+UZj1Nvw=
  dependencies:
    locate-path "^6.0.0"
    path-exists "^4.0.0"

find-versions@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/find-versions/download/find-versions-4.0.0.tgz#3c57e573bf97769b8cb8df16934b627915da4965"
  integrity sha1-PFflc7+XdpuMuN8Wk0tieRXaSWU=
  dependencies:
    semver-regex "^3.1.2"

flat-cache@^3.0.4:
  version "3.0.4"
  resolved "http://r.npm.sankuai.com/flat-cache/download/flat-cache-3.0.4.tgz#61b0338302b2fe9f957dcc32fc2a87f1c3048b11"
  integrity sha1-YbAzgwKy/p+Vfcwy/CqH8cMEixE=
  dependencies:
    flatted "^3.1.0"
    rimraf "^3.0.2"

flatted@^3.1.0:
  version "3.2.7"
  resolved "http://r.npm.sankuai.com/flatted/download/flatted-3.2.7.tgz#609f39207cb614b89d0765b477cb2d437fbf9787"
  integrity sha1-YJ85IHy2FLidB2W0d8stQ3+/l4c=

follow-redirects@^1.14.4:
  version "1.15.3"
  resolved "http://r.npm.sankuai.com/follow-redirects/download/follow-redirects-1.15.3.tgz#fe2f3ef2690afce7e82ed0b44db08165b207123a"
  integrity sha1-/i8+8mkK/OfoLtC0TbCBZbIHEjo=

for-each@^0.3.3:
  version "0.3.3"
  resolved "http://r.npm.sankuai.com/for-each/download/for-each-0.3.3.tgz#69b447e88a0a5d32c3e7084f3f1710034b21376e"
  integrity sha1-abRH6IoKXTLD5whPPxcQA0shN24=
  dependencies:
    is-callable "^1.1.3"

for-in@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/for-in/download/for-in-1.0.2.tgz#81068d295a8142ec0ac726c6e2200c30fb6d5e80"
  integrity sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=

form-data@^3.0.0:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/form-data/download/form-data-3.0.1.tgz#ebd53791b78356a99af9a300d4282c4d5eb9755f"
  integrity sha1-69U3kbeDVqma+aMA1CgsTV65dV8=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    mime-types "^2.1.12"

fragment-cache@^0.2.1:
  version "0.2.1"
  resolved "http://r.npm.sankuai.com/fragment-cache/download/fragment-cache-0.2.1.tgz#4290fad27f13e89be7f33799c6bc5a0abfff0d19"
  integrity sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=
  dependencies:
    map-cache "^0.2.2"

fresh@0.5.2:
  version "0.5.2"
  resolved "http://r.npm.sankuai.com/fresh/download/fresh-0.5.2.tgz#3d8cadd90d976569fa835ab1f8e4b23a105605a7"
  integrity sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=

from2@^2.1.1:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/from2/download/from2-2.3.0.tgz#8bfb5502bde4a4d36cfdeea007fcca21d7e382af"
  integrity sha1-i/tVAr3kpNNs/e6gB/zKIdfjgq8=
  dependencies:
    inherits "^2.0.1"
    readable-stream "^2.0.0"

fs-constants@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/fs-constants/download/fs-constants-1.0.0.tgz#6be0de9be998ce16af8afc24497b9ee9b7ccd9ad"
  integrity sha1-a+Dem+mYzhavivwkSXue6bfM2a0=

fs-extra@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/fs-extra/download/fs-extra-1.0.0.tgz#cd3ce5f7e7cb6145883fcae3191e9877f8587950"
  integrity sha1-zTzl9+fLYUWIP8rjGR6Yd/hYeVA=
  dependencies:
    graceful-fs "^4.1.2"
    jsonfile "^2.1.0"
    klaw "^1.0.0"

fs-extra@^8.1.0:
  version "8.1.0"
  resolved "http://r.npm.sankuai.com/fs-extra/download/fs-extra-8.1.0.tgz#49d43c45a88cd9677668cb7be1b46efdb8d2e1c0"
  integrity sha1-SdQ8RaiM2Wd2aMt74bRu/bjS4cA=
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^4.0.0"
    universalify "^0.1.0"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/fs.realpath/download/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

fsevents@^1.2.7:
  version "1.2.13"
  resolved "http://r.npm.sankuai.com/fsevents/download/fsevents-1.2.13.tgz#f325cb0455592428bcf11b383370ef70e3bfcc38"
  integrity sha1-8yXLBFVZJCi88Rs4M3DvcOO/zDg=
  dependencies:
    bindings "^1.5.0"
    nan "^2.12.1"

fsevents@^2.1.2:
  version "2.3.2"
  resolved "http://r.npm.sankuai.com/fsevents/download/fsevents-2.3.2.tgz#8a526f78b8fdf4623b709e0b975c52c24c02fd1a"
  integrity sha1-ilJveLj99GI7cJ4Ll1xSwkwC/Ro=

function-bind@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/function-bind/download/function-bind-1.1.1.tgz#a56899d3ea3c9bab874bb9773b7c5ede92f4895d"
  integrity sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0=

function-bind@^1.1.2:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/function-bind/download/function-bind-1.1.2.tgz#2c02d864d97f3ea6c8830c464cbd11ab6eab7a1c"
  integrity sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=

function.prototype.name@^1.1.5:
  version "1.1.5"
  resolved "http://r.npm.sankuai.com/function.prototype.name/download/function.prototype.name-1.1.5.tgz#cce0505fe1ffb80503e6f9e46cc64e46a12a9621"
  integrity sha1-zOBQX+H/uAUD5vnkbMZORqEqliE=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    es-abstract "^1.19.0"
    functions-have-names "^1.2.2"

function.prototype.name@^1.1.6:
  version "1.1.6"
  resolved "http://r.npm.sankuai.com/function.prototype.name/download/function.prototype.name-1.1.6.tgz#cdf315b7d90ee77a4c6ee216c3c3362da07533fd"
  integrity sha1-zfMVt9kO53pMbuIWw8M2LaB1M/0=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    functions-have-names "^1.2.3"

functional-red-black-tree@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/functional-red-black-tree/download/functional-red-black-tree-1.0.1.tgz#1b0ab3bd553b2a0d6399d29c0e3ea0b252078327"
  integrity sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=

functions-have-names@^1.2.2, functions-have-names@^1.2.3:
  version "1.2.3"
  resolved "http://r.npm.sankuai.com/functions-have-names/download/functions-have-names-1.2.3.tgz#0404fe4ee2ba2f607f0e0ec3c80bae994133b834"
  integrity sha1-BAT+TuK6L2B/Dg7DyAuumUEzuDQ=

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "http://r.npm.sankuai.com/gensync/download/gensync-1.0.0-beta.2.tgz#32a6ee76c3d7f52d46b2b1ae5d93fea8580a25e0"
  integrity sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=

geojson@^0.5.0:
  version "0.5.0"
  resolved "http://r.npm.sankuai.com/geojson/download/geojson-0.5.0.tgz#3cd6c96399be65b56ee55596116fe9191ce701c0"
  integrity sha1-PNbJY5m+ZbVu5VWWEW/pGRznAcA=

get-caller-file@^2.0.1:
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/get-caller-file/download/get-caller-file-2.0.5.tgz#4f94412a82db32f36e3b0b9741f8a97feb031f7e"
  integrity sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=

get-intrinsic@^1.0.2, get-intrinsic@^1.1.1, get-intrinsic@^1.1.3, get-intrinsic@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/get-intrinsic/download/get-intrinsic-1.2.0.tgz#7ad1dc0535f3a2904bba075772763e5051f6d05f"
  integrity sha1-etHcBTXzopBLugdXcnY+UFH20F8=
  dependencies:
    function-bind "^1.1.1"
    has "^1.0.3"
    has-symbols "^1.0.3"

get-intrinsic@^1.2.1, get-intrinsic@^1.2.3, get-intrinsic@^1.2.4:
  version "1.2.4"
  resolved "http://r.npm.sankuai.com/get-intrinsic/download/get-intrinsic-1.2.4.tgz#e385f5a4b5227d449c3eabbad05494ef0abbeadd"
  integrity sha1-44X1pLUifUScPqu60FSU7wq76t0=
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    has-proto "^1.0.1"
    has-symbols "^1.0.3"
    hasown "^2.0.0"

get-package-type@^0.1.0:
  version "0.1.0"
  resolved "http://r.npm.sankuai.com/get-package-type/download/get-package-type-0.1.0.tgz#8de2d803cff44df3bc6c456e6668b36c3926e11a"
  integrity sha1-jeLYA8/0TfO8bEVuZmizbDkm4Ro=

get-stdin@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/get-stdin/download/get-stdin-6.0.0.tgz#9e09bf712b360ab9225e812048f71fde9c89657b"
  integrity sha1-ngm/cSs2CrkiXoEgSPcf3pyJZXs=

get-stream@3.0.0, get-stream@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/get-stream/download/get-stream-3.0.0.tgz#8e943d1358dc37555054ecbe2edb05aa174ede14"
  integrity sha1-jpQ9E1jcN1VQVOy+LtsFqhdO3hQ=

get-stream@^2.2.0:
  version "2.3.1"
  resolved "http://r.npm.sankuai.com/get-stream/download/get-stream-2.3.1.tgz#5f38f93f346009666ee0150a054167f91bdd95de"
  integrity sha1-Xzj5PzRgCWZu4BUKBUFn+Rvdld4=
  dependencies:
    object-assign "^4.0.1"
    pinkie-promise "^2.0.0"

get-stream@^4.0.0, get-stream@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/get-stream/download/get-stream-4.1.0.tgz#c1b255575f3dc21d59bfc79cd3d2b46b1c3a54b5"
  integrity sha1-wbJVV189wh1Zv8ec09K0axw6VLU=
  dependencies:
    pump "^3.0.0"

get-stream@^5.0.0:
  version "5.2.0"
  resolved "http://r.npm.sankuai.com/get-stream/download/get-stream-5.2.0.tgz#4966a1795ee5ace65e706c4b7beb71257d6e22d3"
  integrity sha1-SWaheV7lrOZecGxLe+txJX1uItM=
  dependencies:
    pump "^3.0.0"

get-stream@^6.0.0:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/get-stream/download/get-stream-6.0.1.tgz#a262d8eef67aced57c2852ad6167526a43cbf7b7"
  integrity sha1-omLY7vZ6ztV8KFKtYWdSakPL97c=

get-symbol-description@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/get-symbol-description/download/get-symbol-description-1.0.0.tgz#7fdb81c900101fbd564dd5f1a30af5aadc1e58d6"
  integrity sha1-f9uByQAQH71WTdXxowr1qtweWNY=
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.1.1"

get-symbol-description@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/get-symbol-description/download/get-symbol-description-1.0.2.tgz#533744d5aa20aca4e079c8e5daf7fd44202821f5"
  integrity sha1-UzdE1aogrKTgecjl2vf9RCAoIfU=
  dependencies:
    call-bind "^1.0.5"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.4"

get-value@^2.0.3, get-value@^2.0.6:
  version "2.0.6"
  resolved "http://r.npm.sankuai.com/get-value/download/get-value-2.0.6.tgz#dc15ca1c672387ca76bd37ac0a395ba2042a2c28"
  integrity sha1-3BXKHGcjh8p2vTesCjlbogQqLCg=

glob-parent@^5.1.2:
  version "5.1.2"
  resolved "http://r.npm.sankuai.com/glob-parent/download/glob-parent-5.1.2.tgz#869832c58034fe68a4093c17dc15e8340d8401c4"
  integrity sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=
  dependencies:
    is-glob "^4.0.1"

glob@^7.1.1, glob@^7.1.2, glob@^7.1.3, glob@^7.1.4, glob@^7.1.6:
  version "7.2.3"
  resolved "http://r.npm.sankuai.com/glob/download/glob-7.2.3.tgz#b8df0fb802bbfa8e89bd1d938b4e16578ed44f2b"
  integrity sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globals@^11.1.0:
  version "11.12.0"
  resolved "http://r.npm.sankuai.com/globals/download/globals-11.12.0.tgz#ab8795338868a0babd8525758018c2a7eb95c42e"
  integrity sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=

globals@^13.6.0, globals@^13.9.0:
  version "13.20.0"
  resolved "http://r.npm.sankuai.com/globals/download/globals-13.20.0.tgz#ea276a1e508ffd4f1612888f9d1bad1e2717bf82"
  integrity sha1-6idqHlCP/U8WEoiPnRutHicXv4I=
  dependencies:
    type-fest "^0.20.2"

globalthis@^1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/globalthis/download/globalthis-1.0.3.tgz#5852882a52b80dc301b0660273e1ed082f0b6ccf"
  integrity sha1-WFKIKlK4DcMBsGYCc+HtCC8LbM8=
  dependencies:
    define-properties "^1.1.3"

globby@^11.1.0:
  version "11.1.0"
  resolved "http://r.npm.sankuai.com/globby/download/globby-11.1.0.tgz#bd4be98bb042f83d796f7e3811991fbe82a0d34b"
  integrity sha1-vUvpi7BC+D15b344EZkfvoKg00s=
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.2.9"
    ignore "^5.2.0"
    merge2 "^1.4.1"
    slash "^3.0.0"

gopd@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/gopd/download/gopd-1.0.1.tgz#29ff76de69dac7489b7c0918a5788e56477c332c"
  integrity sha1-Kf923mnax0ibfAkYpXiOVkd8Myw=
  dependencies:
    get-intrinsic "^1.1.3"

got@^8.3.1:
  version "8.3.2"
  resolved "http://r.npm.sankuai.com/got/download/got-8.3.2.tgz#1d23f64390e97f776cac52e5b936e5f514d2e937"
  integrity sha1-HSP2Q5Dpf3dsrFLluTbl9RTS6Tc=
  dependencies:
    "@sindresorhus/is" "^0.7.0"
    cacheable-request "^2.1.1"
    decompress-response "^3.3.0"
    duplexer3 "^0.1.4"
    get-stream "^3.0.0"
    into-stream "^3.1.0"
    is-retry-allowed "^1.1.0"
    isurl "^1.0.0-alpha5"
    lowercase-keys "^1.0.0"
    mimic-response "^1.0.0"
    p-cancelable "^0.4.0"
    p-timeout "^2.0.1"
    pify "^3.0.0"
    safe-buffer "^5.1.1"
    timed-out "^4.0.1"
    url-parse-lax "^3.0.0"
    url-to-options "^1.0.1"

graceful-fs@^4.1.10, graceful-fs@^4.1.15, graceful-fs@^4.1.2, graceful-fs@^4.1.3, graceful-fs@^4.1.6, graceful-fs@^4.1.9, graceful-fs@^4.2.0, graceful-fs@^4.2.4:
  version "4.2.11"
  resolved "http://r.npm.sankuai.com/graceful-fs/download/graceful-fs-4.2.11.tgz#4183e4e8bf08bb6e05bbb2f7d2e0c8f712ca40e3"
  integrity sha1-QYPk6L8Iu24Fu7L30uDI9xLKQOM=

grapheme-splitter@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/grapheme-splitter/download/grapheme-splitter-1.0.4.tgz#9cf3a665c6247479896834af35cf1dbb4400767e"
  integrity sha1-nPOmZcYkdHmJaDSvNc8du0QAdn4=

growly@^1.3.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/growly/download/growly-1.3.0.tgz#f10748cbe76af964b7c96c93c6bcc28af120c081"
  integrity sha1-8QdIy+dq+WS3yWyTxrzCivEgwIE=

has-bigints@^1.0.1, has-bigints@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/has-bigints/download/has-bigints-1.0.2.tgz#0871bd3e3d51626f6ca0966668ba35d5602d6eaa"
  integrity sha1-CHG9Pj1RYm9soJZmaLo11WAtbqo=

has-flag@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/has-flag/download/has-flag-3.0.0.tgz#b5d454dc2199ae225699f3467e5a07f3b955bafd"
  integrity sha1-tdRU3CGZriJWmfNGfloH87lVuv0=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/has-flag/download/has-flag-4.0.0.tgz#944771fd9c81c81265c4d6941860da06bb59479b"
  integrity sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=

has-flag@^5.0.0:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/has-flag/download/has-flag-5.0.1.tgz#5483db2ae02a472d1d0691462fc587d1843cd940"
  integrity sha1-VIPbKuAqRy0dBpFGL8WH0YQ82UA=

has-property-descriptors@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/has-property-descriptors/download/has-property-descriptors-1.0.0.tgz#610708600606d36961ed04c196193b6a607fa861"
  integrity sha1-YQcIYAYG02lh7QTBlhk7amB/qGE=
  dependencies:
    get-intrinsic "^1.1.1"

has-property-descriptors@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/has-property-descriptors/download/has-property-descriptors-1.0.2.tgz#963ed7d071dc7bf5f084c5bfbe0d1b6222586854"
  integrity sha1-lj7X0HHce/XwhMW/vg0bYiJYaFQ=
  dependencies:
    es-define-property "^1.0.0"

has-proto@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/has-proto/download/has-proto-1.0.1.tgz#1885c1305538958aff469fef37937c22795408e0"
  integrity sha1-GIXBMFU4lYr/Rp/vN5N8InlUCOA=

has-proto@^1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/has-proto/download/has-proto-1.0.3.tgz#b31ddfe9b0e6e9914536a6ab286426d0214f77fd"
  integrity sha1-sx3f6bDm6ZFFNqarKGQm0CFPd/0=

has-symbol-support-x@^1.4.1:
  version "1.4.2"
  resolved "http://r.npm.sankuai.com/has-symbol-support-x/download/has-symbol-support-x-1.4.2.tgz#1409f98bc00247da45da67cee0a36f282ff26455"
  integrity sha1-FAn5i8ACR9pF2mfO4KNvKC/yZFU=

has-symbols@^1.0.2, has-symbols@^1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/has-symbols/download/has-symbols-1.0.3.tgz#bb7b2c4349251dce87b125f7bdf874aa7c8b39f8"
  integrity sha1-u3ssQ0klHc6HsSX3vfh0qnyLOfg=

has-to-string-tag-x@^1.2.0:
  version "1.4.1"
  resolved "http://r.npm.sankuai.com/has-to-string-tag-x/download/has-to-string-tag-x-1.4.1.tgz#a045ab383d7b4b2012a00148ab0aa5f290044d4d"
  integrity sha1-oEWrOD17SyASoAFIqwql8pAETU0=
  dependencies:
    has-symbol-support-x "^1.4.1"

has-tostringtag@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/has-tostringtag/download/has-tostringtag-1.0.0.tgz#7e133818a7d394734f941e73c3d3f9291e658b25"
  integrity sha1-fhM4GKfTlHNPlB5zw9P5KR5liyU=
  dependencies:
    has-symbols "^1.0.2"

has-tostringtag@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/has-tostringtag/download/has-tostringtag-1.0.2.tgz#2cdc42d40bef2e5b4eeab7c01a73c54ce7ab5abc"
  integrity sha1-LNxC1AvvLltO6rfAGnPFTOerWrw=
  dependencies:
    has-symbols "^1.0.3"

has-value@^0.3.1:
  version "0.3.1"
  resolved "http://r.npm.sankuai.com/has-value/download/has-value-0.3.1.tgz#7b1f58bada62ca827ec0a2078025654845995e1f"
  integrity sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=
  dependencies:
    get-value "^2.0.3"
    has-values "^0.1.4"
    isobject "^2.0.0"

has-value@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/has-value/download/has-value-1.0.0.tgz#18b281da585b1c5c51def24c930ed29a0be6b177"
  integrity sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=
  dependencies:
    get-value "^2.0.6"
    has-values "^1.0.0"
    isobject "^3.0.0"

has-values@^0.1.4:
  version "0.1.4"
  resolved "http://r.npm.sankuai.com/has-values/download/has-values-0.1.4.tgz#6d61de95d91dfca9b9a02089ad384bff8f62b771"
  integrity sha1-bWHeldkd/Km5oCCJrThL/49it3E=

has-values@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/has-values/download/has-values-1.0.0.tgz#95b0b63fec2146619a6fe57fe75628d5a39efe4f"
  integrity sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=
  dependencies:
    is-number "^3.0.0"
    kind-of "^4.0.0"

has@^1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/has/download/has-1.0.3.tgz#722d7cbfc1f6aa8241f16dd814e011e1f41e8796"
  integrity sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y=
  dependencies:
    function-bind "^1.1.1"

hasown@^2.0.0, hasown@^2.0.1, hasown@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/hasown/download/hasown-2.0.2.tgz#003eaf91be7adc372e84ec59dc37252cedb80003"
  integrity sha1-AD6vkb563DcuhOxZ3DclLO24AAM=
  dependencies:
    function-bind "^1.1.2"

hermes-profile-transformer@^0.0.6:
  version "0.0.6"
  resolved "http://r.npm.sankuai.com/hermes-profile-transformer/download/hermes-profile-transformer-0.0.6.tgz#bd0f5ecceda80dd0ddaae443469ab26fb38fc27b"
  integrity sha1-vQ9ezO2oDdDdquRDRpqyb7OPwns=
  dependencies:
    source-map "^0.7.3"

hoist-non-react-statics@^3.3.0:
  version "3.3.2"
  resolved "http://r.npm.sankuai.com/hoist-non-react-statics/download/hoist-non-react-statics-3.3.2.tgz#ece0acaf71d62c2969c2ec59feff42a4b1a85b45"
  integrity sha1-7OCsr3HWLClpwuxZ/v9CpLGoW0U=
  dependencies:
    react-is "^16.7.0"

hosted-git-info@^2.1.4:
  version "2.8.9"
  resolved "http://r.npm.sankuai.com/hosted-git-info/download/hosted-git-info-2.8.9.tgz#dffc0bf9a21c02209090f2aa69429e1414daf3f9"
  integrity sha1-3/wL+aIcAiCQkPKqaUKeFBTa8/k=

html-encoding-sniffer@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/html-encoding-sniffer/download/html-encoding-sniffer-2.0.1.tgz#42a6dc4fd33f00281176e8b23759ca4e4fa185f3"
  integrity sha1-QqbcT9M/ACgRduiyN1nKTk+hhfM=
  dependencies:
    whatwg-encoding "^1.0.5"

html-escaper@^2.0.0:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/html-escaper/download/html-escaper-2.0.2.tgz#dfd60027da36a36dfcbe236262c00a5822681453"
  integrity sha1-39YAJ9o2o238viNiYsAKWCJoFFM=

http-cache-semantics@3.8.1:
  version "3.8.1"
  resolved "http://r.npm.sankuai.com/http-cache-semantics/download/http-cache-semantics-3.8.1.tgz#39b0e16add9b605bf0a9ef3d9daaf4843b4cacd2"
  integrity sha1-ObDhat2bYFvwqe89nar0hDtMrNI=

http-errors@2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/http-errors/download/http-errors-2.0.0.tgz#b7774a1486ef73cf7667ac9ae0858c012c57b9d3"
  integrity sha1-t3dKFIbvc892Z6ya4IWMASxXudM=
  dependencies:
    depd "2.0.0"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    toidentifier "1.0.1"

http-proxy-agent@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/http-proxy-agent/download/http-proxy-agent-4.0.1.tgz#8a8c8ef7f5932ccf953c296ca8291b95aa74aa3a"
  integrity sha1-ioyO9/WTLM+VPClsqCkblap0qjo=
  dependencies:
    "@tootallnate/once" "1"
    agent-base "6"
    debug "4"

https-proxy-agent@^5.0.0:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/https-proxy-agent/download/https-proxy-agent-5.0.1.tgz#c59ef224a04fe8b754f3db0063a25ea30d0005d6"
  integrity sha1-xZ7yJKBP6LdU89sAY6Jeow0ABdY=
  dependencies:
    agent-base "6"
    debug "4"

human-signals@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/human-signals/download/human-signals-1.1.1.tgz#c5b1cd14f50aeae09ab6c59fe63ba3395fe4dfa3"
  integrity sha1-xbHNFPUK6uCatsWf5jujOV/k36M=

human-signals@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/human-signals/download/human-signals-2.1.0.tgz#dc91fcba42e4d06e4abaed33b3e7a3c02f514ea0"
  integrity sha1-3JH8ukLk0G5Kuu0zs+ejwC9RTqA=

husky@^4.3.0:
  version "4.3.8"
  resolved "http://r.npm.sankuai.com/husky/download/husky-4.3.8.tgz#31144060be963fd6850e5cc8f019a1dfe194296d"
  integrity sha1-MRRAYL6WP9aFDlzI8Bmh3+GUKW0=
  dependencies:
    chalk "^4.0.0"
    ci-info "^2.0.0"
    compare-versions "^3.6.0"
    cosmiconfig "^7.0.0"
    find-versions "^4.0.0"
    opencollective-postinstall "^2.0.2"
    pkg-dir "^5.0.0"
    please-upgrade-node "^3.2.0"
    slash "^3.0.0"
    which-pm-runs "^1.0.0"

iconv-lite@0.4.24, iconv-lite@^0.4.17, iconv-lite@^0.4.24:
  version "0.4.24"
  resolved "http://r.npm.sankuai.com/iconv-lite/download/iconv-lite-0.4.24.tgz#2022b4b25fbddc21d2f524974a474aafe733908b"
  integrity sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

iconv-lite@^0.6.2:
  version "0.6.3"
  resolved "http://r.npm.sankuai.com/iconv-lite/download/iconv-lite-0.6.3.tgz#a52f80bf38da1952eb5c681790719871a1a72501"
  integrity sha1-pS+AvzjaGVLrXGgXkHGYcaGnJQE=
  dependencies:
    safer-buffer ">= 2.1.2 < 3.0.0"

ieee754@^1.1.13:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/ieee754/download/ieee754-1.2.1.tgz#8eb7a10a63fff25d15a57b001586d177d1b0d352"
  integrity sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=

ignore@^4.0.6:
  version "4.0.6"
  resolved "http://r.npm.sankuai.com/ignore/download/ignore-4.0.6.tgz#750e3db5862087b4737ebac8207ffd1ef27b25fc"
  integrity sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw=

ignore@^5.0.5, ignore@^5.2.0:
  version "5.2.4"
  resolved "http://r.npm.sankuai.com/ignore/download/ignore-5.2.4.tgz#a291c0c6178ff1b960befe47fcdec301674a6324"
  integrity sha1-opHAxheP8blgvv5H/N7DAWdKYyQ=

image-size@^0.6.0:
  version "0.6.3"
  resolved "http://r.npm.sankuai.com/image-size/download/image-size-0.6.3.tgz#e7e5c65bb534bd7cdcedd6cb5166272a85f75fb2"
  integrity sha1-5+XGW7U0vXzc7dbLUWYnKoX3X7I=

immer@^10.0.3:
  version "10.0.3"
  resolved "http://r.npm.sankuai.com/immer/download/immer-10.0.3.tgz#a8de42065e964aa3edf6afc282dfc7f7f34ae3c9"
  integrity sha1-qN5CBl6WSqPt9q/Cgt/H9/NK48k=

import-fresh@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/import-fresh/download/import-fresh-2.0.0.tgz#d81355c15612d386c61f9ddd3922d4304822a546"
  integrity sha1-2BNVwVYS04bGH53dOSLUMEgipUY=
  dependencies:
    caller-path "^2.0.0"
    resolve-from "^3.0.0"

import-fresh@^3.0.0, import-fresh@^3.2.1:
  version "3.3.0"
  resolved "http://r.npm.sankuai.com/import-fresh/download/import-fresh-3.3.0.tgz#37162c25fcb9ebaa2e6e53d5b4d88ce17d9e0c2b"
  integrity sha1-NxYsJfy566oublPVtNiM4X2eDCs=
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

import-local@^3.0.2:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/import-local/download/import-local-3.1.0.tgz#b4479df8a5fd44f6cdce24070675676063c95cb4"
  integrity sha1-tEed+KX9RPbNziQHBnVnYGPJXLQ=
  dependencies:
    pkg-dir "^4.2.0"
    resolve-cwd "^3.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "http://r.npm.sankuai.com/imurmurhash/download/imurmurhash-0.1.4.tgz#9218b9b2b928a238b13dc4fb6b6d576f231453ea"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

indent-string@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/indent-string/download/indent-string-4.0.0.tgz#624f8f4497d619b2d9768531d58f4122854d7251"
  integrity sha1-Yk+PRJfWGbLZdoUx1Y9BIoVNclE=

inflight@^1.0.4:
  version "1.0.6"
  resolved "http://r.npm.sankuai.com/inflight/download/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@2.0.4, inherits@^2.0.1, inherits@^2.0.3, inherits@^2.0.4, inherits@~2.0.3:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/inherits/download/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

inquirer@^3.0.6:
  version "3.3.0"
  resolved "http://r.npm.sankuai.com/inquirer/download/inquirer-3.3.0.tgz#9dd2f2ad765dcab1ff0443b491442a20ba227dc9"
  integrity sha1-ndLyrXZdyrH/BEO0kUQqILoifck=
  dependencies:
    ansi-escapes "^3.0.0"
    chalk "^2.0.0"
    cli-cursor "^2.1.0"
    cli-width "^2.0.0"
    external-editor "^2.0.4"
    figures "^2.0.0"
    lodash "^4.3.0"
    mute-stream "0.0.7"
    run-async "^2.2.0"
    rx-lite "^4.0.8"
    rx-lite-aggregates "^4.0.8"
    string-width "^2.1.0"
    strip-ansi "^4.0.0"
    through "^2.3.6"

inquirer@^7.3.3:
  version "7.3.3"
  resolved "http://r.npm.sankuai.com/inquirer/download/inquirer-7.3.3.tgz#04d176b2af04afc157a83fd7c100e98ee0aad003"
  integrity sha1-BNF2sq8Er8FXqD/XwQDpjuCq0AM=
  dependencies:
    ansi-escapes "^4.2.1"
    chalk "^4.1.0"
    cli-cursor "^3.1.0"
    cli-width "^3.0.0"
    external-editor "^3.0.3"
    figures "^3.0.0"
    lodash "^4.17.19"
    mute-stream "0.0.8"
    run-async "^2.4.0"
    rxjs "^6.6.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"
    through "^2.3.6"

inquirer@^8.2.0:
  version "8.2.6"
  resolved "http://r.npm.sankuai.com/inquirer/download/inquirer-8.2.6.tgz#733b74888195d8d400a67ac332011b5fae5ea562"
  integrity sha1-czt0iIGV2NQApnrDMgEbX65epWI=
  dependencies:
    ansi-escapes "^4.2.1"
    chalk "^4.1.1"
    cli-cursor "^3.1.0"
    cli-width "^3.0.0"
    external-editor "^3.0.3"
    figures "^3.0.0"
    lodash "^4.17.21"
    mute-stream "0.0.8"
    ora "^5.4.1"
    run-async "^2.4.0"
    rxjs "^7.5.5"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"
    through "^2.3.6"
    wrap-ansi "^6.0.1"

internal-slot@^1.0.3, internal-slot@^1.0.5:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/internal-slot/download/internal-slot-1.0.5.tgz#f2a2ee21f668f8627a4667f309dc0f4fb6674986"
  integrity sha1-8qLuIfZo+GJ6RmfzCdwPT7ZnSYY=
  dependencies:
    get-intrinsic "^1.2.0"
    has "^1.0.3"
    side-channel "^1.0.4"

internal-slot@^1.0.7:
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/internal-slot/download/internal-slot-1.0.7.tgz#c06dcca3ed874249881007b0a5523b172a190802"
  integrity sha1-wG3Mo+2HQkmIEAewpVI7FyoZCAI=
  dependencies:
    es-errors "^1.3.0"
    hasown "^2.0.0"
    side-channel "^1.0.4"

intersection-observer@^0.12.0:
  version "0.12.2"
  resolved "http://r.npm.sankuai.com/intersection-observer/download/intersection-observer-0.12.2.tgz#4a45349cc0cd91916682b1f44c28d7ec737dc375"
  integrity sha1-SkU0nMDNkZFmgrH0TCjX7HN9w3U=

into-stream@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/into-stream/download/into-stream-3.1.0.tgz#96fb0a936c12babd6ff1752a17d05616abd094c6"
  integrity sha1-lvsKk2wSur1v8XUqF9BWFqvQlMY=
  dependencies:
    from2 "^2.1.1"
    p-is-promise "^1.1.0"

invariant@2.2.4, invariant@^2.1.0, invariant@^2.2.4:
  version "2.2.4"
  resolved "http://r.npm.sankuai.com/invariant/download/invariant-2.2.4.tgz#610f3c92c9359ce1db616e538008d23ff35158e6"
  integrity sha1-YQ88ksk1nOHbYW5TgAjSP/NRWOY=
  dependencies:
    loose-envify "^1.0.0"

invert-kv@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/invert-kv/download/invert-kv-1.0.0.tgz#104a8e4aaca6d3d8cd157a8ef8bfab2d7a3ffdb6"
  integrity sha1-EEqOSqym09jNFXqO+L+rLXo//bY=

ip@^1.1.5:
  version "1.1.8"
  resolved "http://r.npm.sankuai.com/ip/download/ip-1.1.8.tgz#ae05948f6b075435ed3307acce04629da8cdbf48"
  integrity sha1-rgWUj2sHVDXtMweszgRinajNv0g=

is-accessor-descriptor@^0.1.6:
  version "0.1.6"
  resolved "http://r.npm.sankuai.com/is-accessor-descriptor/download/is-accessor-descriptor-0.1.6.tgz#a9e12cb3ae8d876727eeef3843f8a0897b5c98d6"
  integrity sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=
  dependencies:
    kind-of "^3.0.2"

is-accessor-descriptor@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz#169c2f6d3df1f992618072365c9b0ea1f6878656"
  integrity sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=
  dependencies:
    kind-of "^6.0.0"

is-array-buffer@^3.0.1, is-array-buffer@^3.0.2:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/is-array-buffer/download/is-array-buffer-3.0.2.tgz#f2653ced8412081638ecb0ebbd0c41c6e0aecbbe"
  integrity sha1-8mU87YQSCBY47LDrvQxBxuCuy74=
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.2.0"
    is-typed-array "^1.1.10"

is-array-buffer@^3.0.4:
  version "3.0.4"
  resolved "http://r.npm.sankuai.com/is-array-buffer/download/is-array-buffer-3.0.4.tgz#7a1f92b3d61edd2bc65d24f130530ea93d7fae98"
  integrity sha1-eh+Ss9Ye3SvGXSTxMFMOqT1/rpg=
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.2.1"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "http://r.npm.sankuai.com/is-arrayish/download/is-arrayish-0.2.1.tgz#77c99840527aa8ecb1a8ba697b80645a7a926a9d"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-bigint@^1.0.1:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/is-bigint/download/is-bigint-1.0.4.tgz#08147a1875bc2b32005d41ccd8291dffc6691df3"
  integrity sha1-CBR6GHW8KzIAXUHM2Ckd/8ZpHfM=
  dependencies:
    has-bigints "^1.0.1"

is-boolean-object@^1.1.0:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/is-boolean-object/download/is-boolean-object-1.1.2.tgz#5c6dc200246dd9321ae4b885a114bb1f75f63719"
  integrity sha1-XG3CACRt2TIa5LiFoRS7H3X2Nxk=
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-buffer@^1.1.5:
  version "1.1.6"
  resolved "http://r.npm.sankuai.com/is-buffer/download/is-buffer-1.1.6.tgz#efaa2ea9daa0d7ab2ea13a97b2b8ad51fefbe8be"
  integrity sha1-76ouqdqg16suoTqXsritUf776L4=

is-callable@^1.1.3, is-callable@^1.1.4, is-callable@^1.2.7:
  version "1.2.7"
  resolved "http://r.npm.sankuai.com/is-callable/download/is-callable-1.2.7.tgz#3bc2a85ea742d9e36205dcacdd72ca1fdc51b055"
  integrity sha1-O8KoXqdC2eNiBdys3XLKH9xRsFU=

is-ci@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/is-ci/download/is-ci-2.0.0.tgz#6bc6334181810e04b5c22b3d589fdca55026404c"
  integrity sha1-a8YzQYGBDgS1wis9WJ/cpVAmQEw=
  dependencies:
    ci-info "^2.0.0"

is-core-module@^2.11.0, is-core-module@^2.9.0:
  version "2.11.0"
  resolved "http://r.npm.sankuai.com/is-core-module/download/is-core-module-2.11.0.tgz#ad4cb3e3863e814523c96f3f58d26cc570ff0144"
  integrity sha1-rUyz44Y+gUUjyW8/WNJsxXD/AUQ=
  dependencies:
    has "^1.0.3"

is-core-module@^2.13.0:
  version "2.13.1"
  resolved "http://r.npm.sankuai.com/is-core-module/download/is-core-module-2.13.1.tgz#ad0d7532c6fea9da1ebdc82742d74525c6273384"
  integrity sha1-rQ11Msb+qdoevcgnQtdFJcYnM4Q=
  dependencies:
    hasown "^2.0.0"

is-core-module@^2.13.1:
  version "2.15.0"
  resolved "http://r.npm.sankuai.com/is-core-module/download/is-core-module-2.15.0.tgz#71c72ec5442ace7e76b306e9d48db361f22699ea"
  integrity sha1-cccuxUQqzn52swbp1I2zYfImmeo=
  dependencies:
    hasown "^2.0.2"

is-data-descriptor@^0.1.4:
  version "0.1.4"
  resolved "http://r.npm.sankuai.com/is-data-descriptor/download/is-data-descriptor-0.1.4.tgz#0b5ee648388e2c860282e793f1856fec3f301b56"
  integrity sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=
  dependencies:
    kind-of "^3.0.2"

is-data-descriptor@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz#d84876321d0e7add03990406abbbbd36ba9268c7"
  integrity sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=
  dependencies:
    kind-of "^6.0.0"

is-data-view@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/is-data-view/download/is-data-view-1.0.1.tgz#4b4d3a511b70f3dc26d42c03ca9ca515d847759f"
  integrity sha1-S006URtw89wm1CwDypylFdhHdZ8=
  dependencies:
    is-typed-array "^1.1.13"

is-date-object@^1.0.1:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/is-date-object/download/is-date-object-1.0.5.tgz#0841d5536e724c25597bf6ea62e1bd38298df31f"
  integrity sha1-CEHVU25yTCVZe/bqYuG9OCmN8x8=
  dependencies:
    has-tostringtag "^1.0.0"

is-descriptor@^0.1.0:
  version "0.1.6"
  resolved "http://r.npm.sankuai.com/is-descriptor/download/is-descriptor-0.1.6.tgz#366d8240dde487ca51823b1ab9f07a10a78251ca"
  integrity sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco=
  dependencies:
    is-accessor-descriptor "^0.1.6"
    is-data-descriptor "^0.1.4"
    kind-of "^5.0.0"

is-descriptor@^1.0.0, is-descriptor@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/is-descriptor/download/is-descriptor-1.0.2.tgz#3b159746a66604b04f8c81524ba365c5f14d86ec"
  integrity sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=
  dependencies:
    is-accessor-descriptor "^1.0.0"
    is-data-descriptor "^1.0.0"
    kind-of "^6.0.2"

is-directory@^0.3.1:
  version "0.3.1"
  resolved "http://r.npm.sankuai.com/is-directory/download/is-directory-0.3.1.tgz#61339b6f2475fc772fd9c9d83f5c8575dc154ae1"
  integrity sha1-YTObbyR1/Hcv2cnYP1yFddwVSuE=

is-docker@^2.0.0:
  version "2.2.1"
  resolved "http://r.npm.sankuai.com/is-docker/download/is-docker-2.2.1.tgz#33eeabe23cfe86f14bde4408a02c0cfb853acdaa"
  integrity sha1-M+6r4jz+hvFL3kQIoCwM+4U6zao=

is-extendable@^0.1.0, is-extendable@^0.1.1:
  version "0.1.1"
  resolved "http://r.npm.sankuai.com/is-extendable/download/is-extendable-0.1.1.tgz#62b110e289a471418e3ec36a617d472e301dfc89"
  integrity sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=

is-extendable@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/is-extendable/download/is-extendable-1.0.1.tgz#a7470f9e426733d81bd81e1155264e3a3507cab4"
  integrity sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=
  dependencies:
    is-plain-object "^2.0.4"

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/is-extglob/download/is-extglob-2.1.1.tgz#a88c02535791f02ed37c76a1b9ea9773c833f8c2"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-fullwidth-code-point@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/is-fullwidth-code-point/download/is-fullwidth-code-point-1.0.0.tgz#ef9e31386f031a7f0d643af82fde50c457ef00cb"
  integrity sha1-754xOG8DGn8NZDr4L95QxFfvAMs=
  dependencies:
    number-is-nan "^1.0.0"

is-fullwidth-code-point@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz#a3b30a5c4f199183167aaab93beefae3ddfb654f"
  integrity sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz#f116f8064fe90b3f7844a38997c0b75051269f1d"
  integrity sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=

is-fullwidth-code-point@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/is-fullwidth-code-point/download/is-fullwidth-code-point-4.0.0.tgz#fae3167c729e7463f8461ce512b080a49268aa88"
  integrity sha1-+uMWfHKedGP4RhzlErCApJJoqog=

is-generator-fn@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/is-generator-fn/download/is-generator-fn-2.1.0.tgz#7d140adc389aaf3011a8f2a2a4cfa6faadffb118"
  integrity sha1-fRQK3DiarzARqPKipM+m+q3/sRg=

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3:
  version "4.0.3"
  resolved "http://r.npm.sankuai.com/is-glob/download/is-glob-4.0.3.tgz#64f61e42cbbb2eec2071a9dac0b28ba1e65d5084"
  integrity sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=
  dependencies:
    is-extglob "^2.1.1"

is-interactive@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/is-interactive/download/is-interactive-1.0.0.tgz#cea6e6ae5c870a7b0a0004070b7b587e0252912e"
  integrity sha1-zqbmrlyHCnsKAAQHC3tYfgJSkS4=

is-natural-number@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/is-natural-number/download/is-natural-number-4.0.1.tgz#ab9d76e1db4ced51e35de0c72ebecf09f734cde8"
  integrity sha1-q5124dtM7VHjXeDHLr7PCfc0zeg=

is-negative-zero@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/is-negative-zero/download/is-negative-zero-2.0.2.tgz#7bf6f03a28003b8b3965de3ac26f664d765f3150"
  integrity sha1-e/bwOigAO4s5Zd46wm9mTXZfMVA=

is-negative-zero@^2.0.3:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/is-negative-zero/download/is-negative-zero-2.0.3.tgz#ced903a027aca6381b777a5743069d7376a49747"
  integrity sha1-ztkDoCespjgbd3pXQwadc3akl0c=

is-number-object@^1.0.4:
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/is-number-object/download/is-number-object-1.0.7.tgz#59d50ada4c45251784e9904f5246c742f07a42fc"
  integrity sha1-WdUK2kxFJReE6ZBPUkbHQvB6Qvw=
  dependencies:
    has-tostringtag "^1.0.0"

is-number@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/is-number/download/is-number-3.0.0.tgz#24fd6201a4782cf50561c810276afc7d12d71195"
  integrity sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=
  dependencies:
    kind-of "^3.0.2"

is-number@^7.0.0:
  version "7.0.0"
  resolved "http://r.npm.sankuai.com/is-number/download/is-number-7.0.0.tgz#7535345b896734d5f80c4d06c50955527a14f12b"
  integrity sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=

is-object@^1.0.1:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/is-object/download/is-object-1.0.2.tgz#a56552e1c665c9e950b4a025461da87e72f86fcf"
  integrity sha1-pWVS4cZlyelQtKAlRh2ofnL4b88=

is-plain-obj@^1.0.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/is-plain-obj/download/is-plain-obj-1.1.0.tgz#71a50c8429dfca773c92a390a4a03b39fcd51d3e"
  integrity sha1-caUMhCnfync8kqOQpKA7OfzVHT4=

is-plain-object@^2.0.3, is-plain-object@^2.0.4:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/is-plain-object/download/is-plain-object-2.0.4.tgz#2c163b3fafb1b606d9d17928f05c2a1c38e07677"
  integrity sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=
  dependencies:
    isobject "^3.0.1"

is-potential-custom-element-name@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/is-potential-custom-element-name/download/is-potential-custom-element-name-1.0.1.tgz#171ed6f19e3ac554394edf78caa05784a45bebb5"
  integrity sha1-Fx7W8Z46xVQ5Tt94yqBXhKRb67U=

is-regex@^1.1.4:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/is-regex/download/is-regex-1.1.4.tgz#eef5663cd59fa4c0ae339505323df6854bb15958"
  integrity sha1-7vVmPNWfpMCuM5UFMj32hUuxWVg=
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-retry-allowed@^1.1.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/is-retry-allowed/download/is-retry-allowed-1.2.0.tgz#d778488bd0a4666a3be8a1482b9f2baafedea8b4"
  integrity sha1-13hIi9CkZmo76KFIK58rqv7eqLQ=

is-shared-array-buffer@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/is-shared-array-buffer/download/is-shared-array-buffer-1.0.2.tgz#8f259c573b60b6a32d4058a1a07430c0a7344c79"
  integrity sha1-jyWcVztgtqMtQFihoHQwwKc0THk=
  dependencies:
    call-bind "^1.0.2"

is-shared-array-buffer@^1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/is-shared-array-buffer/download/is-shared-array-buffer-1.0.3.tgz#1237f1cba059cdb62431d378dcc37d9680181688"
  integrity sha1-Ejfxy6BZzbYkMdN43MN9loAYFog=
  dependencies:
    call-bind "^1.0.7"

is-stream@^1.0.1, is-stream@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/is-stream/download/is-stream-1.1.0.tgz#12d4a3dd4e68e0b79ceb8dbc84173ae80d91ca44"
  integrity sha1-EtSj3U5o4Lec6428hBc66A2RykQ=

is-stream@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/is-stream/download/is-stream-2.0.1.tgz#fac1e3d53b97ad5a9d0ae9cef2389f5810a5c077"
  integrity sha1-+sHj1TuXrVqdCunO8jifWBClwHc=

is-string@^1.0.5, is-string@^1.0.7:
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/is-string/download/is-string-1.0.7.tgz#0dd12bf2006f255bb58f695110eff7491eebc0fd"
  integrity sha1-DdEr8gBvJVu1j2lREO/3SR7rwP0=
  dependencies:
    has-tostringtag "^1.0.0"

is-symbol@^1.0.2, is-symbol@^1.0.3:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/is-symbol/download/is-symbol-1.0.4.tgz#a6dac93b635b063ca6872236de88910a57af139c"
  integrity sha1-ptrJO2NbBjymhyI23oiRClevE5w=
  dependencies:
    has-symbols "^1.0.2"

is-typed-array@^1.1.10, is-typed-array@^1.1.9:
  version "1.1.10"
  resolved "http://r.npm.sankuai.com/is-typed-array/download/is-typed-array-1.1.10.tgz#36a5b5cb4189b575d1a3e4b08536bfb485801e3f"
  integrity sha1-NqW1y0GJtXXRo+SwhTa/tIWAHj8=
  dependencies:
    available-typed-arrays "^1.0.5"
    call-bind "^1.0.2"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-tostringtag "^1.0.0"

is-typed-array@^1.1.13:
  version "1.1.13"
  resolved "http://r.npm.sankuai.com/is-typed-array/download/is-typed-array-1.1.13.tgz#d6c5ca56df62334959322d7d7dd1cca50debe229"
  integrity sha1-1sXKVt9iM0lZMi19fdHMpQ3r4ik=
  dependencies:
    which-typed-array "^1.1.14"

is-typedarray@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/is-typedarray/download/is-typedarray-1.0.0.tgz#e479c80858df0c1b11ddda6940f96011fcda4a9a"
  integrity sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=

is-unicode-supported@^0.1.0:
  version "0.1.0"
  resolved "http://r.npm.sankuai.com/is-unicode-supported/download/is-unicode-supported-0.1.0.tgz#3f26c76a809593b52bfa2ecb5710ed2779b522a7"
  integrity sha1-PybHaoCVk7Ur+i7LVxDtJ3m1Iqc=

is-weakref@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/is-weakref/download/is-weakref-1.0.2.tgz#9529f383a9338205e89765e0392efc2f100f06f2"
  integrity sha1-lSnzg6kzggXol2XgOS78LxAPBvI=
  dependencies:
    call-bind "^1.0.2"

is-windows@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/is-windows/download/is-windows-1.0.2.tgz#d1850eb9791ecd18e6182ce12a30f396634bb19d"
  integrity sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0=

is-wsl@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/is-wsl/download/is-wsl-1.1.0.tgz#1f16e4aa22b04d1336b66188a66af3c600c3a66d"
  integrity sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0=

is-wsl@^2.2.0:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/is-wsl/download/is-wsl-2.2.0.tgz#74a4c76e77ca9fd3f932f290c17ea326cd157271"
  integrity sha1-dKTHbnfKn9P5MvKQwX6jJs0VcnE=
  dependencies:
    is-docker "^2.0.0"

isarray@1.0.0, isarray@~1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/isarray/download/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isarray@^2.0.5:
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/isarray/download/isarray-2.0.5.tgz#8af1e4c1221244cc62459faf38940d4e644a5723"
  integrity sha1-ivHkwSISRMxiRZ+vOJQNTmRKVyM=

isexe@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/isexe/download/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

isobject@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/isobject/download/isobject-2.1.0.tgz#f065561096a3f1da2ef46272f815c840d87e0c89"
  integrity sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=
  dependencies:
    isarray "1.0.0"

isobject@^3.0.0, isobject@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/isobject/download/isobject-3.0.1.tgz#4e431e92b11a9731636aa1f9c8d1ccbcfdab78df"
  integrity sha1-TkMekrEalzFjaqH5yNHMvP2reN8=

isomorphic-fetch@^2.1.1:
  version "2.2.1"
  resolved "http://r.npm.sankuai.com/isomorphic-fetch/download/isomorphic-fetch-2.2.1.tgz#611ae1acf14f5e81f729507472819fe9733558a9"
  integrity sha1-YRrhrPFPXoH3KVB0coGf6XM1WKk=
  dependencies:
    node-fetch "^1.0.1"
    whatwg-fetch ">=0.10.0"

istanbul-lib-coverage@^3.0.0, istanbul-lib-coverage@^3.2.0:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/istanbul-lib-coverage/download/istanbul-lib-coverage-3.2.0.tgz#189e7909d0a39fa5a3dfad5b03f71947770191d3"
  integrity sha1-GJ55CdCjn6Wj361bA/cZR3cBkdM=

istanbul-lib-instrument@^4.0.3:
  version "4.0.3"
  resolved "http://r.npm.sankuai.com/istanbul-lib-instrument/download/istanbul-lib-instrument-4.0.3.tgz#873c6fff897450118222774696a3f28902d77c1d"
  integrity sha1-hzxv/4l0UBGCIndGlqPyiQLXfB0=
  dependencies:
    "@babel/core" "^7.7.5"
    "@istanbuljs/schema" "^0.1.2"
    istanbul-lib-coverage "^3.0.0"
    semver "^6.3.0"

istanbul-lib-instrument@^5.0.4:
  version "5.2.1"
  resolved "http://r.npm.sankuai.com/istanbul-lib-instrument/download/istanbul-lib-instrument-5.2.1.tgz#d10c8885c2125574e1c231cacadf955675e1ce3d"
  integrity sha1-0QyIhcISVXThwjHKyt+VVnXhzj0=
  dependencies:
    "@babel/core" "^7.12.3"
    "@babel/parser" "^7.14.7"
    "@istanbuljs/schema" "^0.1.2"
    istanbul-lib-coverage "^3.2.0"
    semver "^6.3.0"

istanbul-lib-report@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/istanbul-lib-report/download/istanbul-lib-report-3.0.0.tgz#7518fe52ea44de372f460a76b5ecda9ffb73d8a6"
  integrity sha1-dRj+UupE3jcvRgp2tezan/tz2KY=
  dependencies:
    istanbul-lib-coverage "^3.0.0"
    make-dir "^3.0.0"
    supports-color "^7.1.0"

istanbul-lib-source-maps@^4.0.0:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/istanbul-lib-source-maps/download/istanbul-lib-source-maps-4.0.1.tgz#895f3a709fcfba34c6de5a42939022f3e4358551"
  integrity sha1-iV86cJ/PujTG3lpCk5Ai8+Q1hVE=
  dependencies:
    debug "^4.1.1"
    istanbul-lib-coverage "^3.0.0"
    source-map "^0.6.1"

istanbul-reports@^3.0.2:
  version "3.1.5"
  resolved "http://r.npm.sankuai.com/istanbul-reports/download/istanbul-reports-3.1.5.tgz#cc9a6ab25cb25659810e4785ed9d9fb742578bae"
  integrity sha1-zJpqslyyVlmBDkeF7Z2ft0JXi64=
  dependencies:
    html-escaper "^2.0.0"
    istanbul-lib-report "^3.0.0"

isurl@^1.0.0-alpha5:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/isurl/download/isurl-1.0.0.tgz#b27f4f49f3cdaa3ea44a0a5b7f3462e6edc39d67"
  integrity sha1-sn9PSfPNqj6kSgpbfzRi5u3DnWc=
  dependencies:
    has-to-string-tag-x "^1.2.0"
    is-object "^1.0.1"

jake@^10.6.1, jake@^10.8.5:
  version "10.8.7"
  resolved "http://r.npm.sankuai.com/jake/download/jake-10.8.7.tgz#63a32821177940c33f356e0ba44ff9d34e1c7d8f"
  integrity sha1-Y6MoIRd5QMM/NW4LpE/5004cfY8=
  dependencies:
    async "^3.2.3"
    chalk "^4.0.2"
    filelist "^1.0.4"
    minimatch "^3.1.2"

jest-changed-files@^26.6.2:
  version "26.6.2"
  resolved "http://r.npm.sankuai.com/jest-changed-files/download/jest-changed-files-26.6.2.tgz#f6198479e1cc66f22f9ae1e22acaa0b429c042d0"
  integrity sha1-9hmEeeHMZvIvmuHiKsqgtCnAQtA=
  dependencies:
    "@jest/types" "^26.6.2"
    execa "^4.0.0"
    throat "^5.0.0"

jest-cli@^26.6.3:
  version "26.6.3"
  resolved "http://r.npm.sankuai.com/jest-cli/download/jest-cli-26.6.3.tgz#43117cfef24bc4cd691a174a8796a532e135e92a"
  integrity sha1-QxF8/vJLxM1pGhdKh5alMuE16So=
  dependencies:
    "@jest/core" "^26.6.3"
    "@jest/test-result" "^26.6.2"
    "@jest/types" "^26.6.2"
    chalk "^4.0.0"
    exit "^0.1.2"
    graceful-fs "^4.2.4"
    import-local "^3.0.2"
    is-ci "^2.0.0"
    jest-config "^26.6.3"
    jest-util "^26.6.2"
    jest-validate "^26.6.2"
    prompts "^2.0.1"
    yargs "^15.4.1"

jest-config@^26.6.3:
  version "26.6.3"
  resolved "http://r.npm.sankuai.com/jest-config/download/jest-config-26.6.3.tgz#64f41444eef9eb03dc51d5c53b75c8c71f645349"
  integrity sha1-ZPQURO756wPcUdXFO3XIxx9kU0k=
  dependencies:
    "@babel/core" "^7.1.0"
    "@jest/test-sequencer" "^26.6.3"
    "@jest/types" "^26.6.2"
    babel-jest "^26.6.3"
    chalk "^4.0.0"
    deepmerge "^4.2.2"
    glob "^7.1.1"
    graceful-fs "^4.2.4"
    jest-environment-jsdom "^26.6.2"
    jest-environment-node "^26.6.2"
    jest-get-type "^26.3.0"
    jest-jasmine2 "^26.6.3"
    jest-regex-util "^26.0.0"
    jest-resolve "^26.6.2"
    jest-util "^26.6.2"
    jest-validate "^26.6.2"
    micromatch "^4.0.2"
    pretty-format "^26.6.2"

jest-diff@^26.0.0, jest-diff@^26.6.2:
  version "26.6.2"
  resolved "http://r.npm.sankuai.com/jest-diff/download/jest-diff-26.6.2.tgz#1aa7468b52c3a68d7d5c5fdcdfcd5e49bd164394"
  integrity sha1-GqdGi1LDpo19XF/c381eSb0WQ5Q=
  dependencies:
    chalk "^4.0.0"
    diff-sequences "^26.6.2"
    jest-get-type "^26.3.0"
    pretty-format "^26.6.2"

jest-diff@^29.7.0:
  version "29.7.0"
  resolved "http://r.npm.sankuai.com/jest-diff/download/jest-diff-29.7.0.tgz#017934a66ebb7ecf6f205e84699be10afd70458a"
  integrity sha1-AXk0pm67fs9vIF6EaZvhCv1wRYo=
  dependencies:
    chalk "^4.0.0"
    diff-sequences "^29.6.3"
    jest-get-type "^29.6.3"
    pretty-format "^29.7.0"

jest-docblock@^26.0.0:
  version "26.0.0"
  resolved "http://r.npm.sankuai.com/jest-docblock/download/jest-docblock-26.0.0.tgz#3e2fa20899fc928cb13bd0ff68bd3711a36889b5"
  integrity sha1-Pi+iCJn8koyxO9D/aL03EaNoibU=
  dependencies:
    detect-newline "^3.0.0"

jest-each@^26.6.2:
  version "26.6.2"
  resolved "http://r.npm.sankuai.com/jest-each/download/jest-each-26.6.2.tgz#02526438a77a67401c8a6382dfe5999952c167cb"
  integrity sha1-AlJkOKd6Z0AcimOC3+WZmVLBZ8s=
  dependencies:
    "@jest/types" "^26.6.2"
    chalk "^4.0.0"
    jest-get-type "^26.3.0"
    jest-util "^26.6.2"
    pretty-format "^26.6.2"

jest-environment-jsdom@^26.6.2:
  version "26.6.2"
  resolved "http://r.npm.sankuai.com/jest-environment-jsdom/download/jest-environment-jsdom-26.6.2.tgz#78d09fe9cf019a357009b9b7e1f101d23bd1da3e"
  integrity sha1-eNCf6c8BmjVwCbm34fEB0jvR2j4=
  dependencies:
    "@jest/environment" "^26.6.2"
    "@jest/fake-timers" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    jest-mock "^26.6.2"
    jest-util "^26.6.2"
    jsdom "^16.4.0"

jest-environment-node@^26.6.2:
  version "26.6.2"
  resolved "http://r.npm.sankuai.com/jest-environment-node/download/jest-environment-node-26.6.2.tgz#824e4c7fb4944646356f11ac75b229b0035f2b0c"
  integrity sha1-gk5Mf7SURkY1bxGsdbIpsANfKww=
  dependencies:
    "@jest/environment" "^26.6.2"
    "@jest/fake-timers" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    jest-mock "^26.6.2"
    jest-util "^26.6.2"

jest-get-type@^24.9.0:
  version "24.9.0"
  resolved "http://r.npm.sankuai.com/jest-get-type/download/jest-get-type-24.9.0.tgz#1684a0c8a50f2e4901b6644ae861f579eed2ef0e"
  integrity sha1-FoSgyKUPLkkBtmRK6GH1ee7S7w4=

jest-get-type@^26.3.0:
  version "26.3.0"
  resolved "http://r.npm.sankuai.com/jest-get-type/download/jest-get-type-26.3.0.tgz#e97dc3c3f53c2b406ca7afaed4493b1d099199e0"
  integrity sha1-6X3Dw/U8K0Bsp6+u1Ek7HQmRmeA=

jest-get-type@^29.6.3:
  version "29.6.3"
  resolved "http://r.npm.sankuai.com/jest-get-type/download/jest-get-type-29.6.3.tgz#36f499fdcea197c1045a127319c0481723908fd1"
  integrity sha1-NvSZ/c6hl8EEWhJzGcBIFyOQj9E=

jest-haste-map@^24.9.0:
  version "24.9.0"
  resolved "http://r.npm.sankuai.com/jest-haste-map/download/jest-haste-map-24.9.0.tgz#b38a5d64274934e21fa417ae9a9fbeb77ceaac7d"
  integrity sha1-s4pdZCdJNOIfpBeump++t3zqrH0=
  dependencies:
    "@jest/types" "^24.9.0"
    anymatch "^2.0.0"
    fb-watchman "^2.0.0"
    graceful-fs "^4.1.15"
    invariant "^2.2.4"
    jest-serializer "^24.9.0"
    jest-util "^24.9.0"
    jest-worker "^24.9.0"
    micromatch "^3.1.10"
    sane "^4.0.3"
    walker "^1.0.7"
  optionalDependencies:
    fsevents "^1.2.7"

jest-haste-map@^26.6.2:
  version "26.6.2"
  resolved "http://r.npm.sankuai.com/jest-haste-map/download/jest-haste-map-26.6.2.tgz#dd7e60fe7dc0e9f911a23d79c5ff7fb5c2cafeaa"
  integrity sha1-3X5g/n3A6fkRoj15xf9/tcLK/qo=
  dependencies:
    "@jest/types" "^26.6.2"
    "@types/graceful-fs" "^4.1.2"
    "@types/node" "*"
    anymatch "^3.0.3"
    fb-watchman "^2.0.0"
    graceful-fs "^4.2.4"
    jest-regex-util "^26.0.0"
    jest-serializer "^26.6.2"
    jest-util "^26.6.2"
    jest-worker "^26.6.2"
    micromatch "^4.0.2"
    sane "^4.0.3"
    walker "^1.0.7"
  optionalDependencies:
    fsevents "^2.1.2"

jest-jasmine2@^26.6.3:
  version "26.6.3"
  resolved "http://r.npm.sankuai.com/jest-jasmine2/download/jest-jasmine2-26.6.3.tgz#adc3cf915deacb5212c93b9f3547cd12958f2edd"
  integrity sha1-rcPPkV3qy1ISyTufNUfNEpWPLt0=
  dependencies:
    "@babel/traverse" "^7.1.0"
    "@jest/environment" "^26.6.2"
    "@jest/source-map" "^26.6.2"
    "@jest/test-result" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    chalk "^4.0.0"
    co "^4.6.0"
    expect "^26.6.2"
    is-generator-fn "^2.0.0"
    jest-each "^26.6.2"
    jest-matcher-utils "^26.6.2"
    jest-message-util "^26.6.2"
    jest-runtime "^26.6.3"
    jest-snapshot "^26.6.2"
    jest-util "^26.6.2"
    pretty-format "^26.6.2"
    throat "^5.0.0"

jest-leak-detector@^26.6.2:
  version "26.6.2"
  resolved "http://r.npm.sankuai.com/jest-leak-detector/download/jest-leak-detector-26.6.2.tgz#7717cf118b92238f2eba65054c8a0c9c653a91af"
  integrity sha1-dxfPEYuSI48uumUFTIoMnGU6ka8=
  dependencies:
    jest-get-type "^26.3.0"
    pretty-format "^26.6.2"

jest-matcher-utils@^26.6.2:
  version "26.6.2"
  resolved "http://r.npm.sankuai.com/jest-matcher-utils/download/jest-matcher-utils-26.6.2.tgz#8e6fd6e863c8b2d31ac6472eeb237bc595e53e7a"
  integrity sha1-jm/W6GPIstMaxkcu6yN7xZXlPno=
  dependencies:
    chalk "^4.0.0"
    jest-diff "^26.6.2"
    jest-get-type "^26.3.0"
    pretty-format "^26.6.2"

jest-matcher-utils@^29.7.0:
  version "29.7.0"
  resolved "http://r.npm.sankuai.com/jest-matcher-utils/download/jest-matcher-utils-29.7.0.tgz#ae8fec79ff249fd592ce80e3ee474e83a6c44f12"
  integrity sha1-ro/sef8kn9WSzoDj7kdOg6bETxI=
  dependencies:
    chalk "^4.0.0"
    jest-diff "^29.7.0"
    jest-get-type "^29.6.3"
    pretty-format "^29.7.0"

jest-message-util@^24.9.0:
  version "24.9.0"
  resolved "http://r.npm.sankuai.com/jest-message-util/download/jest-message-util-24.9.0.tgz#527f54a1e380f5e202a8d1149b0ec872f43119e3"
  integrity sha1-Un9UoeOA9eICqNEUmw7IcvQxGeM=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@jest/test-result" "^24.9.0"
    "@jest/types" "^24.9.0"
    "@types/stack-utils" "^1.0.1"
    chalk "^2.0.1"
    micromatch "^3.1.10"
    slash "^2.0.0"
    stack-utils "^1.0.1"

jest-message-util@^26.6.2:
  version "26.6.2"
  resolved "http://r.npm.sankuai.com/jest-message-util/download/jest-message-util-26.6.2.tgz#58173744ad6fc0506b5d21150b9be56ef001ca07"
  integrity sha1-WBc3RK1vwFBrXSEVC5vlbvABygc=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@jest/types" "^26.6.2"
    "@types/stack-utils" "^2.0.0"
    chalk "^4.0.0"
    graceful-fs "^4.2.4"
    micromatch "^4.0.2"
    pretty-format "^26.6.2"
    slash "^3.0.0"
    stack-utils "^2.0.2"

jest-mock@^24.9.0:
  version "24.9.0"
  resolved "http://r.npm.sankuai.com/jest-mock/download/jest-mock-24.9.0.tgz#c22835541ee379b908673ad51087a2185c13f1c6"
  integrity sha1-wig1VB7jebkIZzrVEIeiGFwT8cY=
  dependencies:
    "@jest/types" "^24.9.0"

jest-mock@^26.6.2:
  version "26.6.2"
  resolved "http://r.npm.sankuai.com/jest-mock/download/jest-mock-26.6.2.tgz#d6cb712b041ed47fe0d9b6fc3474bc6543feb302"
  integrity sha1-1stxKwQe1H/g2bb8NHS8ZUP+swI=
  dependencies:
    "@jest/types" "^26.6.2"
    "@types/node" "*"

jest-pnp-resolver@^1.2.2:
  version "1.2.3"
  resolved "http://r.npm.sankuai.com/jest-pnp-resolver/download/jest-pnp-resolver-1.2.3.tgz#930b1546164d4ad5937d5540e711d4d38d4cad2e"
  integrity sha1-kwsVRhZNStWTfVVA5xHU041MrS4=

jest-regex-util@^26.0.0:
  version "26.0.0"
  resolved "http://r.npm.sankuai.com/jest-regex-util/download/jest-regex-util-26.0.0.tgz#d25e7184b36e39fd466c3bc41be0971e821fee28"
  integrity sha1-0l5xhLNuOf1GbDvEG+CXHoIf7ig=

jest-resolve-dependencies@^26.6.3:
  version "26.6.3"
  resolved "http://r.npm.sankuai.com/jest-resolve-dependencies/download/jest-resolve-dependencies-26.6.3.tgz#6680859ee5d22ee5dcd961fe4871f59f4c784fb6"
  integrity sha1-ZoCFnuXSLuXc2WH+SHH1n0x4T7Y=
  dependencies:
    "@jest/types" "^26.6.2"
    jest-regex-util "^26.0.0"
    jest-snapshot "^26.6.2"

jest-resolve@^26.6.2:
  version "26.6.2"
  resolved "http://r.npm.sankuai.com/jest-resolve/download/jest-resolve-26.6.2.tgz#a3ab1517217f469b504f1b56603c5bb541fbb507"
  integrity sha1-o6sVFyF/RptQTxtWYDxbtUH7tQc=
  dependencies:
    "@jest/types" "^26.6.2"
    chalk "^4.0.0"
    graceful-fs "^4.2.4"
    jest-pnp-resolver "^1.2.2"
    jest-util "^26.6.2"
    read-pkg-up "^7.0.1"
    resolve "^1.18.1"
    slash "^3.0.0"

jest-runner@^26.6.3:
  version "26.6.3"
  resolved "http://r.npm.sankuai.com/jest-runner/download/jest-runner-26.6.3.tgz#2d1fed3d46e10f233fd1dbd3bfaa3fe8924be159"
  integrity sha1-LR/tPUbhDyM/0dvTv6o/6JJL4Vk=
  dependencies:
    "@jest/console" "^26.6.2"
    "@jest/environment" "^26.6.2"
    "@jest/test-result" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    chalk "^4.0.0"
    emittery "^0.7.1"
    exit "^0.1.2"
    graceful-fs "^4.2.4"
    jest-config "^26.6.3"
    jest-docblock "^26.0.0"
    jest-haste-map "^26.6.2"
    jest-leak-detector "^26.6.2"
    jest-message-util "^26.6.2"
    jest-resolve "^26.6.2"
    jest-runtime "^26.6.3"
    jest-util "^26.6.2"
    jest-worker "^26.6.2"
    source-map-support "^0.5.6"
    throat "^5.0.0"

jest-runtime@^26.6.3:
  version "26.6.3"
  resolved "http://r.npm.sankuai.com/jest-runtime/download/jest-runtime-26.6.3.tgz#4f64efbcfac398331b74b4b3c82d27d401b8fa2b"
  integrity sha1-T2TvvPrDmDMbdLSzyC0n1AG4+is=
  dependencies:
    "@jest/console" "^26.6.2"
    "@jest/environment" "^26.6.2"
    "@jest/fake-timers" "^26.6.2"
    "@jest/globals" "^26.6.2"
    "@jest/source-map" "^26.6.2"
    "@jest/test-result" "^26.6.2"
    "@jest/transform" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/yargs" "^15.0.0"
    chalk "^4.0.0"
    cjs-module-lexer "^0.6.0"
    collect-v8-coverage "^1.0.0"
    exit "^0.1.2"
    glob "^7.1.3"
    graceful-fs "^4.2.4"
    jest-config "^26.6.3"
    jest-haste-map "^26.6.2"
    jest-message-util "^26.6.2"
    jest-mock "^26.6.2"
    jest-regex-util "^26.0.0"
    jest-resolve "^26.6.2"
    jest-snapshot "^26.6.2"
    jest-util "^26.6.2"
    jest-validate "^26.6.2"
    slash "^3.0.0"
    strip-bom "^4.0.0"
    yargs "^15.4.1"

jest-serializer@^24.9.0:
  version "24.9.0"
  resolved "http://r.npm.sankuai.com/jest-serializer/download/jest-serializer-24.9.0.tgz#e6d7d7ef96d31e8b9079a714754c5d5c58288e73"
  integrity sha1-5tfX75bTHouQeacUdUxdXFgojnM=

jest-serializer@^26.6.2:
  version "26.6.2"
  resolved "http://r.npm.sankuai.com/jest-serializer/download/jest-serializer-26.6.2.tgz#d139aafd46957d3a448f3a6cdabe2919ba0742d1"
  integrity sha1-0Tmq/UaVfTpEjzps2r4pGboHQtE=
  dependencies:
    "@types/node" "*"
    graceful-fs "^4.2.4"

jest-snapshot@^26.6.2:
  version "26.6.2"
  resolved "http://r.npm.sankuai.com/jest-snapshot/download/jest-snapshot-26.6.2.tgz#f3b0af1acb223316850bd14e1beea9837fb39c84"
  integrity sha1-87CvGssiMxaFC9FOG+6pg3+znIQ=
  dependencies:
    "@babel/types" "^7.0.0"
    "@jest/types" "^26.6.2"
    "@types/babel__traverse" "^7.0.4"
    "@types/prettier" "^2.0.0"
    chalk "^4.0.0"
    expect "^26.6.2"
    graceful-fs "^4.2.4"
    jest-diff "^26.6.2"
    jest-get-type "^26.3.0"
    jest-haste-map "^26.6.2"
    jest-matcher-utils "^26.6.2"
    jest-message-util "^26.6.2"
    jest-resolve "^26.6.2"
    natural-compare "^1.4.0"
    pretty-format "^26.6.2"
    semver "^7.3.2"

jest-util@^24.9.0:
  version "24.9.0"
  resolved "http://r.npm.sankuai.com/jest-util/download/jest-util-24.9.0.tgz#7396814e48536d2e85a37de3e4c431d7cb140162"
  integrity sha1-c5aBTkhTbS6Fo33j5MQx18sUAWI=
  dependencies:
    "@jest/console" "^24.9.0"
    "@jest/fake-timers" "^24.9.0"
    "@jest/source-map" "^24.9.0"
    "@jest/test-result" "^24.9.0"
    "@jest/types" "^24.9.0"
    callsites "^3.0.0"
    chalk "^2.0.1"
    graceful-fs "^4.1.15"
    is-ci "^2.0.0"
    mkdirp "^0.5.1"
    slash "^2.0.0"
    source-map "^0.6.0"

jest-util@^26.1.0, jest-util@^26.6.2:
  version "26.6.2"
  resolved "http://r.npm.sankuai.com/jest-util/download/jest-util-26.6.2.tgz#907535dbe4d5a6cb4c47ac9b926f6af29576cbc1"
  integrity sha1-kHU12+TVpstMR6ybkm9q8pV2y8E=
  dependencies:
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    chalk "^4.0.0"
    graceful-fs "^4.2.4"
    is-ci "^2.0.0"
    micromatch "^4.0.2"

jest-validate@^24.9.0:
  version "24.9.0"
  resolved "http://r.npm.sankuai.com/jest-validate/download/jest-validate-24.9.0.tgz#0775c55360d173cd854e40180756d4ff52def8ab"
  integrity sha1-B3XFU2DRc82FTkAYB1bU/1Le+Ks=
  dependencies:
    "@jest/types" "^24.9.0"
    camelcase "^5.3.1"
    chalk "^2.0.1"
    jest-get-type "^24.9.0"
    leven "^3.1.0"
    pretty-format "^24.9.0"

jest-validate@^26.6.2:
  version "26.6.2"
  resolved "http://r.npm.sankuai.com/jest-validate/download/jest-validate-26.6.2.tgz#23d380971587150467342911c3d7b4ac57ab20ec"
  integrity sha1-I9OAlxWHFQRnNCkRw9e0rFerIOw=
  dependencies:
    "@jest/types" "^26.6.2"
    camelcase "^6.0.0"
    chalk "^4.0.0"
    jest-get-type "^26.3.0"
    leven "^3.1.0"
    pretty-format "^26.6.2"

jest-watcher@^26.6.2:
  version "26.6.2"
  resolved "http://r.npm.sankuai.com/jest-watcher/download/jest-watcher-26.6.2.tgz#a5b683b8f9d68dbcb1d7dae32172d2cca0592975"
  integrity sha1-pbaDuPnWjbyx19rjIXLSzKBZKXU=
  dependencies:
    "@jest/test-result" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    ansi-escapes "^4.2.1"
    chalk "^4.0.0"
    jest-util "^26.6.2"
    string-length "^4.0.1"

jest-worker@^24.9.0:
  version "24.9.0"
  resolved "http://r.npm.sankuai.com/jest-worker/download/jest-worker-24.9.0.tgz#5dbfdb5b2d322e98567898238a9697bcce67b3e5"
  integrity sha1-Xb/bWy0yLphWeJgjipaXvM5ns+U=
  dependencies:
    merge-stream "^2.0.0"
    supports-color "^6.1.0"

jest-worker@^26.6.2:
  version "26.6.2"
  resolved "http://r.npm.sankuai.com/jest-worker/download/jest-worker-26.6.2.tgz#7f72cbc4d643c365e27b9fd775f9d0eaa9c7a8ed"
  integrity sha1-f3LLxNZDw2Xie5/XdfnQ6qnHqO0=
  dependencies:
    "@types/node" "*"
    merge-stream "^2.0.0"
    supports-color "^7.0.0"

jest@^26.4.2:
  version "26.6.3"
  resolved "http://r.npm.sankuai.com/jest/download/jest-26.6.3.tgz#40e8fdbe48f00dfa1f0ce8121ca74b88ac9148ef"
  integrity sha1-QOj9vkjwDfofDOgSHKdLiKyRSO8=
  dependencies:
    "@jest/core" "^26.6.3"
    import-local "^3.0.2"
    jest-cli "^26.6.3"

jetifier@^1.6.2:
  version "1.6.8"
  resolved "http://r.npm.sankuai.com/jetifier/download/jetifier-1.6.8.tgz#e88068697875cbda98c32472902c4d3756247798"
  integrity sha1-6IBoaXh1y9qYwyRykCxNN1Ykd5g=

js-cookie@^2.x.x:
  version "2.2.1"
  resolved "http://r.npm.sankuai.com/js-cookie/download/js-cookie-2.2.1.tgz#69e106dc5d5806894562902aa5baec3744e9b2b8"
  integrity sha1-aeEG3F1YBolFYpAqpbrsN0Tpsrg=

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/js-tokens/download/js-tokens-4.0.0.tgz#19203fb59991df98e3a287050d4647cdeaf32499"
  integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=

js-yaml@^3.13.1:
  version "3.14.1"
  resolved "http://r.npm.sankuai.com/js-yaml/download/js-yaml-3.14.1.tgz#dae812fdb3825fa306609a8717383c50c36a0537"
  integrity sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

jsdom@^16.4.0:
  version "16.7.0"
  resolved "http://r.npm.sankuai.com/jsdom/download/jsdom-16.7.0.tgz#918ae71965424b197c819f8183a754e18977b710"
  integrity sha1-kYrnGWVCSxl8gZ+Bg6dU4Yl3txA=
  dependencies:
    abab "^2.0.5"
    acorn "^8.2.4"
    acorn-globals "^6.0.0"
    cssom "^0.4.4"
    cssstyle "^2.3.0"
    data-urls "^2.0.0"
    decimal.js "^10.2.1"
    domexception "^2.0.1"
    escodegen "^2.0.0"
    form-data "^3.0.0"
    html-encoding-sniffer "^2.0.1"
    http-proxy-agent "^4.0.1"
    https-proxy-agent "^5.0.0"
    is-potential-custom-element-name "^1.0.1"
    nwsapi "^2.2.0"
    parse5 "6.0.1"
    saxes "^5.0.1"
    symbol-tree "^3.2.4"
    tough-cookie "^4.0.0"
    w3c-hr-time "^1.0.2"
    w3c-xmlserializer "^2.0.0"
    webidl-conversions "^6.1.0"
    whatwg-encoding "^1.0.5"
    whatwg-mimetype "^2.3.0"
    whatwg-url "^8.5.0"
    ws "^7.4.6"
    xml-name-validator "^3.0.0"

jsesc@^2.5.1:
  version "2.5.2"
  resolved "http://r.npm.sankuai.com/jsesc/download/jsesc-2.5.2.tgz#80564d2e483dacf6e8ef209650a67df3f0c283a4"
  integrity sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q=

jsesc@~0.5.0:
  version "0.5.0"
  resolved "http://r.npm.sankuai.com/jsesc/download/jsesc-0.5.0.tgz#e7dee66e35d6fc16f710fe91d5cf69f70f08911d"
  integrity sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0=

json-buffer@3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/json-buffer/download/json-buffer-3.0.0.tgz#5b1f397afc75d677bde8bcfc0e47e1f9a3d9a898"
  integrity sha1-Wx85evx11ne96Lz8Dkfh+aPZqJg=

json-parse-better-errors@^1.0.1:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/json-parse-better-errors/download/json-parse-better-errors-1.0.2.tgz#bb867cfb3450e69107c131d1c514bab3dc8bcaa9"
  integrity sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk=

json-parse-even-better-errors@^2.3.0:
  version "2.3.1"
  resolved "http://r.npm.sankuai.com/json-parse-even-better-errors/download/json-parse-even-better-errors-2.3.1.tgz#7c47805a94319928e05777405dc12e1f7a4ee02d"
  integrity sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "http://r.npm.sankuai.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz#69f6a87d9513ab8bb8fe63bdb0979c448e684660"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json-schema-traverse@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/json-schema-traverse/download/json-schema-traverse-1.0.0.tgz#ae7bcb3656ab77a73ba5c49bf654f38e6b6860e2"
  integrity sha1-rnvLNlard6c7pcSb9lTzjmtoYOI=

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz#9db7b59496ad3f3cfef30a75142d2d930ad72651"
  integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=

json-stable-stringify@^1.0.1:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/json-stable-stringify/download/json-stable-stringify-1.0.2.tgz#e06f23128e0bbe342dc996ed5a19e28b57b580e0"
  integrity sha1-4G8jEo4LvjQtyZbtWhnii1e1gOA=
  dependencies:
    jsonify "^0.0.1"

json5@2.x, json5@^2.2.2:
  version "2.2.3"
  resolved "http://r.npm.sankuai.com/json5/download/json5-2.2.3.tgz#78cd6f1a19bdc12b73db5ad0c61efd66c1e29283"
  integrity sha1-eM1vGhm9wStz21rQxh79ZsHikoM=

json5@^0.5.1:
  version "0.5.1"
  resolved "http://r.npm.sankuai.com/json5/download/json5-0.5.1.tgz#1eade7acc012034ad84e2396767ead9fa5495821"
  integrity sha1-Hq3nrMASA0rYTiOWdn6tn6VJWCE=

json5@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/json5/download/json5-1.0.2.tgz#63d98d60f21b313b77c4d6da18bfa69d80e1d593"
  integrity sha1-Y9mNYPIbMTt3xNbaGL+mnYDh1ZM=
  dependencies:
    minimist "^1.2.0"

jsonfile@^2.1.0:
  version "2.4.0"
  resolved "http://r.npm.sankuai.com/jsonfile/download/jsonfile-2.4.0.tgz#3736a2b428b87bbda0cc83b53fa3d633a35c2ae8"
  integrity sha1-NzaitCi4e72gzIO1P6PWM6NcKug=
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonfile@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/jsonfile/download/jsonfile-4.0.0.tgz#8771aae0799b64076b76640fca058f9c10e33ecb"
  integrity sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonify@^0.0.1, jsonify@~0.0.0:
  version "0.0.1"
  resolved "http://r.npm.sankuai.com/jsonify/download/jsonify-0.0.1.tgz#2aa3111dae3d34a0f151c63f3a45d995d9420978"
  integrity sha1-KqMRHa49NKDxUcY/OkXZldlCCXg=

"jsx-ast-utils@^2.4.1 || ^3.0.0":
  version "3.3.3"
  resolved "http://r.npm.sankuai.com/jsx-ast-utils/download/jsx-ast-utils-3.3.3.tgz#76b3e6e6cece5c69d49a5792c3d01bd1a0cdc7ea"
  integrity sha1-drPm5s7OXGnUmleSw9Ab0aDNx+o=
  dependencies:
    array-includes "^3.1.5"
    object.assign "^4.1.3"

keymirror@0.1.1:
  version "0.1.1"
  resolved "http://r.npm.sankuai.com/keymirror/download/keymirror-0.1.1.tgz#918889ea13f8d0a42e7c557250eee713adc95c35"
  integrity sha1-kYiJ6hP40KQufFVyUO7nE63JXDU=

keyv@3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/keyv/download/keyv-3.0.0.tgz#44923ba39e68b12a7cec7df6c3268c031f2ef373"
  integrity sha1-RJI7o55osSp87H32wyaMAx8u83M=
  dependencies:
    json-buffer "3.0.0"

kind-of@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/kind-of/download/kind-of-1.1.0.tgz#140a3d2d41a36d2efcfa9377b62c24f8495a5c44"
  integrity sha1-FAo9LUGjbS78+pN3tiwk+ElaXEQ=

kind-of@^3.0.2, kind-of@^3.0.3, kind-of@^3.2.0:
  version "3.2.2"
  resolved "http://r.npm.sankuai.com/kind-of/download/kind-of-3.2.2.tgz#31ea21a734bab9bbb0f32466d893aea51e4a3c64"
  integrity sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/kind-of/download/kind-of-4.0.0.tgz#20813df3d712928b207378691a45066fae72dd57"
  integrity sha1-IIE989cSkosgc3hpGkUGb65y3Vc=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^5.0.0:
  version "5.1.0"
  resolved "http://r.npm.sankuai.com/kind-of/download/kind-of-5.1.0.tgz#729c91e2d857b7a419a1f9aa65685c4c33f5845d"
  integrity sha1-cpyR4thXt6QZofmqZWhcTDP1hF0=

kind-of@^6.0.0, kind-of@^6.0.2:
  version "6.0.3"
  resolved "http://r.npm.sankuai.com/kind-of/download/kind-of-6.0.3.tgz#07c05034a6c349fa06e24fa35aa76db4580ce4dd"
  integrity sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=

klaw@^1.0.0:
  version "1.3.1"
  resolved "http://r.npm.sankuai.com/klaw/download/klaw-1.3.1.tgz#4088433b46b3b1ba259d78785d8e96f73ba02439"
  integrity sha1-QIhDO0azsbolnXh4XY6W9zugJDk=
  optionalDependencies:
    graceful-fs "^4.1.9"

kleur@^3.0.3:
  version "3.0.3"
  resolved "http://r.npm.sankuai.com/kleur/download/kleur-3.0.3.tgz#a79c9ecc86ee1ce3fa6206d1216c501f147fc07e"
  integrity sha1-p5yezIbuHOP6YgbRIWxQHxR/wH4=

lazystream@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/lazystream/download/lazystream-1.0.1.tgz#494c831062f1f9408251ec44db1cba29242a2638"
  integrity sha1-SUyDEGLx+UCCUexE2xy6KSQqJjg=
  dependencies:
    readable-stream "^2.0.5"

lcid@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/lcid/download/lcid-1.0.0.tgz#308accafa0bc483a3867b4b6f2b9506251d1b835"
  integrity sha1-MIrMr6C8SDo4Z7S28rlQYlHRuDU=
  dependencies:
    invert-kv "^1.0.0"

leven@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/leven/download/leven-3.1.0.tgz#77891de834064cccba82ae7842bb6b14a13ed7f2"
  integrity sha1-d4kd6DQGTMy6gq54QrtrFKE+1/I=

levn@^0.4.1:
  version "0.4.1"
  resolved "http://r.npm.sankuai.com/levn/download/levn-0.4.1.tgz#ae4562c007473b932a6200d403268dd2fffc6ade"
  integrity sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

levn@~0.3.0:
  version "0.3.0"
  resolved "http://r.npm.sankuai.com/levn/download/levn-0.3.0.tgz#3b09924edf9f083c0490fdd4c0bc4421e04764ee"
  integrity sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4=
  dependencies:
    prelude-ls "~1.1.2"
    type-check "~0.3.2"

lines-and-columns@^1.1.6:
  version "1.2.4"
  resolved "http://r.npm.sankuai.com/lines-and-columns/download/lines-and-columns-1.2.4.tgz#eca284f75d2965079309dc0ad9255abb2ebc1632"
  integrity sha1-7KKE910pZQeTCdwK2SVauy68FjI=

linkify-it@^2.0.0:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/linkify-it/download/linkify-it-2.2.0.tgz#e3b54697e78bf915c70a38acd78fd09e0058b1cf"
  integrity sha1-47VGl+eL+RXHCjis14/QngBYsc8=
  dependencies:
    uc.micro "^1.0.1"

lint-staged@12.0.0:
  version "12.0.0"
  resolved "http://r.npm.sankuai.com/lint-staged/download/lint-staged-12.0.0.tgz#3d19fd7d68d43a29d3af1bccfc8bb72e8ed7f3b5"
  integrity sha1-PRn9fWjUOinTrxvM/Iu3Lo7X87U=
  dependencies:
    cli-truncate "3.1.0"
    colorette "^2.0.16"
    commander "^8.3.0"
    cosmiconfig "^7.0.1"
    debug "^4.3.2"
    execa "^5.1.1"
    listr2 "^3.13.3"
    micromatch "^4.0.4"
    normalize-path "^3.0.0"
    object-inspect "1.11.0"
    string-argv "0.3.1"
    supports-color "9.0.2"

listr2@^3.13.3:
  version "3.14.0"
  resolved "http://r.npm.sankuai.com/listr2/download/listr2-3.14.0.tgz#23101cc62e1375fd5836b248276d1d2b51fdbe9e"
  integrity sha1-IxAcxi4Tdf1YNrJIJ20dK1H9vp4=
  dependencies:
    cli-truncate "^2.1.0"
    colorette "^2.0.16"
    log-update "^4.0.0"
    p-map "^4.0.0"
    rfdc "^1.3.0"
    rxjs "^7.5.1"
    through "^2.3.8"
    wrap-ansi "^7.0.0"

locate-path@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/locate-path/download/locate-path-3.0.0.tgz#dbec3b3ab759758071b58fe59fc41871af21400e"
  integrity sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=
  dependencies:
    p-locate "^3.0.0"
    path-exists "^3.0.0"

locate-path@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/locate-path/download/locate-path-5.0.0.tgz#1afba396afd676a6d42504d0a67a3a7eb9f62aa0"
  integrity sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=
  dependencies:
    p-locate "^4.1.0"

locate-path@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/locate-path/download/locate-path-6.0.0.tgz#55321eb309febbc59c4801d931a72452a681d286"
  integrity sha1-VTIeswn+u8WcSAHZMackUqaB0oY=
  dependencies:
    p-locate "^5.0.0"

lodash.debounce@^4.0.8:
  version "4.0.8"
  resolved "http://r.npm.sankuai.com/lodash.debounce/download/lodash.debounce-4.0.8.tgz#82d79bff30a67c4005ffd5e2515300ad9ca4d7af"
  integrity sha1-gteb/zCmfEAF/9XiUVMArZyk168=

lodash.defaults@^4.2.0:
  version "4.2.0"
  resolved "http://r.npm.sankuai.com/lodash.defaults/download/lodash.defaults-4.2.0.tgz#d09178716ffea4dde9e5fb7b37f6f0802274580c"
  integrity sha1-0JF4cW/+pN3p5ft7N/bwgCJ0WAw=

lodash.difference@^4.5.0:
  version "4.5.0"
  resolved "http://r.npm.sankuai.com/lodash.difference/download/lodash.difference-4.5.0.tgz#9ccb4e505d486b91651345772885a2df27fd017c"
  integrity sha1-nMtOUF1Ia5FlE0V3KIWi3yf9AXw=

lodash.flatten@^4.4.0:
  version "4.4.0"
  resolved "http://r.npm.sankuai.com/lodash.flatten/download/lodash.flatten-4.4.0.tgz#f31c22225a9632d2bbf8e4addbef240aa765a61f"
  integrity sha1-8xwiIlqWMtK7+OSt2+8kCqdlph8=

lodash.isplainobject@^4.0.6:
  version "4.0.6"
  resolved "http://r.npm.sankuai.com/lodash.isplainobject/download/lodash.isplainobject-4.0.6.tgz#7c526a52d89b45c45cc690b88163be0497f550cb"
  integrity sha1-fFJqUtibRcRcxpC4gWO+BJf1UMs=

lodash.merge@^4.6.2:
  version "4.6.2"
  resolved "http://r.npm.sankuai.com/lodash.merge/download/lodash.merge-4.6.2.tgz#558aa53b43b661e1925a0afdfa36a9a1085fe57a"
  integrity sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=

lodash.throttle@^4.1.1:
  version "4.1.1"
  resolved "http://r.npm.sankuai.com/lodash.throttle/download/lodash.throttle-4.1.1.tgz#c23e91b710242ac70c37f1e1cda9274cc39bf2f4"
  integrity sha1-wj6RtxAkKscMN/HhzaknTMOb8vQ=

lodash.truncate@^4.4.2:
  version "4.4.2"
  resolved "http://r.npm.sankuai.com/lodash.truncate/download/lodash.truncate-4.4.2.tgz#5a350da0b1113b837ecfffd5812cbe58d6eae193"
  integrity sha1-WjUNoLERO4N+z//VgSy+WNbq4ZM=

lodash.union@^4.6.0:
  version "4.6.0"
  resolved "http://r.npm.sankuai.com/lodash.union/download/lodash.union-4.6.0.tgz#48bb5088409f16f1821666641c44dd1aaae3cd88"
  integrity sha1-SLtQiECfFvGCFmZkHETdGqrjzYg=

lodash@4.x, lodash@^4.0.0, lodash@^4.17.10, lodash@^4.17.11, lodash@^4.17.14, lodash@^4.17.15, lodash@^4.17.19, lodash@^4.17.20, lodash@^4.17.21, lodash@^4.3.0, lodash@^4.7.0:
  version "4.17.21"
  resolved "http://r.npm.sankuai.com/lodash/download/lodash-4.17.21.tgz#679591c564c3bffaae8454cf0b3df370c3d6911c"
  integrity sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=

log-symbols@^2.2.0:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/log-symbols/download/log-symbols-2.2.0.tgz#5740e1c5d6f0dfda4ad9323b5332107ef6b4c40a"
  integrity sha1-V0Dhxdbw39pK2TI7UzIQfva0xAo=
  dependencies:
    chalk "^2.0.1"

log-symbols@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/log-symbols/download/log-symbols-4.1.0.tgz#3fbdbb95b4683ac9fc785111e792e558d4abd503"
  integrity sha1-P727lbRoOsn8eFER55LlWNSr1QM=
  dependencies:
    chalk "^4.1.0"
    is-unicode-supported "^0.1.0"

log-update@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/log-update/download/log-update-4.0.0.tgz#589ecd352471f2a1c0c570287543a64dfd20e0a1"
  integrity sha1-WJ7NNSRx8qHAxXAodUOmTf0g4KE=
  dependencies:
    ansi-escapes "^4.3.0"
    cli-cursor "^3.1.0"
    slice-ansi "^4.0.0"
    wrap-ansi "^6.2.0"

logkitty@^0.7.1:
  version "0.7.1"
  resolved "http://r.npm.sankuai.com/logkitty/download/logkitty-0.7.1.tgz#8e8d62f4085a826e8d38987722570234e33c6aa7"
  integrity sha1-jo1i9Ahagm6NOJh3IlcCNOM8aqc=
  dependencies:
    ansi-fragments "^0.2.1"
    dayjs "^1.8.15"
    yargs "^15.1.0"

loose-envify@^1.0.0, loose-envify@^1.1.0, loose-envify@^1.3.1, loose-envify@^1.4.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/loose-envify/download/loose-envify-1.4.0.tgz#71ee51fa7be4caec1a63839f7e682d8132d30caf"
  integrity sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

lowercase-keys@1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/lowercase-keys/download/lowercase-keys-1.0.0.tgz#4e3366b39e7f5457e35f1324bdf6f88d0bfc7306"
  integrity sha1-TjNms55/VFfjXxMkvfb4jQv8cwY=

lowercase-keys@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/lowercase-keys/download/lowercase-keys-1.0.1.tgz#6f9e30b47084d971a7c820ff15a6c5167b74c26f"
  integrity sha1-b54wtHCE2XGnyCD/FabFFnt0wm8=

lru-cache@^4.0.1:
  version "4.1.5"
  resolved "http://r.npm.sankuai.com/lru-cache/download/lru-cache-4.1.5.tgz#8bbe50ea85bed59bc9e33dcab8235ee9bcf443cd"
  integrity sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80=
  dependencies:
    pseudomap "^1.0.2"
    yallist "^2.1.2"

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "http://r.npm.sankuai.com/lru-cache/download/lru-cache-5.1.1.tgz#1da27e6710271947695daf6848e847f01d84b920"
  integrity sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=
  dependencies:
    yallist "^3.0.2"

lru-cache@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/lru-cache/download/lru-cache-6.0.0.tgz#6d6fe6570ebd96aaf90fcad1dafa3b2566db3a94"
  integrity sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ=
  dependencies:
    yallist "^4.0.0"

make-dir@^1.0.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/make-dir/download/make-dir-1.3.0.tgz#79c1033b80515bd6d24ec9933e860ca75ee27f0c"
  integrity sha1-ecEDO4BRW9bSTsmTPoYMp17ifww=
  dependencies:
    pify "^3.0.0"

make-dir@^2.0.0, make-dir@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/make-dir/download/make-dir-2.1.0.tgz#5f0310e18b8be898cc07009295a30ae41e91e6f5"
  integrity sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU=
  dependencies:
    pify "^4.0.1"
    semver "^5.6.0"

make-dir@^3.0.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/make-dir/download/make-dir-3.1.0.tgz#415e967046b3a7f1d185277d84aa58203726a13f"
  integrity sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8=
  dependencies:
    semver "^6.0.0"

make-error@1.x:
  version "1.3.6"
  resolved "http://r.npm.sankuai.com/make-error/download/make-error-1.3.6.tgz#2eb2e37ea9b67c4891f684a1394799af484cf7a2"
  integrity sha1-LrLjfqm2fEiR9oShOUeZr0hM96I=

makeerror@1.0.12:
  version "1.0.12"
  resolved "http://r.npm.sankuai.com/makeerror/download/makeerror-1.0.12.tgz#3e5dd2079a82e812e983cc6610c4a2cb0eaa801a"
  integrity sha1-Pl3SB5qC6BLpg8xmEMSiyw6qgBo=
  dependencies:
    tmpl "1.0.5"

map-cache@^0.2.2:
  version "0.2.2"
  resolved "http://r.npm.sankuai.com/map-cache/download/map-cache-0.2.2.tgz#c32abd0bd6525d9b051645bb4f26ac5dc98a0dbf"
  integrity sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=

map-visit@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/map-visit/download/map-visit-1.0.0.tgz#ecdca8f13144e660f1b5bd41f12f3479d98dfb8f"
  integrity sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=
  dependencies:
    object-visit "^1.0.0"

markdown-it@^10.0.0:
  version "10.0.0"
  resolved "http://r.npm.sankuai.com/markdown-it/download/markdown-it-10.0.0.tgz#abfc64f141b1722d663402044e43927f1f50a8dc"
  integrity sha1-q/xk8UGxci1mNAIETkOSfx9QqNw=
  dependencies:
    argparse "^1.0.7"
    entities "~2.0.0"
    linkify-it "^2.0.0"
    mdurl "^1.0.1"
    uc.micro "^1.0.5"

mdn-data@2.0.14:
  version "2.0.14"
  resolved "http://r.npm.sankuai.com/mdn-data/download/mdn-data-2.0.14.tgz#7113fc4281917d63ce29b43446f701e68c25ba50"
  integrity sha1-cRP8QoGRfWPOKbQ0RvcB5owlulA=

mdurl@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/mdurl/download/mdurl-1.0.1.tgz#fe85b2ec75a59037f2adfec100fd6c601761152e"
  integrity sha1-/oWy7HWlkDfyrf7BAP1sYBdhFS4=

merge-stream@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/merge-stream/download/merge-stream-1.0.1.tgz#4041202d508a342ba00174008df0c251b8c135e1"
  integrity sha1-QEEgLVCKNCugAXQAjfDCUbjBNeE=
  dependencies:
    readable-stream "^2.0.1"

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/merge-stream/download/merge-stream-2.0.0.tgz#52823629a14dd00c9770fb6ad47dc6310f2c1f60"
  integrity sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=

merge2@^1.3.0, merge2@^1.4.1:
  version "1.4.1"
  resolved "http://r.npm.sankuai.com/merge2/download/merge2-1.4.1.tgz#4368892f885e907455a6fd7dc55c0c9d404990ae"
  integrity sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=

metro-babel-register@0.59.0:
  version "0.59.0"
  resolved "http://r.npm.sankuai.com/metro-babel-register/download/metro-babel-register-0.59.0.tgz#2bcff65641b36794cf083ba732fbc46cf870fb43"
  integrity sha1-K8/2VkGzZ5TPCDunMvvEbPhw+0M=
  dependencies:
    "@babel/core" "^7.0.0"
    "@babel/plugin-proposal-class-properties" "^7.0.0"
    "@babel/plugin-proposal-nullish-coalescing-operator" "^7.0.0"
    "@babel/plugin-proposal-optional-chaining" "^7.0.0"
    "@babel/plugin-transform-flow-strip-types" "^7.0.0"
    "@babel/plugin-transform-modules-commonjs" "^7.0.0"
    "@babel/register" "^7.0.0"
    escape-string-regexp "^1.0.5"

metro-babel-transformer@0.59.0:
  version "0.59.0"
  resolved "http://r.npm.sankuai.com/metro-babel-transformer/download/metro-babel-transformer-0.59.0.tgz#dda99c75d831b00142c42c020c51c103b29f199d"
  integrity sha1-3amcddgxsAFCxCwCDFHBA7KfGZ0=
  dependencies:
    "@babel/core" "^7.0.0"
    metro-source-map "0.59.0"

metro-cache@0.59.0:
  version "0.59.0"
  resolved "http://r.npm.sankuai.com/metro-cache/download/metro-cache-0.59.0.tgz#ef3c055f276933979b731455dc8317d7a66f0f2d"
  integrity sha1-7zwFXydpM5ebcxRV3IMX16ZvDy0=
  dependencies:
    jest-serializer "^24.9.0"
    metro-core "0.59.0"
    mkdirp "^0.5.1"
    rimraf "^2.5.4"

metro-config@0.59.0, metro-config@^0.59.0:
  version "0.59.0"
  resolved "http://r.npm.sankuai.com/metro-config/download/metro-config-0.59.0.tgz#9844e388069321dd7403e49f0d495a81f9aa0fef"
  integrity sha1-mETjiAaTId10A+SfDUlagfmqD+8=
  dependencies:
    cosmiconfig "^5.0.5"
    jest-validate "^24.9.0"
    metro "0.59.0"
    metro-cache "0.59.0"
    metro-core "0.59.0"

metro-core@0.59.0, metro-core@^0.59.0:
  version "0.59.0"
  resolved "http://r.npm.sankuai.com/metro-core/download/metro-core-0.59.0.tgz#958cde3fe5c8cd84a78e1899af801ad69e9c83b1"
  integrity sha1-lYzeP+XIzYSnjhiZr4Aa1p6cg7E=
  dependencies:
    jest-haste-map "^24.9.0"
    lodash.throttle "^4.1.1"
    metro-resolver "0.59.0"
    wordwrap "^1.0.0"

metro-inspector-proxy@0.59.0:
  version "0.59.0"
  resolved "http://r.npm.sankuai.com/metro-inspector-proxy/download/metro-inspector-proxy-0.59.0.tgz#39d1390772d13767fc595be9a1a7074e2425cf8e"
  integrity sha1-OdE5B3LRN2f8WVvpoacHTiQlz44=
  dependencies:
    connect "^3.6.5"
    debug "^2.2.0"
    ws "^1.1.5"
    yargs "^14.2.0"

metro-minify-uglify@0.59.0:
  version "0.59.0"
  resolved "http://r.npm.sankuai.com/metro-minify-uglify/download/metro-minify-uglify-0.59.0.tgz#6491876308d878742f7b894d7fca4af356886dd5"
  integrity sha1-ZJGHYwjYeHQve4lNf8pK81aIbdU=
  dependencies:
    uglify-es "^3.1.9"

metro-react-native-babel-preset@0.59.0:
  version "0.59.0"
  resolved "http://r.npm.sankuai.com/metro-react-native-babel-preset/download/metro-react-native-babel-preset-0.59.0.tgz#20e020bc6ac9849e1477de1333d303ed42aba225"
  integrity sha1-IOAgvGrJhJ4Ud94TM9MD7UKroiU=
  dependencies:
    "@babel/plugin-proposal-class-properties" "^7.0.0"
    "@babel/plugin-proposal-export-default-from" "^7.0.0"
    "@babel/plugin-proposal-nullish-coalescing-operator" "^7.0.0"
    "@babel/plugin-proposal-object-rest-spread" "^7.0.0"
    "@babel/plugin-proposal-optional-catch-binding" "^7.0.0"
    "@babel/plugin-proposal-optional-chaining" "^7.0.0"
    "@babel/plugin-syntax-dynamic-import" "^7.0.0"
    "@babel/plugin-syntax-export-default-from" "^7.0.0"
    "@babel/plugin-syntax-flow" "^7.2.0"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.0.0"
    "@babel/plugin-syntax-optional-chaining" "^7.0.0"
    "@babel/plugin-transform-arrow-functions" "^7.0.0"
    "@babel/plugin-transform-block-scoping" "^7.0.0"
    "@babel/plugin-transform-classes" "^7.0.0"
    "@babel/plugin-transform-computed-properties" "^7.0.0"
    "@babel/plugin-transform-destructuring" "^7.0.0"
    "@babel/plugin-transform-exponentiation-operator" "^7.0.0"
    "@babel/plugin-transform-flow-strip-types" "^7.0.0"
    "@babel/plugin-transform-for-of" "^7.0.0"
    "@babel/plugin-transform-function-name" "^7.0.0"
    "@babel/plugin-transform-literals" "^7.0.0"
    "@babel/plugin-transform-modules-commonjs" "^7.0.0"
    "@babel/plugin-transform-object-assign" "^7.0.0"
    "@babel/plugin-transform-parameters" "^7.0.0"
    "@babel/plugin-transform-react-display-name" "^7.0.0"
    "@babel/plugin-transform-react-jsx" "^7.0.0"
    "@babel/plugin-transform-react-jsx-self" "^7.0.0"
    "@babel/plugin-transform-react-jsx-source" "^7.0.0"
    "@babel/plugin-transform-regenerator" "^7.0.0"
    "@babel/plugin-transform-runtime" "^7.0.0"
    "@babel/plugin-transform-shorthand-properties" "^7.0.0"
    "@babel/plugin-transform-spread" "^7.0.0"
    "@babel/plugin-transform-sticky-regex" "^7.0.0"
    "@babel/plugin-transform-template-literals" "^7.0.0"
    "@babel/plugin-transform-typescript" "^7.5.0"
    "@babel/plugin-transform-unicode-regex" "^7.0.0"
    "@babel/template" "^7.0.0"
    react-refresh "^0.4.0"

metro-react-native-babel-transformer@^0.59.0:
  version "0.59.0"
  resolved "http://r.npm.sankuai.com/metro-react-native-babel-transformer/download/metro-react-native-babel-transformer-0.59.0.tgz#9b3dfd6ad35c6ef37fc4ce4d20a2eb67fabbb4be"
  integrity sha1-mz39atNcbvN/xM5NIKLrZ/q7tL4=
  dependencies:
    "@babel/core" "^7.0.0"
    babel-preset-fbjs "^3.3.0"
    metro-babel-transformer "0.59.0"
    metro-react-native-babel-preset "0.59.0"
    metro-source-map "0.59.0"

metro-resolver@0.59.0, metro-resolver@^0.59.0:
  version "0.59.0"
  resolved "http://r.npm.sankuai.com/metro-resolver/download/metro-resolver-0.59.0.tgz#fbc9d7c95f094c52807877d0011feffb9e896fad"
  integrity sha1-+8nXyV8JTFKAeHfQAR/v+56Jb60=
  dependencies:
    absolute-path "^0.0.0"

metro-source-map@0.59.0:
  version "0.59.0"
  resolved "http://r.npm.sankuai.com/metro-source-map/download/metro-source-map-0.59.0.tgz#e9beb9fc51bfb4e060f95820cf1508fc122d23f7"
  integrity sha1-6b65/FG/tOBg+VggzxUI/BItI/c=
  dependencies:
    "@babel/traverse" "^7.0.0"
    "@babel/types" "^7.0.0"
    invariant "^2.2.4"
    metro-symbolicate "0.59.0"
    ob1 "0.59.0"
    source-map "^0.5.6"
    vlq "^1.0.0"

metro-symbolicate@0.59.0:
  version "0.59.0"
  resolved "http://r.npm.sankuai.com/metro-symbolicate/download/metro-symbolicate-0.59.0.tgz#fc7f93957a42b02c2bfc57ed1e8f393f5f636a54"
  integrity sha1-/H+TlXpCsCwr/FftHo85P19jalQ=
  dependencies:
    invariant "^2.2.4"
    metro-source-map "0.59.0"
    source-map "^0.5.6"
    through2 "^2.0.1"
    vlq "^1.0.0"

metro@0.59.0, metro@^0.59.0:
  version "0.59.0"
  resolved "http://r.npm.sankuai.com/metro/download/metro-0.59.0.tgz#64a87cd61357814a4f279518e0781b1eab5934b8"
  integrity sha1-ZKh81hNXgUpPJ5UY4HgbHqtZNLg=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@babel/core" "^7.0.0"
    "@babel/generator" "^7.5.0"
    "@babel/parser" "^7.0.0"
    "@babel/plugin-external-helpers" "^7.0.0"
    "@babel/template" "^7.0.0"
    "@babel/traverse" "^7.0.0"
    "@babel/types" "^7.0.0"
    absolute-path "^0.0.0"
    async "^2.4.0"
    babel-preset-fbjs "^3.3.0"
    buffer-crc32 "^0.2.13"
    chalk "^2.4.1"
    ci-info "^2.0.0"
    concat-stream "^1.6.0"
    connect "^3.6.5"
    debug "^2.2.0"
    denodeify "^1.2.1"
    error-stack-parser "^2.0.6"
    eventemitter3 "^3.0.0"
    fbjs "^1.0.0"
    fs-extra "^1.0.0"
    graceful-fs "^4.1.3"
    image-size "^0.6.0"
    invariant "^2.2.4"
    jest-haste-map "^24.9.0"
    jest-worker "^24.9.0"
    json-stable-stringify "^1.0.1"
    lodash.throttle "^4.1.1"
    merge-stream "^1.0.1"
    metro-babel-register "0.59.0"
    metro-babel-transformer "0.59.0"
    metro-cache "0.59.0"
    metro-config "0.59.0"
    metro-core "0.59.0"
    metro-inspector-proxy "0.59.0"
    metro-minify-uglify "0.59.0"
    metro-react-native-babel-preset "0.59.0"
    metro-resolver "0.59.0"
    metro-source-map "0.59.0"
    metro-symbolicate "0.59.0"
    mime-types "2.1.11"
    mkdirp "^0.5.1"
    node-fetch "^2.2.0"
    nullthrows "^1.1.1"
    resolve "^1.5.0"
    rimraf "^2.5.4"
    serialize-error "^2.1.0"
    source-map "^0.5.6"
    strip-ansi "^4.0.0"
    temp "0.8.3"
    throat "^4.1.0"
    wordwrap "^1.0.0"
    ws "^1.1.5"
    xpipe "^1.0.5"
    yargs "^14.2.0"

micromatch@^3.1.10, micromatch@^3.1.4:
  version "3.1.10"
  resolved "http://r.npm.sankuai.com/micromatch/download/micromatch-3.1.10.tgz#70859bc95c9840952f359a068a3fc49f9ecfac23"
  integrity sha1-cIWbyVyYQJUvNZoGij/En57PrCM=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    braces "^2.3.1"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    extglob "^2.0.4"
    fragment-cache "^0.2.1"
    kind-of "^6.0.2"
    nanomatch "^1.2.9"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.2"

micromatch@^4.0.2, micromatch@^4.0.4:
  version "4.0.5"
  resolved "http://r.npm.sankuai.com/micromatch/download/micromatch-4.0.5.tgz#bc8999a7cbbf77cdc89f132f6e467051b49090c6"
  integrity sha1-vImZp8u/d83InxMvbkZwUbSQkMY=
  dependencies:
    braces "^3.0.2"
    picomatch "^2.3.1"

mime-db@1.52.0, "mime-db@>= 1.43.0 < 2", mime-db@^1.28.0:
  version "1.52.0"
  resolved "http://r.npm.sankuai.com/mime-db/download/mime-db-1.52.0.tgz#bbabcdc02859f4987301c856e3387ce5ec43bf70"
  integrity sha1-u6vNwChZ9JhzAchW4zh85exDv3A=

mime-db@~1.23.0:
  version "1.23.0"
  resolved "http://r.npm.sankuai.com/mime-db/download/mime-db-1.23.0.tgz#a31b4070adaea27d732ea333740a64d0ec9a6659"
  integrity sha1-oxtAcK2uon1zLqMzdApk0OyaZlk=

mime-types@2.1.11:
  version "2.1.11"
  resolved "http://r.npm.sankuai.com/mime-types/download/mime-types-2.1.11.tgz#c259c471bda808a85d6cd193b430a5fae4473b3c"
  integrity sha1-wlnEcb2oCKhdbNGTtDCl+uRHOzw=
  dependencies:
    mime-db "~1.23.0"

mime-types@^2.1.12, mime-types@~2.1.34:
  version "2.1.35"
  resolved "http://r.npm.sankuai.com/mime-types/download/mime-types-2.1.35.tgz#381a871b62a734450660ae3deee44813f70d959a"
  integrity sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=
  dependencies:
    mime-db "1.52.0"

mime@1.6.0:
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/mime/download/mime-1.6.0.tgz#32cd9e5c64553bd58d19a568af452acff04981b1"
  integrity sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=

mime@^2.4.1:
  version "2.6.0"
  resolved "http://r.npm.sankuai.com/mime/download/mime-2.6.0.tgz#a2a682a95cd4d0cb1d6257e28f83da7e35800367"
  integrity sha1-oqaCqVzU0MsdYlfij4PafjWAA2c=

mimic-fn@^1.0.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/mimic-fn/download/mimic-fn-1.2.0.tgz#820c86a39334640e99516928bd03fca88057d022"
  integrity sha1-ggyGo5M0ZA6ZUWkovQP8qIBX0CI=

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/mimic-fn/download/mimic-fn-2.1.0.tgz#7ed2c2ccccaf84d3ffcb7a69b57711fc2083401b"
  integrity sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=

mimic-response@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/mimic-response/download/mimic-response-1.0.1.tgz#4923538878eef42063cb8a3e3b0798781487ab1b"
  integrity sha1-SSNTiHju9CBjy4o+OweYeBSHqxs=

min-indent@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/min-indent/download/min-indent-1.0.1.tgz#a63f681673b30571fbe8bc25686ae746eefa9869"
  integrity sha1-pj9oFnOzBXH76LwlaGrnRu76mGk=

minimatch@^3.0.4, minimatch@^3.1.1, minimatch@^3.1.2:
  version "3.1.2"
  resolved "http://r.npm.sankuai.com/minimatch/download/minimatch-3.1.2.tgz#19cd194bfd3e428f049a70817c038d89ab4be35b"
  integrity sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^5.0.1, minimatch@^5.1.0:
  version "5.1.6"
  resolved "http://r.npm.sankuai.com/minimatch/download/minimatch-5.1.6.tgz#1cfcb8cf5522ea69952cd2af95ae09477f122a96"
  integrity sha1-HPy4z1Ui6mmVLNKvla4JR38SKpY=
  dependencies:
    brace-expansion "^2.0.1"

minimist@^1.1.1, minimist@^1.2.0, minimist@^1.2.6:
  version "1.2.8"
  resolved "http://r.npm.sankuai.com/minimist/download/minimist-1.2.8.tgz#c1a464e7693302e082a075cee0c057741ac4772c"
  integrity sha1-waRk52kzAuCCoHXO4MBXdBrEdyw=

mixin-deep@^1.2.0:
  version "1.3.2"
  resolved "http://r.npm.sankuai.com/mixin-deep/download/mixin-deep-1.3.2.tgz#1120b43dc359a785dce65b55b82e257ccf479566"
  integrity sha1-ESC0PcNZp4Xc5ltVuC4lfM9HlWY=
  dependencies:
    for-in "^1.0.2"
    is-extendable "^1.0.1"

mkdirp@1.x:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/mkdirp/download/mkdirp-1.0.4.tgz#3eb5ed62622756d79a5f0e2a221dfebad75c2f7e"
  integrity sha1-PrXtYmInVteaXw4qIh3+utdcL34=

mkdirp@^0.5.1, mkdirp@^0.5.6:
  version "0.5.6"
  resolved "http://r.npm.sankuai.com/mkdirp/download/mkdirp-0.5.6.tgz#7def03d2432dcae4ba1d611445c48396062255f6"
  integrity sha1-fe8D0kMtyuS6HWEURcSDlgYiVfY=
  dependencies:
    minimist "^1.2.6"

moment@^2.24.0:
  version "2.29.4"
  resolved "http://r.npm.sankuai.com/moment/download/moment-2.29.4.tgz#3dbe052889fe7c1b2ed966fcb3a77328964ef108"
  integrity sha1-Pb4FKIn+fBsu2Wb8s6dzKJZO8Qg=

ms@2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/ms/download/ms-2.0.0.tgz#5608aeadfc00be6c2901df5f9861788de0d597c8"
  integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=

ms@2.1.2:
  version "2.1.2"
  resolved "http://r.npm.sankuai.com/ms/download/ms-2.1.2.tgz#d09d1f357b443f493382a8eb3ccd183872ae6009"
  integrity sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=

ms@2.1.3, ms@^2.1.1:
  version "2.1.3"
  resolved "http://r.npm.sankuai.com/ms/download/ms-2.1.3.tgz#574c8138ce1d2b5861f0b44579dbadd60c6615b2"
  integrity sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=

mute-stream@0.0.7:
  version "0.0.7"
  resolved "http://r.npm.sankuai.com/mute-stream/download/mute-stream-0.0.7.tgz#3075ce93bc21b8fab43e1bc4da7e8115ed1e7bab"
  integrity sha1-MHXOk7whuPq0PhvE2n6BFe0ee6s=

mute-stream@0.0.8:
  version "0.0.8"
  resolved "http://r.npm.sankuai.com/mute-stream/download/mute-stream-0.0.8.tgz#1630c42b2251ff81e2a283de96a5497ea92e5e0d"
  integrity sha1-FjDEKyJR/4HiooPelqVJfqkuXg0=

nan@^2.12.1:
  version "2.17.0"
  resolved "http://r.npm.sankuai.com/nan/download/nan-2.17.0.tgz#c0150a2368a182f033e9aa5195ec76ea41a199cb"
  integrity sha1-wBUKI2ihgvAz6apRlex26kGhmcs=

nanoid@^3.1.23:
  version "3.3.6"
  resolved "http://r.npm.sankuai.com/nanoid/download/nanoid-3.3.6.tgz#443380c856d6e9f9824267d960b4236ad583ea4c"
  integrity sha1-RDOAyFbW6fmCQmfZYLQjatWD6kw=

nanomatch@^1.2.9:
  version "1.2.13"
  resolved "http://r.npm.sankuai.com/nanomatch/download/nanomatch-1.2.13.tgz#b87a8aa4fc0de8fe6be88895b38983ff265bd119"
  integrity sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    fragment-cache "^0.2.1"
    is-windows "^1.0.2"
    kind-of "^6.0.2"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

natural-compare-lite@^1.4.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/natural-compare-lite/download/natural-compare-lite-1.4.0.tgz#17b09581988979fddafe0201e931ba933c96cbb4"
  integrity sha1-F7CVgZiJef3a/gIB6TG6kzyWy7Q=

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/natural-compare/download/natural-compare-1.4.0.tgz#4abebfeed7541f2c27acfb29bdbbd15c8d5ba4f7"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

negotiator@0.6.3:
  version "0.6.3"
  resolved "http://r.npm.sankuai.com/negotiator/download/negotiator-0.6.3.tgz#58e323a72fedc0d6f9cd4d31fe49f51479590ccd"
  integrity sha1-WOMjpy/twNb5zU0x/kn1FHlZDM0=

nice-try@^1.0.4:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/nice-try/download/nice-try-1.0.5.tgz#a3378a7696ce7d223e88fc9b764bd7ef1089e366"
  integrity sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y=

nocache@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/nocache/download/nocache-2.1.0.tgz#120c9ffec43b5729b1d5de88cd71aa75a0ba491f"
  integrity sha1-Egyf/sQ7Vymx1d6IzXGqdaC6SR8=

node-addon-api@^4.2.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/node-addon-api/download/node-addon-api-4.3.0.tgz#52a1a0b475193e0928e98e0426a0d1254782b77f"
  integrity sha1-UqGgtHUZPgko6Y4EJqDRJUeCt38=

node-fetch@^1.0.1:
  version "1.7.3"
  resolved "http://r.npm.sankuai.com/node-fetch/download/node-fetch-1.7.3.tgz#980f6f72d85211a5347c6b2bc18c5b84c3eb47ef"
  integrity sha1-mA9vcthSEaU0fGsrwYxbhMPrR+8=
  dependencies:
    encoding "^0.1.11"
    is-stream "^1.0.1"

node-fetch@^2.2.0, node-fetch@^2.6.0:
  version "2.6.9"
  resolved "http://r.npm.sankuai.com/node-fetch/download/node-fetch-2.6.9.tgz#7c7f744b5cc6eb5fd404e0c7a9fec630a55657e6"
  integrity sha1-fH90S1zG61/UBODHqf7GMKVWV+Y=
  dependencies:
    whatwg-url "^5.0.0"

node-gyp-build@^4.3.0:
  version "4.6.0"
  resolved "http://r.npm.sankuai.com/node-gyp-build/download/node-gyp-build-4.6.0.tgz#0c52e4cbf54bbd28b709820ef7b6a3c2d6209055"
  integrity sha1-DFLky/VLvSi3CYIO97ajwtYgkFU=

node-int64@^0.4.0:
  version "0.4.0"
  resolved "http://r.npm.sankuai.com/node-int64/download/node-int64-0.4.0.tgz#87a9065cdb355d3182d8f94ce11188b825c68a3b"
  integrity sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs=

node-machine-id@^1.1.12:
  version "1.1.12"
  resolved "http://r.npm.sankuai.com/node-machine-id/download/node-machine-id-1.1.12.tgz#37904eee1e59b320bb9c5d6c0a59f3b469cb6267"
  integrity sha1-N5BO7h5ZsyC7nF1sClnztGnLYmc=

node-notifier@^8.0.0:
  version "8.0.2"
  resolved "http://r.npm.sankuai.com/node-notifier/download/node-notifier-8.0.2.tgz#f3167a38ef0d2c8a866a83e318c1ba0efeb702c5"
  integrity sha1-8xZ6OO8NLIqGaoPjGMG6Dv63AsU=
  dependencies:
    growly "^1.3.0"
    is-wsl "^2.2.0"
    semver "^7.3.2"
    shellwords "^0.1.1"
    uuid "^8.3.0"
    which "^2.0.2"

node-releases@^2.0.8:
  version "2.0.10"
  resolved "http://r.npm.sankuai.com/node-releases/download/node-releases-2.0.10.tgz#c311ebae3b6a148c89b1813fd7c4d3c024ef537f"
  integrity sha1-wxHrrjtqFIyJsYE/18TTwCTvU38=

node-stream-zip@^1.9.1:
  version "1.15.0"
  resolved "http://r.npm.sankuai.com/node-stream-zip/download/node-stream-zip-1.15.0.tgz#158adb88ed8004c6c49a396b50a6a5de3bca33ea"
  integrity sha1-FYrbiO2ABMbEmjlrUKal3jvKM+o=

normalize-package-data@^2.5.0:
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/normalize-package-data/download/normalize-package-data-2.5.0.tgz#e66db1838b200c1dfc233225d12cb36520e234a8"
  integrity sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=
  dependencies:
    hosted-git-info "^2.1.4"
    resolve "^1.10.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

normalize-path@^2.1.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/normalize-path/download/normalize-path-2.1.1.tgz#1ab28b556e198363a8c1a6f7e6fa20137fe6aed9"
  integrity sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=
  dependencies:
    remove-trailing-separator "^1.0.1"

normalize-path@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/normalize-path/download/normalize-path-3.0.0.tgz#0dcd69ff23a1c9b11fd0978316644a0388216a65"
  integrity sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=

normalize-url@2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/normalize-url/download/normalize-url-2.0.1.tgz#835a9da1551fa26f70e92329069a23aa6574d7e6"
  integrity sha1-g1qdoVUfom9w6SMpBpojqmV01+Y=
  dependencies:
    prepend-http "^2.0.0"
    query-string "^5.0.1"
    sort-keys "^2.0.0"

npm-run-path@^2.0.0:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/npm-run-path/download/npm-run-path-2.0.2.tgz#35a9232dfa35d7067b4cb2ddf2357b1871536c5f"
  integrity sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8=
  dependencies:
    path-key "^2.0.0"

npm-run-path@^4.0.0, npm-run-path@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/npm-run-path/download/npm-run-path-4.0.1.tgz#b7ecd1e5ed53da8e37a55e1c2269e0b97ed748ea"
  integrity sha1-t+zR5e1T2o43pV4cImnguX7XSOo=
  dependencies:
    path-key "^3.0.0"

nth-check@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/nth-check/download/nth-check-1.0.2.tgz#b2bd295c37e3dd58a3bf0700376663ba4d9cf05c"
  integrity sha1-sr0pXDfj3VijvwcAN2Zjuk2c8Fw=
  dependencies:
    boolbase "~1.0.0"

nullthrows@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/nullthrows/download/nullthrows-1.1.1.tgz#7818258843856ae971eae4208ad7d7eb19a431b1"
  integrity sha1-eBgliEOFaulx6uQgitfX6xmkMbE=

number-is-nan@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/number-is-nan/download/number-is-nan-1.0.1.tgz#097b602b53422a522c1afb8790318336941a011d"
  integrity sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0=

nwsapi@^2.2.0:
  version "2.2.2"
  resolved "http://r.npm.sankuai.com/nwsapi/download/nwsapi-2.2.2.tgz#e5418863e7905df67d51ec95938d67bf801f0bb0"
  integrity sha1-5UGIY+eQXfZ9UeyVk41nv4AfC7A=

ob1@0.59.0:
  version "0.59.0"
  resolved "http://r.npm.sankuai.com/ob1/download/ob1-0.59.0.tgz#ee103619ef5cb697f2866e3577da6f0ecd565a36"
  integrity sha1-7hA2Ge9ctpfyhm41d9pvDs1WWjY=

object-assign@^4.0.1, object-assign@^4.1.0, object-assign@^4.1.1:
  version "4.1.1"
  resolved "http://r.npm.sankuai.com/object-assign/download/object-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-copy@^0.1.0:
  version "0.1.0"
  resolved "http://r.npm.sankuai.com/object-copy/download/object-copy-0.1.0.tgz#7e7d858b781bd7c991a41ba975ed3812754e998c"
  integrity sha1-fn2Fi3gb18mRpBupde04EnVOmYw=
  dependencies:
    copy-descriptor "^0.1.0"
    define-property "^0.2.5"
    kind-of "^3.0.3"

object-inspect@1.11.0:
  version "1.11.0"
  resolved "http://r.npm.sankuai.com/object-inspect/download/object-inspect-1.11.0.tgz#9dceb146cedd4148a0d9e51ab88d34cf509922b1"
  integrity sha1-nc6xRs7dQUig2eUauI00z1CZIrE=

object-inspect@^1.12.3, object-inspect@^1.9.0:
  version "1.12.3"
  resolved "http://r.npm.sankuai.com/object-inspect/download/object-inspect-1.12.3.tgz#ba62dffd67ee256c8c086dfae69e016cd1f198b9"
  integrity sha1-umLf/WfuJWyMCG365p4BbNHxmLk=

object-inspect@^1.13.1:
  version "1.13.1"
  resolved "http://r.npm.sankuai.com/object-inspect/download/object-inspect-1.13.1.tgz#b96c6109324ccfef6b12216a956ca4dc2ff94bc2"
  integrity sha1-uWxhCTJMz+9rEiFqlWyk3C/5S8I=

object-keys@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/object-keys/download/object-keys-1.1.1.tgz#1c47f272df277f3b1daf061677d9c82e2322c60e"
  integrity sha1-HEfyct8nfzsdrwYWd9nILiMixg4=

object-visit@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/object-visit/download/object-visit-1.0.1.tgz#f79c4493af0c5377b59fe39d395e41042dd045bb"
  integrity sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=
  dependencies:
    isobject "^3.0.0"

object.assign@^4.1.3, object.assign@^4.1.4:
  version "4.1.4"
  resolved "http://r.npm.sankuai.com/object.assign/download/object.assign-4.1.4.tgz#9673c7c7c351ab8c4d0b516f4343ebf4dfb7799f"
  integrity sha1-lnPHx8NRq4xNC1FvQ0Pr9N+3eZ8=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    has-symbols "^1.0.3"
    object-keys "^1.1.1"

object.assign@^4.1.5:
  version "4.1.5"
  resolved "http://r.npm.sankuai.com/object.assign/download/object.assign-4.1.5.tgz#3a833f9ab7fdb80fc9e8d2300c803d216d8fdbb0"
  integrity sha1-OoM/mrf9uA/J6NIwDIA9IW2P27A=
  dependencies:
    call-bind "^1.0.5"
    define-properties "^1.2.1"
    has-symbols "^1.0.3"
    object-keys "^1.1.1"

object.entries@^1.1.6:
  version "1.1.6"
  resolved "http://r.npm.sankuai.com/object.entries/download/object.entries-1.1.6.tgz#9737d0e5b8291edd340a3e3264bb8a3b00d5fa23"
  integrity sha1-lzfQ5bgpHt00Cj4yZLuKOwDV+iM=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"

object.fromentries@^2.0.6:
  version "2.0.6"
  resolved "http://r.npm.sankuai.com/object.fromentries/download/object.fromentries-2.0.6.tgz#cdb04da08c539cffa912dcd368b886e0904bfa73"
  integrity sha1-zbBNoIxTnP+pEtzTaLiG4JBL+nM=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"

object.fromentries@^2.0.7:
  version "2.0.8"
  resolved "http://r.npm.sankuai.com/object.fromentries/download/object.fromentries-2.0.8.tgz#f7195d8a9b97bd95cbc1999ea939ecd1a2b00c65"
  integrity sha1-9xldipuXvZXLwZmeqTns0aKwDGU=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-object-atoms "^1.0.0"

object.groupby@^1.0.1:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/object.groupby/download/object.groupby-1.0.3.tgz#9b125c36238129f6f7b61954a1e7176148d5002e"
  integrity sha1-mxJcNiOBKfb3thlUoecXYUjVAC4=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"

object.hasown@^1.1.2:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/object.hasown/download/object.hasown-1.1.2.tgz#f919e21fad4eb38a57bc6345b3afd496515c3f92"
  integrity sha1-+RniH61Os4pXvGNFs6/UllFcP5I=
  dependencies:
    define-properties "^1.1.4"
    es-abstract "^1.20.4"

object.pick@^1.3.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/object.pick/download/object.pick-1.3.0.tgz#87a10ac4c1694bd2e1cbf53591a66141fb5dd747"
  integrity sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=
  dependencies:
    isobject "^3.0.1"

object.values@^1.1.6:
  version "1.1.6"
  resolved "http://r.npm.sankuai.com/object.values/download/object.values-1.1.6.tgz#4abbaa71eba47d63589d402856f908243eea9b1d"
  integrity sha1-SruqceukfWNYnUAoVvkIJD7qmx0=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"

object.values@^1.1.7:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/object.values/download/object.values-1.2.0.tgz#65405a9d92cee68ac2d303002e0b8470a4d9ab1b"
  integrity sha1-ZUBanZLO5orC0wMALguEcKTZqxs=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

on-finished@2.4.1:
  version "2.4.1"
  resolved "http://r.npm.sankuai.com/on-finished/download/on-finished-2.4.1.tgz#58c8c44116e54845ad57f14ab10b03533184ac3f"
  integrity sha1-WMjEQRblSEWtV/FKsQsDUzGErD8=
  dependencies:
    ee-first "1.1.1"

on-finished@~2.3.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/on-finished/download/on-finished-2.3.0.tgz#20f1336481b083cd75337992a16971aa2d906947"
  integrity sha1-IPEzZIGwg811M3mSoWlxqi2QaUc=
  dependencies:
    ee-first "1.1.1"

on-headers@~1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/on-headers/download/on-headers-1.0.2.tgz#772b0ae6aaa525c399e489adfad90c403eb3c28f"
  integrity sha1-dysK5qqlJcOZ5Imt+tkMQD6zwo8=

once@^1.3.0, once@^1.3.1, once@^1.4.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/once/download/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

onetime@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/onetime/download/onetime-2.0.1.tgz#067428230fd67443b2794b22bba528b6867962d4"
  integrity sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ=
  dependencies:
    mimic-fn "^1.0.0"

onetime@^5.1.0, onetime@^5.1.2:
  version "5.1.2"
  resolved "http://r.npm.sankuai.com/onetime/download/onetime-5.1.2.tgz#d0e96ebb56b07476df1dd9c4806e5237985ca45e"
  integrity sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=
  dependencies:
    mimic-fn "^2.1.0"

open@^6.2.0:
  version "6.4.0"
  resolved "http://r.npm.sankuai.com/open/download/open-6.4.0.tgz#5c13e96d0dc894686164f18965ecfe889ecfc8a9"
  integrity sha1-XBPpbQ3IlGhhZPGJZez+iJ7PyKk=
  dependencies:
    is-wsl "^1.1.0"

opencollective-postinstall@^2.0.2:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/opencollective-postinstall/download/opencollective-postinstall-2.0.3.tgz#7a0fff978f6dbfa4d006238fbac98ed4198c3259"
  integrity sha1-eg//l49tv6TQBiOPusmO1BmMMlk=

optionator@^0.8.1:
  version "0.8.3"
  resolved "http://r.npm.sankuai.com/optionator/download/optionator-0.8.3.tgz#84fa1d036fe9d3c7e21d99884b601167ec8fb495"
  integrity sha1-hPodA2/p08fiHZmIS2ARZ+yPtJU=
  dependencies:
    deep-is "~0.1.3"
    fast-levenshtein "~2.0.6"
    levn "~0.3.0"
    prelude-ls "~1.1.2"
    type-check "~0.3.2"
    word-wrap "~1.2.3"

optionator@^0.9.1:
  version "0.9.1"
  resolved "http://r.npm.sankuai.com/optionator/download/optionator-0.9.1.tgz#4f236a6373dae0566a6d43e1326674f50c291499"
  integrity sha1-TyNqY3Pa4FZqbUPhMmZ09QwpFJk=
  dependencies:
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"
    word-wrap "^1.2.3"

options@>=0.0.5:
  version "0.0.6"
  resolved "http://r.npm.sankuai.com/options/download/options-0.0.6.tgz#ec22d312806bb53e731773e7cdaefcf1c643128f"
  integrity sha1-7CLTEoBrtT5zF3Pnza788cZDEo8=

ora@^3.4.0:
  version "3.4.0"
  resolved "http://r.npm.sankuai.com/ora/download/ora-3.4.0.tgz#bf0752491059a3ef3ed4c85097531de9fdbcd318"
  integrity sha1-vwdSSRBZo+8+1MhQl1Md6f280xg=
  dependencies:
    chalk "^2.4.2"
    cli-cursor "^2.1.0"
    cli-spinners "^2.0.0"
    log-symbols "^2.2.0"
    strip-ansi "^5.2.0"
    wcwidth "^1.0.1"

ora@^5.1.0, ora@^5.4.0, ora@^5.4.1:
  version "5.4.1"
  resolved "http://r.npm.sankuai.com/ora/download/ora-5.4.1.tgz#1b2678426af4ac4a509008e5e4ac9e9959db9e18"
  integrity sha1-GyZ4Qmr0rEpQkAjl5KyemVnbnhg=
  dependencies:
    bl "^4.1.0"
    chalk "^4.1.0"
    cli-cursor "^3.1.0"
    cli-spinners "^2.5.0"
    is-interactive "^1.0.0"
    is-unicode-supported "^0.1.0"
    log-symbols "^4.1.0"
    strip-ansi "^6.0.0"
    wcwidth "^1.0.1"

os-locale@^1.4.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/os-locale/download/os-locale-1.4.0.tgz#20f9f17ae29ed345e8bde583b13d2009803c14d9"
  integrity sha1-IPnxeuKe00XoveWDsT0gCYA8FNk=
  dependencies:
    lcid "^1.0.0"

os-tmpdir@^1.0.0, os-tmpdir@~1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/os-tmpdir/download/os-tmpdir-1.0.2.tgz#bbe67406c79aa85c5cfec766fe5734555dfa1274"
  integrity sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=

owner@^0.1.0:
  version "0.1.0"
  resolved "http://r.npm.sankuai.com/owner/download/owner-0.1.0.tgz#14d91146b445a110dd44ec23b5ba4af6c3cdbd64"
  integrity sha1-FNkRRrRFoRDdROwjtbpK9sPNvWQ=

p-cancelable@^0.4.0:
  version "0.4.1"
  resolved "http://r.npm.sankuai.com/p-cancelable/download/p-cancelable-0.4.1.tgz#35f363d67d52081c8d9585e37bcceb7e0bbcb2a0"
  integrity sha1-NfNj1n1SCByNlYXje8zrfgu8sqA=

p-each-series@^2.1.0:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/p-each-series/download/p-each-series-2.2.0.tgz#105ab0357ce72b202a8a8b94933672657b5e2a9a"
  integrity sha1-EFqwNXznKyAqiouUkzZyZXteKpo=

p-event@^2.1.0:
  version "2.3.1"
  resolved "http://r.npm.sankuai.com/p-event/download/p-event-2.3.1.tgz#596279ef169ab2c3e0cae88c1cfbb08079993ef6"
  integrity sha1-WWJ57xaassPgyuiMHPuwgHmZPvY=
  dependencies:
    p-timeout "^2.0.1"

p-finally@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/p-finally/download/p-finally-1.0.0.tgz#3fbcfb15b899a44123b34b6dcc18b724336a2cae"
  integrity sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4=

p-is-promise@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/p-is-promise/download/p-is-promise-1.1.0.tgz#9c9456989e9f6588017b0434d56097675c3da05e"
  integrity sha1-nJRWmJ6fZYgBewQ01WCXZ1w9oF4=

p-limit@^2.0.0, p-limit@^2.2.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/p-limit/download/p-limit-2.3.0.tgz#3dd33c647a214fdfffd835933eb086da0dc21db1"
  integrity sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=
  dependencies:
    p-try "^2.0.0"

p-limit@^3.0.2:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/p-limit/download/p-limit-3.1.0.tgz#e1daccbe78d0d1388ca18c64fea38e3e57e3706b"
  integrity sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=
  dependencies:
    yocto-queue "^0.1.0"

p-locate@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/p-locate/download/p-locate-3.0.0.tgz#322d69a05c0264b25997d9f40cd8a891ab0064a4"
  integrity sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=
  dependencies:
    p-limit "^2.0.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/p-locate/download/p-locate-4.1.0.tgz#a3428bb7088b3a60292f66919278b7c297ad4f07"
  integrity sha1-o0KLtwiLOmApL2aRkni3wpetTwc=
  dependencies:
    p-limit "^2.2.0"

p-locate@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/p-locate/download/p-locate-5.0.0.tgz#83c8315c6785005e3bd021839411c9e110e6d834"
  integrity sha1-g8gxXGeFAF470CGDlBHJ4RDm2DQ=
  dependencies:
    p-limit "^3.0.2"

p-map@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/p-map/download/p-map-4.0.0.tgz#bb2f95a5eda2ec168ec9274e06a747c3e2904d2b"
  integrity sha1-uy+Vpe2i7BaOySdOBqdHw+KQTSs=
  dependencies:
    aggregate-error "^3.0.0"

p-retry@4.2.0:
  version "4.2.0"
  resolved "http://r.npm.sankuai.com/p-retry/download/p-retry-4.2.0.tgz#ea9066c6b44f23cab4cd42f6147cdbbc6604da5d"
  integrity sha1-6pBmxrRPI8q0zUL2FHzbvGYE2l0=
  dependencies:
    "@types/retry" "^0.12.0"
    retry "^0.12.0"

p-timeout@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/p-timeout/download/p-timeout-2.0.1.tgz#d8dd1979595d2dc0139e1fe46b8b646cb3cdf038"
  integrity sha1-2N0ZeVldLcATnh/ka4tkbLPN8Dg=
  dependencies:
    p-finally "^1.0.0"

p-try@^2.0.0:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/p-try/download/p-try-2.2.0.tgz#cb2868540e313d61de58fafbe35ce9004d5540e6"
  integrity sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=

parent-module@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/parent-module/download/parent-module-1.0.1.tgz#691d2709e78c79fae3a156622452d00762caaaa2"
  integrity sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=
  dependencies:
    callsites "^3.0.0"

parse-json@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/parse-json/download/parse-json-4.0.0.tgz#be35f5425be1f7f6c747184f98a788cb99477ee0"
  integrity sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=
  dependencies:
    error-ex "^1.3.1"
    json-parse-better-errors "^1.0.1"

parse-json@^5.0.0:
  version "5.2.0"
  resolved "http://r.npm.sankuai.com/parse-json/download/parse-json-5.2.0.tgz#c76fc66dee54231c962b22bcc8a72cf2f99753cd"
  integrity sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

parse-node-version@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/parse-node-version/download/parse-node-version-1.0.1.tgz#e2b5dbede00e7fa9bc363607f53327e8b073189b"
  integrity sha1-4rXb7eAOf6m8NjYH9TMn6LBzGJs=

parse5@6.0.1:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/parse5/download/parse5-6.0.1.tgz#e1a1c085c569b3dc08321184f19a39cc27f7c30b"
  integrity sha1-4aHAhcVps9wIMhGE8Zo5zCf3wws=

parseurl@~1.3.3:
  version "1.3.3"
  resolved "http://r.npm.sankuai.com/parseurl/download/parseurl-1.3.3.tgz#9da19e7bee8d12dff0513ed5b76957793bc2e8d4"
  integrity sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=

pascalcase@^0.1.1:
  version "0.1.1"
  resolved "http://r.npm.sankuai.com/pascalcase/download/pascalcase-0.1.1.tgz#b363e55e8006ca6fe21784d2db22bd15d7917f14"
  integrity sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ=

path-exists@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/path-exists/download/path-exists-3.0.0.tgz#ce0ebeaa5f78cb18925ea7d810d7b59b010fd515"
  integrity sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=

path-exists@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/path-exists/download/path-exists-4.0.0.tgz#513bdbe2d3b95d7762e8c1137efa195c6c61b5b3"
  integrity sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-key@^2.0.0, path-key@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/path-key/download/path-key-2.0.1.tgz#411cadb574c5a140d3a4b1910d40d80cc9f40b40"
  integrity sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/path-key/download/path-key-3.1.1.tgz#581f6ade658cbba65a0d3380de7753295054f375"
  integrity sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=

path-parse@^1.0.7:
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/path-parse/download/path-parse-1.0.7.tgz#fbc114b60ca42b30d9daf5858e4bd68bbedb6735"
  integrity sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=

path-type@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/path-type/download/path-type-4.0.0.tgz#84ed01c0a7ba380afe09d90a8c180dcd9d03043b"
  integrity sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=

pend@~1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/pend/download/pend-1.2.0.tgz#7a57eb550a6783f9115331fcf4663d5c8e007a50"
  integrity sha1-elfrVQpng/kRUzH89GY9XI4AelA=

picocolors@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/picocolors/download/picocolors-1.0.0.tgz#cb5bdc74ff3f51892236eaf79d68bc44564ab81c"
  integrity sha1-y1vcdP8/UYkiNur3nWi8RFZKuBw=

picomatch@^2.0.4, picomatch@^2.3.1:
  version "2.3.1"
  resolved "http://r.npm.sankuai.com/picomatch/download/picomatch-2.3.1.tgz#3ba3833733646d9d3e4995946c1365a67fb07a42"
  integrity sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=

pify@^2.3.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/pify/download/pify-2.3.0.tgz#ed141a6ac043a849ea588498e7dca8b15330e90c"
  integrity sha1-7RQaasBDqEnqWISY59yosVMw6Qw=

pify@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/pify/download/pify-3.0.0.tgz#e5a4acd2c101fdf3d9a4d07f0dbc4db49dd28176"
  integrity sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=

pify@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/pify/download/pify-4.0.1.tgz#4b2cd25c50d598735c50292224fd8c6df41e3231"
  integrity sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE=

pinkie-promise@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/pinkie-promise/download/pinkie-promise-2.0.1.tgz#2135d6dfa7a358c069ac9b178776288228450ffa"
  integrity sha1-ITXW36ejWMBprJsXh3YogihFD/o=
  dependencies:
    pinkie "^2.0.0"

pinkie@^2.0.0:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/pinkie/download/pinkie-2.0.4.tgz#72556b80cfa0d48a974e80e77248e80ed4f7f870"
  integrity sha1-clVrgM+g1IqXToDnckjoDtT3+HA=

pirates@^4.0.1, pirates@^4.0.5:
  version "4.0.5"
  resolved "http://r.npm.sankuai.com/pirates/download/pirates-4.0.5.tgz#feec352ea5c3268fb23a37c702ab1699f35a5f3b"
  integrity sha1-/uw1LqXDJo+yOjfHAqsWmfNaXzs=

pkg-dir@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/pkg-dir/download/pkg-dir-3.0.0.tgz#2749020f239ed990881b1f71210d51eb6523bea3"
  integrity sha1-J0kCDyOe2ZCIGx9xIQ1R62UjvqM=
  dependencies:
    find-up "^3.0.0"

pkg-dir@^4.2.0:
  version "4.2.0"
  resolved "http://r.npm.sankuai.com/pkg-dir/download/pkg-dir-4.2.0.tgz#f099133df7ede422e81d1d8448270eeb3e4261f3"
  integrity sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=
  dependencies:
    find-up "^4.0.0"

pkg-dir@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/pkg-dir/download/pkg-dir-5.0.0.tgz#a02d6aebe6ba133a928f74aec20bafdfe6b8e760"
  integrity sha1-oC1q6+a6EzqSj3Suwguv3+a452A=
  dependencies:
    find-up "^5.0.0"

pkg-up@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/pkg-up/download/pkg-up-3.1.0.tgz#100ec235cc150e4fd42519412596a28512a0def5"
  integrity sha1-EA7CNcwVDk/UJRlBJZaihRKg3vU=
  dependencies:
    find-up "^3.0.0"

please-upgrade-node@^3.2.0:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/please-upgrade-node/download/please-upgrade-node-3.2.0.tgz#aeddd3f994c933e4ad98b99d9a556efa0e2fe942"
  integrity sha1-rt3T+ZTJM+StmLmdmlVu+g4v6UI=
  dependencies:
    semver-compare "^1.0.0"

plist@^3.0.1, plist@^3.0.5:
  version "3.0.6"
  resolved "http://r.npm.sankuai.com/plist/download/plist-3.0.6.tgz#7cfb68a856a7834bca6dbfe3218eb9c7740145d3"
  integrity sha1-fPtoqFang0vKbb/jIY65x3QBRdM=
  dependencies:
    base64-js "^1.5.1"
    xmlbuilder "^15.1.1"

plugin-error@^0.1.2:
  version "0.1.2"
  resolved "http://r.npm.sankuai.com/plugin-error/download/plugin-error-0.1.2.tgz#3b9bb3335ccf00f425e07437e19276967da47ace"
  integrity sha1-O5uzM1zPAPQl4HQ34ZJ2ln2kes4=
  dependencies:
    ansi-cyan "^0.1.1"
    ansi-red "^0.1.1"
    arr-diff "^1.0.1"
    arr-union "^2.0.1"
    extend-shallow "^1.1.2"

portfinder@^1.0.28:
  version "1.0.32"
  resolved "http://r.npm.sankuai.com/portfinder/download/portfinder-1.0.32.tgz#2fe1b9e58389712429dc2bea5beb2146146c7f81"
  integrity sha1-L+G55YOJcSQp3CvqW+shRhRsf4E=
  dependencies:
    async "^2.6.4"
    debug "^3.2.7"
    mkdirp "^0.5.6"

posix-character-classes@^0.1.0:
  version "0.1.1"
  resolved "http://r.npm.sankuai.com/posix-character-classes/download/posix-character-classes-0.1.1.tgz#01eac0fe3b5af71a2a6c02feabb8c1fef7e00eab"
  integrity sha1-AerA/jta9xoqbAL+q7jB/vfgDqs=

possible-typed-array-names@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/possible-typed-array-names/download/possible-typed-array-names-1.0.0.tgz#89bb63c6fada2c3e90adc4a647beeeb39cc7bf8f"
  integrity sha1-ibtjxvraLD6QrcSmR77us5zHv48=

postcss-value-parser@^4.0.2:
  version "4.2.0"
  resolved "http://r.npm.sankuai.com/postcss-value-parser/download/postcss-value-parser-4.2.0.tgz#723c09920836ba6d3e5af019f92bc0971c02e514"
  integrity sha1-cjwJkgg2um0+WvAZ+SvAlxwC5RQ=

prelude-ls@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/prelude-ls/download/prelude-ls-1.2.1.tgz#debc6489d7a6e6b0e7611888cec880337d316396"
  integrity sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=

prelude-ls@~1.1.2:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/prelude-ls/download/prelude-ls-1.1.2.tgz#21932a549f5e52ffd9a827f570e04be62a97da54"
  integrity sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ=

prepend-http@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/prepend-http/download/prepend-http-2.0.0.tgz#e92434bfa5ea8c19f41cdfd401d741a3c819d897"
  integrity sha1-6SQ0v6XqjBn0HN/UAddBo8gZ2Jc=

prettier-linter-helpers@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/prettier-linter-helpers/download/prettier-linter-helpers-1.0.0.tgz#d23d41fe1375646de2d0104d3454a3008802cf7b"
  integrity sha1-0j1B/hN1ZG3i0BBNNFSjAIgCz3s=
  dependencies:
    fast-diff "^1.1.2"

prettier@^2.0.2:
  version "2.8.7"
  resolved "http://r.npm.sankuai.com/prettier/download/prettier-2.8.7.tgz#bb79fc8729308549d28fe3a98fce73d2c0656450"
  integrity sha1-u3n8hykwhUnSj+Opj85z0sBlZFA=

pretty-format@^24.9.0:
  version "24.9.0"
  resolved "http://r.npm.sankuai.com/pretty-format/download/pretty-format-24.9.0.tgz#12fac31b37019a4eea3c11aa9a959eb7628aa7c9"
  integrity sha1-EvrDGzcBmk7qPBGqmpWet2KKp8k=
  dependencies:
    "@jest/types" "^24.9.0"
    ansi-regex "^4.0.0"
    ansi-styles "^3.2.0"
    react-is "^16.8.4"

pretty-format@^25.1.0, pretty-format@^25.2.0:
  version "25.5.0"
  resolved "http://r.npm.sankuai.com/pretty-format/download/pretty-format-25.5.0.tgz#7873c1d774f682c34b8d48b6743a2bf2ac55791a"
  integrity sha1-eHPB13T2gsNLjUi2dDor8qxVeRo=
  dependencies:
    "@jest/types" "^25.5.0"
    ansi-regex "^5.0.0"
    ansi-styles "^4.0.0"
    react-is "^16.12.0"

pretty-format@^26.0.0, pretty-format@^26.6.2:
  version "26.6.2"
  resolved "http://r.npm.sankuai.com/pretty-format/download/pretty-format-26.6.2.tgz#e35c2705f14cb7fe2fe94fa078345b444120fc93"
  integrity sha1-41wnBfFMt/4v6U+geDRbREEg/JM=
  dependencies:
    "@jest/types" "^26.6.2"
    ansi-regex "^5.0.0"
    ansi-styles "^4.0.0"
    react-is "^17.0.1"

pretty-format@^29.7.0:
  version "29.7.0"
  resolved "http://r.npm.sankuai.com/pretty-format/download/pretty-format-29.7.0.tgz#ca42c758310f365bfa71a0bda0a807160b776812"
  integrity sha1-ykLHWDEPNlv6caC9oKgHFgt3aBI=
  dependencies:
    "@jest/schemas" "^29.6.3"
    ansi-styles "^5.0.0"
    react-is "^18.0.0"

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/process-nextick-args/download/process-nextick-args-2.0.1.tgz#7820d9b16120cc55ca9ae7792680ae7dba6d7fe2"
  integrity sha1-eCDZsWEgzFXKmud5JoCufbptf+I=

progress@^2.0.0:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/progress/download/progress-2.0.3.tgz#7e8cf8d8f5b8f239c1bc68beb4eb78567d572ef8"
  integrity sha1-foz42PW48jnBvGi+tOt4Vn1XLvg=

promise@^7.1.1:
  version "7.3.1"
  resolved "http://r.npm.sankuai.com/promise/download/promise-7.3.1.tgz#064b72602b18f90f29192b8b1bc418ffd1ebd3bf"
  integrity sha1-BktyYCsY+Q8pGSuLG8QY/9Hr078=
  dependencies:
    asap "~2.0.3"

prompts@^2.0.1:
  version "2.4.2"
  resolved "http://r.npm.sankuai.com/prompts/download/prompts-2.4.2.tgz#7b57e73b3a48029ad10ebd44f74b01722a4cb069"
  integrity sha1-e1fnOzpIAprRDr1E90sBcipMsGk=
  dependencies:
    kleur "^3.0.3"
    sisteransi "^1.0.5"

prop-types@^15.5.10, prop-types@^15.6.2, prop-types@^15.7.2, prop-types@^15.8.1:
  version "15.8.1"
  resolved "http://r.npm.sankuai.com/prop-types/download/prop-types-15.8.1.tgz#67d87bf1a694f48435cf332c24af10214a3140b5"
  integrity sha1-Z9h78aaU9IQ1zzMsJK8QIUoxQLU=
  dependencies:
    loose-envify "^1.4.0"
    object-assign "^4.1.1"
    react-is "^16.13.1"

pseudomap@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/pseudomap/download/pseudomap-1.0.2.tgz#f052a28da70e618917ef0a8ac34c1ae5a68286b3"
  integrity sha1-8FKijacOYYkX7wqKw0wa5aaChrM=

psl@^1.1.33:
  version "1.9.0"
  resolved "http://r.npm.sankuai.com/psl/download/psl-1.9.0.tgz#d0df2a137f00794565fcaf3b2c00cd09f8d5a5a7"
  integrity sha1-0N8qE38AeUVl/K87LADNCfjVpac=

pump@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/pump/download/pump-3.0.0.tgz#b4a2116815bde2f4e1ea602354e8c75565107a64"
  integrity sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ=
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

punycode@^2.1.0, punycode@^2.1.1:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/punycode/download/punycode-2.3.0.tgz#f67fa67c94da8f4d0cfff981aee4118064199b8f"
  integrity sha1-9n+mfJTaj00M//mBruQRgGQZm48=

qrcode-terminal@^0.12.0:
  version "0.12.0"
  resolved "http://r.npm.sankuai.com/qrcode-terminal/download/qrcode-terminal-0.12.0.tgz#bb5b699ef7f9f0505092a3748be4464fe71b5819"
  integrity sha1-u1tpnvf58FBQkqN0i+RGT+cbWBk=

query-string@^5.0.1:
  version "5.1.1"
  resolved "http://r.npm.sankuai.com/query-string/download/query-string-5.1.1.tgz#a78c012b71c17e05f2e3fa2319dd330682efb3cb"
  integrity sha1-p4wBK3HBfgXy4/ojGd0zBoLvs8s=
  dependencies:
    decode-uri-component "^0.2.0"
    object-assign "^4.1.0"
    strict-uri-encode "^1.0.0"

query-string@^7.1.1, query-string@^7.1.3:
  version "7.1.3"
  resolved "http://r.npm.sankuai.com/query-string/download/query-string-7.1.3.tgz#a1cf90e994abb113a325804a972d98276fe02328"
  integrity sha1-oc+Q6ZSrsROjJYBKly2YJ2/gIyg=
  dependencies:
    decode-uri-component "^0.2.2"
    filter-obj "^1.1.0"
    split-on-first "^1.0.0"
    strict-uri-encode "^2.0.0"

querystring@^0.2.0:
  version "0.2.1"
  resolved "http://r.npm.sankuai.com/querystring/download/querystring-0.2.1.tgz#40d77615bb09d16902a85c3e38aa8b5ed761c2dd"
  integrity sha1-QNd2FbsJ0WkCqFw+OKqLXtdhwt0=

querystringify@^2.1.1:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/querystringify/download/querystringify-2.2.0.tgz#3345941b4153cb9d082d8eee4cda2016a9aef7f6"
  integrity sha1-M0WUG0FTy50ILY7uTNogFqmu9/Y=

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "http://r.npm.sankuai.com/queue-microtask/download/queue-microtask-1.2.3.tgz#4929228bbc724dfac43e0efb058caf7b6cfb6243"
  integrity sha1-SSkii7xyTfrEPg77BYyve2z7YkM=

range-parser@~1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/range-parser/download/range-parser-1.2.1.tgz#3cf37023d199e1c24d1a55b84800c2f3e6468031"
  integrity sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=

react-devtools-core@^4.6.0:
  version "4.27.2"
  resolved "http://r.npm.sankuai.com/react-devtools-core/download/react-devtools-core-4.27.2.tgz#d20fc57e258c656eedabafc2c851d38b33583148"
  integrity sha1-0g/FfiWMZW7tq6/CyFHTizNYMUg=
  dependencies:
    shell-quote "^1.6.1"
    ws "^7"

react-is@^16.12.0, react-is@^16.13.0, react-is@^16.13.1, react-is@^16.7.0, react-is@^16.8.4, react-is@^16.8.6, react-is@^16.9.0:
  version "16.13.1"
  resolved "http://r.npm.sankuai.com/react-is/download/react-is-16.13.1.tgz#789729a4dc36de2999dc156dd6c1d9c18cea56a4"
  integrity sha1-eJcppNw23imZ3BVt1sHZwYzqVqQ=

react-is@^17.0.1:
  version "17.0.2"
  resolved "http://r.npm.sankuai.com/react-is/download/react-is-17.0.2.tgz#e691d4a8e9c789365655539ab372762b0efb54f0"
  integrity sha1-5pHUqOnHiTZWVVOas3J2Kw77VPA=

react-is@^18.0.0:
  version "18.3.1"
  resolved "http://r.npm.sankuai.com/react-is/download/react-is-18.3.1.tgz#e83557dc12eae63a99e003a46388b1dcbb44db7e"
  integrity sha1-6DVX3BLq5jqZ4AOkY4ix3LtE234=

react-native-communications@2.2.1, react-native-communications@^2.2.1:
  version "2.2.1"
  resolved "http://r.npm.sankuai.com/react-native-communications/download/react-native-communications-2.2.1.tgz#7883b56b20a002eeb790c113f8616ea8692ca795"
  integrity sha1-eIO1ayCgAu63kMET+GFuqGksp5U=

react-native-device-info@^0.21.5:
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/react-native-device-info/download/react-native-device-info-0.21.5.tgz#99478a2d68182e012297f2d63f2bd1b788106dee"
  integrity sha1-mUeKLWgYLgEil/LWPyvRt4gQbe4=

react-native-draggable-flatlist@1:
  version "1.1.9"
  resolved "http://r.npm.sankuai.com/react-native-draggable-flatlist/download/react-native-draggable-flatlist-1.1.9.tgz#b8f58122f639841c83aa1d913b2e3e9f9507bfe5"
  integrity sha1-uPWBIvY5hByDqh2ROy4+n5UHv+U=

react-native-fit-image@^1.5.5:
  version "1.5.5"
  resolved "http://r.npm.sankuai.com/react-native-fit-image/download/react-native-fit-image-1.5.5.tgz#c660d1ad74b9dcaa1cba27a0d9c23837e000226c"
  integrity sha1-xmDRrXS53Kocuieg2cI4N+AAImw=
  dependencies:
    prop-types "^15.5.10"

react-native-image-pan-zoom@^2.1.12:
  version "2.1.12"
  resolved "http://r.npm.sankuai.com/react-native-image-pan-zoom/download/react-native-image-pan-zoom-2.1.12.tgz#eb98bf56fb5610379bdbfdb63219cc1baca98fd2"
  integrity sha1-65i/VvtWEDeb2/22MhnMG6ypj9I=

react-native-image-zoom-viewer@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/react-native-image-zoom-viewer/download/react-native-image-zoom-viewer-3.0.1.tgz#a2bd5fb3bda15e0686ce88fcde8576726495d7fb"
  integrity sha1-or1fs72hXgaGzoj83oV2cmSV1/s=
  dependencies:
    react-native-image-pan-zoom "^2.1.12"

react-native-keyboard-manager@^6.5.16-0:
  version "6.5.16-0"
  resolved "http://r.npm.sankuai.com/react-native-keyboard-manager/download/react-native-keyboard-manager-6.5.16-0.tgz#ead536f01ade296f483cbf3ecaf3026cff702c9c"
  integrity sha1-6tU28BreKW9IPL8+yvMCbP9wLJw=

react-native-markdown-display@7:
  version "7.0.2"
  resolved "http://r.npm.sankuai.com/react-native-markdown-display/download/react-native-markdown-display-7.0.2.tgz#b6584cec8d6670c0141fb8780bc2f0710188a4c2"
  integrity sha1-tlhM7I1mcMAUH7h4C8LwcQGIpMI=
  dependencies:
    css-to-react-native "^3.0.0"
    markdown-it "^10.0.0"
    prop-types "^15.7.2"
    react-native-fit-image "^1.5.5"

react-native-orientation@^3.1.3:
  version "3.1.3"
  resolved "http://r.npm.sankuai.com/react-native-orientation/download/react-native-orientation-3.1.3.tgz#d45803841fe94b6cce9acbe904fd5ca191a3711e"
  integrity sha1-1FgDhB/pS2zOmsvpBP1coZGjcR4=

react-native-permissions@^3.2.0:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/react-native-permissions/download/react-native-permissions-3.2.0.tgz#2199a49e96bac633016955d36142032d747d0d3e"
  integrity sha1-IZmknpa6xjMBaVXTYUIDLXR9DT4=

react-native-safe-area-context@3.4.1:
  version "3.4.1"
  resolved "http://r.npm.sankuai.com/react-native-safe-area-context/download/react-native-safe-area-context-3.4.1.tgz#c967a52903d55fe010b2428e5368b42f1debc0a7"
  integrity sha1-yWelKQPVX+AQskKOU2i0Lx3rwKc=

react-native-screens@3.5.0:
  version "3.5.0"
  resolved "http://r.npm.sankuai.com/react-native-screens/download/react-native-screens-3.5.0.tgz#c40be78dff8e2dff1b00ba8fa670b2e429e632d2"
  integrity sha1-xAvnjf+OLf8bALqPpnCy5CnmMtI=
  dependencies:
    warn-once "^0.1.0"

react-native-svg@12.1.0:
  version "12.1.0"
  resolved "http://r.npm.sankuai.com/react-native-svg/download/react-native-svg-12.1.0.tgz#acfe48c35cd5fca3d5fd767abae0560c36cfc03d"
  integrity sha1-rP5Iw1zV/KPV/XZ6uuBWDDbPwD0=
  dependencies:
    css-select "^2.1.0"
    css-tree "^1.0.0-alpha.39"

react-native-webview@^10.9.0:
  version "10.10.2"
  resolved "http://r.npm.sankuai.com/react-native-webview/download/react-native-webview-10.10.2.tgz#13208aef17c9ccfd28355363688f4cbe2143f10a"
  integrity sha1-EyCK7xfJzP0oNVNjaI9MviFD8Qo=
  dependencies:
    escape-string-regexp "2.0.0"
    invariant "2.2.4"

react-redux@7.2.1:
  version "7.2.1"
  resolved "http://r.npm.sankuai.com/react-redux/download/react-redux-7.2.1.tgz#8dedf784901014db2feca1ab633864dee68ad985"
  integrity sha1-je33hJAQFNsv7KGrYzhk3uaK2YU=
  dependencies:
    "@babel/runtime" "^7.5.5"
    hoist-non-react-statics "^3.3.0"
    loose-envify "^1.4.0"
    prop-types "^15.7.2"
    react-is "^16.9.0"

react-refresh@^0.4.0:
  version "0.4.3"
  resolved "http://r.npm.sankuai.com/react-refresh/download/react-refresh-0.4.3.tgz#966f1750c191672e76e16c2efa569150cc73ab53"
  integrity sha1-lm8XUMGRZy524Wwu+laRUMxzq1M=

react-test-renderer@~16.13.1:
  version "16.13.1"
  resolved "http://r.npm.sankuai.com/react-test-renderer/download/react-test-renderer-16.13.1.tgz#de25ea358d9012606de51e012d9742e7f0deabc1"
  integrity sha1-3iXqNY2QEmBt5R4BLZdC5/Deq8E=
  dependencies:
    object-assign "^4.1.1"
    prop-types "^15.6.2"
    react-is "^16.8.6"
    scheduler "^0.19.1"

react@16.13.1:
  version "16.13.1"
  resolved "http://r.npm.sankuai.com/react/download/react-16.13.1.tgz#2e818822f1a9743122c063d6410d85c1e3afe48e"
  integrity sha1-LoGIIvGpdDEiwGPWQQ2FweOv5I4=
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"
    prop-types "^15.6.2"

read-pkg-up@^7.0.1:
  version "7.0.1"
  resolved "http://r.npm.sankuai.com/read-pkg-up/download/read-pkg-up-7.0.1.tgz#f3a6135758459733ae2b95638056e1854e7ef507"
  integrity sha1-86YTV1hFlzOuK5VjgFbhhU5+9Qc=
  dependencies:
    find-up "^4.1.0"
    read-pkg "^5.2.0"
    type-fest "^0.8.1"

read-pkg@^5.2.0:
  version "5.2.0"
  resolved "http://r.npm.sankuai.com/read-pkg/download/read-pkg-5.2.0.tgz#7bf295438ca5a33e56cd30e053b34ee7250c93cc"
  integrity sha1-e/KVQ4yloz5WzTDgU7NO5yUMk8w=
  dependencies:
    "@types/normalize-package-data" "^2.4.0"
    normalize-package-data "^2.5.0"
    parse-json "^5.0.0"
    type-fest "^0.6.0"

readable-stream@^2.0.0, readable-stream@^2.0.1, readable-stream@^2.0.5, readable-stream@^2.2.2, readable-stream@^2.3.0, readable-stream@^2.3.5, readable-stream@~2.3.6:
  version "2.3.8"
  resolved "http://r.npm.sankuai.com/readable-stream/download/readable-stream-2.3.8.tgz#91125e8042bba1b9887f49345f6277027ce8be9b"
  integrity sha1-kRJegEK7obmIf0k0X2J3Anzovps=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^3.1.1, readable-stream@^3.4.0, readable-stream@^3.6.0:
  version "3.6.2"
  resolved "http://r.npm.sankuai.com/readable-stream/download/readable-stream-3.6.2.tgz#56a9b36ea965c00c5a93ef31eb111a0f11056967"
  integrity sha1-VqmzbqllwAxak+8x6xEaDxEFaWc=
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readdir-glob@^1.0.0:
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/readdir-glob/download/readdir-glob-1.1.3.tgz#c3d831f51f5e7bfa62fa2ffbe4b508c640f09584"
  integrity sha1-w9gx9R9ee/pi+i/75LUIxkDwlYQ=
  dependencies:
    minimatch "^5.1.0"

redent@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/redent/download/redent-3.0.0.tgz#e557b7998316bb53c9f1f56fa626352c6963059f"
  integrity sha1-5Ve3mYMWu1PJ8fVvpiY1LGljBZ8=
  dependencies:
    indent-string "^4.0.0"
    strip-indent "^3.0.0"

redux@^4.0.0, redux@^4.0.5:
  version "4.2.1"
  resolved "http://r.npm.sankuai.com/redux/download/redux-4.2.1.tgz#c08f4306826c49b5e9dc901dee0452ea8fce6197"
  integrity sha1-wI9DBoJsSbXp3JAd7gRS6o/OYZc=
  dependencies:
    "@babel/runtime" "^7.9.2"

regenerate-unicode-properties@^10.1.0:
  version "10.1.0"
  resolved "http://r.npm.sankuai.com/regenerate-unicode-properties/download/regenerate-unicode-properties-10.1.0.tgz#7c3192cab6dd24e21cb4461e5ddd7dd24fa8374c"
  integrity sha1-fDGSyrbdJOIctEYeXd190k+oN0w=
  dependencies:
    regenerate "^1.4.2"

regenerate@^1.4.2:
  version "1.4.2"
  resolved "http://r.npm.sankuai.com/regenerate/download/regenerate-1.4.2.tgz#b9346d8827e8f5a32f7ba29637d398b69014848a"
  integrity sha1-uTRtiCfo9aMve6KWN9OYtpAUhIo=

regenerator-runtime@^0.13.11:
  version "0.13.11"
  resolved "http://r.npm.sankuai.com/regenerator-runtime/download/regenerator-runtime-0.13.11.tgz#f6dca3e7ceec20590d07ada785636a90cdca17f9"
  integrity sha1-9tyj587sIFkNB62nhWNqkM3KF/k=

regenerator-runtime@^0.14.0:
  version "0.14.0"
  resolved "http://r.npm.sankuai.com/regenerator-runtime/download/regenerator-runtime-0.14.0.tgz#5e19d68eb12d486f797e15a3c6a918f7cec5eb45"
  integrity sha1-XhnWjrEtSG95fhWjxqkY987F60U=

regenerator-transform@^0.15.1:
  version "0.15.1"
  resolved "http://r.npm.sankuai.com/regenerator-transform/download/regenerator-transform-0.15.1.tgz#f6c4e99fc1b4591f780db2586328e4d9a9d8dc56"
  integrity sha1-9sTpn8G0WR94DbJYYyjk2anY3FY=
  dependencies:
    "@babel/runtime" "^7.8.4"

regex-not@^1.0.0, regex-not@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/regex-not/download/regex-not-1.0.2.tgz#1f4ece27e00b0b65e0247a6810e6a85d83a5752c"
  integrity sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw=
  dependencies:
    extend-shallow "^3.0.2"
    safe-regex "^1.1.0"

regexp.prototype.flags@^1.4.3:
  version "1.4.3"
  resolved "http://r.npm.sankuai.com/regexp.prototype.flags/download/regexp.prototype.flags-1.4.3.tgz#87cab30f80f66660181a3bb7bf5981a872b367ac"
  integrity sha1-h8qzD4D2ZmAYGju3v1mBqHKzZ6w=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    functions-have-names "^1.2.2"

regexp.prototype.flags@^1.5.2:
  version "1.5.2"
  resolved "http://r.npm.sankuai.com/regexp.prototype.flags/download/regexp.prototype.flags-1.5.2.tgz#138f644a3350f981a858c44f6bb1a61ff59be334"
  integrity sha1-E49kSjNQ+YGoWMRPa7GmH/Wb4zQ=
  dependencies:
    call-bind "^1.0.6"
    define-properties "^1.2.1"
    es-errors "^1.3.0"
    set-function-name "^2.0.1"

regexpp@^3.0.0, regexpp@^3.1.0:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/regexpp/download/regexpp-3.2.0.tgz#0425a2768d8f23bad70ca4b90461fa2f1213e1b2"
  integrity sha1-BCWido2PI7rXDKS5BGH6LxIT4bI=

regexpu-core@^5.3.1:
  version "5.3.2"
  resolved "http://r.npm.sankuai.com/regexpu-core/download/regexpu-core-5.3.2.tgz#11a2b06884f3527aec3e93dbbf4a3b958a95546b"
  integrity sha1-EaKwaITzUnrsPpPbv0o7lYqVVGs=
  dependencies:
    "@babel/regjsgen" "^0.8.0"
    regenerate "^1.4.2"
    regenerate-unicode-properties "^10.1.0"
    regjsparser "^0.9.1"
    unicode-match-property-ecmascript "^2.0.0"
    unicode-match-property-value-ecmascript "^2.1.0"

regjsparser@^0.9.1:
  version "0.9.1"
  resolved "http://r.npm.sankuai.com/regjsparser/download/regjsparser-0.9.1.tgz#272d05aa10c7c1f67095b1ff0addae8442fc5709"
  integrity sha1-Jy0FqhDHwfZwlbH/Ct2uhEL8Vwk=
  dependencies:
    jsesc "~0.5.0"

remove-trailing-separator@^1.0.1:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/remove-trailing-separator/download/remove-trailing-separator-1.1.0.tgz#c24bce2a283adad5bc3f58e0d48249b92379d8ef"
  integrity sha1-wkvOKig62tW8P1jg1IJJuSN52O8=

repeat-element@^1.1.2:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/repeat-element/download/repeat-element-1.1.4.tgz#be681520847ab58c7568ac75fbfad28ed42d39e9"
  integrity sha1-vmgVIIR6tYx1aKx1+/rSjtQtOek=

repeat-string@^1.6.1:
  version "1.6.1"
  resolved "http://r.npm.sankuai.com/repeat-string/download/repeat-string-1.6.1.tgz#8dcae470e1c88abc2d600fff4a776286da75e637"
  integrity sha1-jcrkcOHIirwtYA//Sndihtp15jc=

require-directory@^2.1.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/require-directory/download/require-directory-2.1.1.tgz#8c64ad5fd30dab1c976e2344ffe7f792a6a6df42"
  integrity sha1-jGStX9MNqxyXbiNE/+f3kqam30I=

require-from-string@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/require-from-string/download/require-from-string-2.0.2.tgz#89a7fdd938261267318eafe14f9c32e598c36909"
  integrity sha1-iaf92TgmEmcxjq/hT5wy5ZjDaQk=

require-main-filename@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/require-main-filename/download/require-main-filename-2.0.0.tgz#d0b329ecc7cc0f61649f62215be69af54aa8989b"
  integrity sha1-0LMp7MfMD2Fkn2IhW+aa9UqomJs=

requires-port@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/requires-port/download/requires-port-1.0.0.tgz#925d2601d39ac485e091cf0da5c6e694dc3dcaff"
  integrity sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=

reselect@^4.0.0:
  version "4.1.7"
  resolved "http://r.npm.sankuai.com/reselect/download/reselect-4.1.7.tgz#56480d9ff3d3188970ee2b76527bd94a95567a42"
  integrity sha1-VkgNn/PTGIlw7it2UnvZSpVWekI=

resize-observer-polyfill@^1.5.1:
  version "1.5.1"
  resolved "http://r.npm.sankuai.com/resize-observer-polyfill/download/resize-observer-polyfill-1.5.1.tgz#0e9020dd3d21024458d4ebd27e23e40269810464"
  integrity sha1-DpAg3T0hAkRY1OvSfiPkAmmBBGQ=

resolve-cwd@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/resolve-cwd/download/resolve-cwd-3.0.0.tgz#0f0075f1bb2544766cf73ba6a6e2adfebcb13f2d"
  integrity sha1-DwB18bslRHZs9zumpuKt/ryxPy0=
  dependencies:
    resolve-from "^5.0.0"

resolve-from@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/resolve-from/download/resolve-from-3.0.0.tgz#b22c7af7d9d6881bc8b6e653335eebcb0a188748"
  integrity sha1-six699nWiBvItuZTM17rywoYh0g=

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/resolve-from/download/resolve-from-4.0.0.tgz#4abcd852ad32dd7baabfe9b40e00a36db5f392e6"
  integrity sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=

resolve-from@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/resolve-from/download/resolve-from-5.0.0.tgz#c35225843df8f776df21c57557bc087e9dfdfc69"
  integrity sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=

resolve-url@^0.2.1:
  version "0.2.1"
  resolved "http://r.npm.sankuai.com/resolve-url/download/resolve-url-0.2.1.tgz#2c637fe77c893afd2a663fe21aa9080068e2052a"
  integrity sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=

resolve@^1.10.0, resolve@^1.12.0, resolve@^1.13.1, resolve@^1.14.2, resolve@^1.18.1, resolve@^1.5.0:
  version "1.22.2"
  resolved "http://r.npm.sankuai.com/resolve/download/resolve-1.22.2.tgz#0ed0943d4e301867955766c9f3e1ae6d01c6845f"
  integrity sha1-DtCUPU4wGGeVV2bJ8+GubQHGhF8=
  dependencies:
    is-core-module "^2.11.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

resolve@^1.22.4:
  version "1.22.8"
  resolved "http://r.npm.sankuai.com/resolve/download/resolve-1.22.8.tgz#b6c87a9f2aa06dfab52e3d70ac8cde321fa5a48d"
  integrity sha1-tsh6nyqgbfq1Lj1wrIzeMh+lpI0=
  dependencies:
    is-core-module "^2.13.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

resolve@^2.0.0-next.4:
  version "2.0.0-next.4"
  resolved "http://r.npm.sankuai.com/resolve/download/resolve-2.0.0-next.4.tgz#3d37a113d6429f496ec4752d2a2e58efb1fd4660"
  integrity sha1-PTehE9ZCn0luxHUtKi5Y77H9RmA=
  dependencies:
    is-core-module "^2.9.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

responselike@1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/responselike/download/responselike-1.0.2.tgz#918720ef3b631c5642be068f15ade5a46f4ba1e7"
  integrity sha1-kYcg7ztjHFZCvgaPFa3lpG9Loec=
  dependencies:
    lowercase-keys "^1.0.0"

restore-cursor@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/restore-cursor/download/restore-cursor-2.0.0.tgz#9f7ee287f82fd326d4fd162923d62129eee0dfaf"
  integrity sha1-n37ih/gv0ybU/RYpI9YhKe7g368=
  dependencies:
    onetime "^2.0.0"
    signal-exit "^3.0.2"

restore-cursor@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/restore-cursor/download/restore-cursor-3.1.0.tgz#39f67c54b3a7a58cea5236d95cf0034239631f7e"
  integrity sha1-OfZ8VLOnpYzqUjbZXPADQjljH34=
  dependencies:
    onetime "^5.1.0"
    signal-exit "^3.0.2"

ret@~0.1.10:
  version "0.1.15"
  resolved "http://r.npm.sankuai.com/ret/download/ret-0.1.15.tgz#b8a4825d5bdb1fc3f6f53c2bc33f81388681c7bc"
  integrity sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w=

retry@^0.12.0:
  version "0.12.0"
  resolved "http://r.npm.sankuai.com/retry/download/retry-0.12.0.tgz#1b42a6266a21f07421d1b0b54b7dc167b01c013b"
  integrity sha1-G0KmJmoh8HQh0bC1S33BZ7AcATs=

reusify@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/reusify/download/reusify-1.0.4.tgz#90da382b1e126efc02146e90845a88db12925d76"
  integrity sha1-kNo4Kx4SbvwCFG6QhFqI2xKSXXY=

rfdc@^1.3.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/rfdc/download/rfdc-1.3.0.tgz#d0b7c441ab2720d05dc4cf26e01c89631d9da08b"
  integrity sha1-0LfEQasnINBdxM8m4ByJYx2doIs=

rimraf@^2.5.4:
  version "2.7.1"
  resolved "http://r.npm.sankuai.com/rimraf/download/rimraf-2.7.1.tgz#35797f13a7fdadc566142c29d4f07ccad483e3ec"
  integrity sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w=
  dependencies:
    glob "^7.1.3"

rimraf@^3.0.0, rimraf@^3.0.2:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/rimraf/download/rimraf-3.0.2.tgz#f1a5402ba6220ad52cc1282bac1ae3aa49fd061a"
  integrity sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=
  dependencies:
    glob "^7.1.3"

rimraf@~2.2.6:
  version "2.2.8"
  resolved "http://r.npm.sankuai.com/rimraf/download/rimraf-2.2.8.tgz#e439be2aaee327321952730f99a8929e4fc50582"
  integrity sha1-5Dm+Kq7jJzIZUnMPmaiSnk/FBYI=

rsvp@^4.8.4:
  version "4.8.5"
  resolved "http://r.npm.sankuai.com/rsvp/download/rsvp-4.8.5.tgz#c8f155311d167f68f21e168df71ec5b083113734"
  integrity sha1-yPFVMR0Wf2jyHhaN9x7FsIMRNzQ=

rsync@^0.6.1:
  version "0.6.1"
  resolved "http://r.npm.sankuai.com/rsync/download/rsync-0.6.1.tgz#3681a0098bd8750448f8bf9da1fee09f7763742b"
  integrity sha1-NoGgCYvYdQRI+L+dof7gn3djdCs=

run-async@^2.2.0, run-async@^2.4.0:
  version "2.4.1"
  resolved "http://r.npm.sankuai.com/run-async/download/run-async-2.4.1.tgz#8440eccf99ea3e70bd409d49aab88e10c189a455"
  integrity sha1-hEDsz5nqPnC9QJ1JqriOEMGJpFU=

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/run-parallel/download/run-parallel-1.2.0.tgz#66d1368da7bdf921eb9d95bd1a9229e7f21a43ee"
  integrity sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=
  dependencies:
    queue-microtask "^1.2.2"

rx-lite-aggregates@^4.0.8:
  version "4.0.8"
  resolved "http://r.npm.sankuai.com/rx-lite-aggregates/download/rx-lite-aggregates-4.0.8.tgz#753b87a89a11c95467c4ac1626c4efc4e05c67be"
  integrity sha1-dTuHqJoRyVRnxKwWJsTvxOBcZ74=
  dependencies:
    rx-lite "*"

rx-lite@*, rx-lite@^4.0.8:
  version "4.0.8"
  resolved "http://r.npm.sankuai.com/rx-lite/download/rx-lite-4.0.8.tgz#0b1e11af8bc44836f04a6407e92da42467b79444"
  integrity sha1-Cx4Rr4vESDbwSmQH6S2kJGe3lEQ=

rxjs@^6.6.0:
  version "6.6.7"
  resolved "http://r.npm.sankuai.com/rxjs/download/rxjs-6.6.7.tgz#90ac018acabf491bf65044235d5863c4dab804c9"
  integrity sha1-kKwBisq/SRv2UEQjXVhjxNq4BMk=
  dependencies:
    tslib "^1.9.0"

rxjs@^7.5.1:
  version "7.8.0"
  resolved "http://r.npm.sankuai.com/rxjs/download/rxjs-7.8.0.tgz#90a938862a82888ff4c7359811a595e14e1e09a4"
  integrity sha1-kKk4hiqCiI/0xzWYEaWV4U4eCaQ=
  dependencies:
    tslib "^2.1.0"

rxjs@^7.5.5:
  version "7.8.1"
  resolved "http://r.npm.sankuai.com/rxjs/download/rxjs-7.8.1.tgz#6f6f3d99ea8044291efd92e7c7fcf562c4057543"
  integrity sha1-b289meqARCke/ZLnx/z1YsQFdUM=
  dependencies:
    tslib "^2.1.0"

safe-array-concat@^1.1.2:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/safe-array-concat/download/safe-array-concat-1.1.2.tgz#81d77ee0c4e8b863635227c721278dd524c20edb"
  integrity sha1-gdd+4MTouGNjUifHISeN1STCDts=
  dependencies:
    call-bind "^1.0.7"
    get-intrinsic "^1.2.4"
    has-symbols "^1.0.3"
    isarray "^2.0.5"

safe-buffer@5.1.2, safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "http://r.npm.sankuai.com/safe-buffer/download/safe-buffer-5.1.2.tgz#991ec69d296e0313747d59bdfd2b745c35f8828d"
  integrity sha1-mR7GnSluAxN0fVm9/St0XDX4go0=

safe-buffer@5.2.1, safe-buffer@^5.1.1, safe-buffer@~5.2.0:
  version "5.2.1"
  resolved "http://r.npm.sankuai.com/safe-buffer/download/safe-buffer-5.2.1.tgz#1eaf9fa9bdb1fdd4ec75f58f9cdb4e6b7827eec6"
  integrity sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=

safe-regex-test@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/safe-regex-test/download/safe-regex-test-1.0.0.tgz#793b874d524eb3640d1873aad03596db2d4f2295"
  integrity sha1-eTuHTVJOs2QNGHOq0DWW2y1PIpU=
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.1.3"
    is-regex "^1.1.4"

safe-regex-test@^1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/safe-regex-test/download/safe-regex-test-1.0.3.tgz#a5b4c0f06e0ab50ea2c395c14d8371232924c377"
  integrity sha1-pbTA8G4KtQ6iw5XBTYNxIykkw3c=
  dependencies:
    call-bind "^1.0.6"
    es-errors "^1.3.0"
    is-regex "^1.1.4"

safe-regex@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/safe-regex/download/safe-regex-1.1.0.tgz#40a3669f3b077d1e943d44629e157dd48023bf2e"
  integrity sha1-QKNmnzsHfR6UPURinhV91IAjvy4=
  dependencies:
    ret "~0.1.10"

"safer-buffer@>= 2.1.2 < 3", "safer-buffer@>= 2.1.2 < 3.0.0":
  version "2.1.2"
  resolved "http://r.npm.sankuai.com/safer-buffer/download/safer-buffer-2.1.2.tgz#44fa161b0187b9549dd84bb91802f9bd8385cd6a"
  integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=

sane@^4.0.3:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/sane/download/sane-4.1.0.tgz#ed881fd922733a6c461bc189dc2b6c006f3ffded"
  integrity sha1-7Ygf2SJzOmxGG8GJ3CtsAG8//e0=
  dependencies:
    "@cnakazawa/watch" "^1.0.3"
    anymatch "^2.0.0"
    capture-exit "^2.0.0"
    exec-sh "^0.3.2"
    execa "^1.0.0"
    fb-watchman "^2.0.0"
    micromatch "^3.1.4"
    minimist "^1.1.1"
    walker "~1.0.5"

sax@^1.2.4:
  version "1.2.4"
  resolved "http://r.npm.sankuai.com/sax/download/sax-1.2.4.tgz#2816234e2378bddc4e5354fab5caa895df7100d9"
  integrity sha1-KBYjTiN4vdxOU1T6tcqold9xANk=

saxes@^5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/saxes/download/saxes-5.0.1.tgz#eebab953fa3b7608dbe94e5dadb15c888fa6696d"
  integrity sha1-7rq5U/o7dgjb6U5drbFciI+maW0=
  dependencies:
    xmlchars "^2.2.0"

scheduler@0.19.1, scheduler@^0.19.1:
  version "0.19.1"
  resolved "http://r.npm.sankuai.com/scheduler/download/scheduler-0.19.1.tgz#4f3e2ed2c1a7d65681f4c854fa8c5a1ccb40f196"
  integrity sha1-Tz4u0sGn1laB9MhU+oxaHMtA8ZY=
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"

screenfull@^5.0.0:
  version "5.2.0"
  resolved "http://r.npm.sankuai.com/screenfull/download/screenfull-5.2.0.tgz#6533d524d30621fc1283b9692146f3f13a93d1ba"
  integrity sha1-ZTPVJNMGIfwSg7lpIUbz8TqT0bo=

seek-bzip@^1.0.5:
  version "1.0.6"
  resolved "http://r.npm.sankuai.com/seek-bzip/download/seek-bzip-1.0.6.tgz#35c4171f55a680916b52a07859ecf3b5857f21c4"
  integrity sha1-NcQXH1WmgJFrUqB4WezztYV/IcQ=
  dependencies:
    commander "^2.8.1"

semver-compare@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/semver-compare/download/semver-compare-1.0.0.tgz#0dee216a1c941ab37e9efb1788f6afc5ff5537fc"
  integrity sha1-De4hahyUGrN+nvsXiPavxf9VN/w=

semver-regex@^3.1.2:
  version "3.1.4"
  resolved "http://r.npm.sankuai.com/semver-regex/download/semver-regex-3.1.4.tgz#13053c0d4aa11d070a2f2872b6b1e3ae1e1971b4"
  integrity sha1-EwU8DUqhHQcKLyhytrHjrh4ZcbQ=

"semver@2 || 3 || 4 || 5", semver@^5.1.0, semver@^5.5.0, semver@^5.6.0:
  version "5.7.1"
  resolved "http://r.npm.sankuai.com/semver/download/semver-5.7.1.tgz#a954f931aeba508d307bbf069eff0c01c96116f7"
  integrity sha1-qVT5Ma66UI0we78Gnv8MAclhFvc=

semver@7.x, semver@^7.2.1, semver@^7.3.2, semver@^7.3.7:
  version "7.4.0"
  resolved "http://r.npm.sankuai.com/semver/download/semver-7.4.0.tgz#8481c92feffc531ab1e012a8ffc15bdd3a0f4318"
  integrity sha1-hIHJL+/8Uxqx4BKo/8Fb3ToPQxg=
  dependencies:
    lru-cache "^6.0.0"

semver@^6.0.0, semver@^6.1.1, semver@^6.1.2, semver@^6.3.0:
  version "6.3.0"
  resolved "http://r.npm.sankuai.com/semver/download/semver-6.3.0.tgz#ee0a64c8af5e8ceea67687b133761e1becbd1d3d"
  integrity sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=

semver@^6.3.1:
  version "6.3.1"
  resolved "http://r.npm.sankuai.com/semver/download/semver-6.3.1.tgz#556d2ef8689146e46dcea4bfdd095f3434dffcb4"
  integrity sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=

send@0.18.0:
  version "0.18.0"
  resolved "http://r.npm.sankuai.com/send/download/send-0.18.0.tgz#670167cc654b05f5aa4a767f9113bb371bc706be"
  integrity sha1-ZwFnzGVLBfWqSnZ/kRO7NxvHBr4=
  dependencies:
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    fresh "0.5.2"
    http-errors "2.0.0"
    mime "1.6.0"
    ms "2.1.3"
    on-finished "2.4.1"
    range-parser "~1.2.1"
    statuses "2.0.1"

serialize-error@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/serialize-error/download/serialize-error-2.1.0.tgz#50b679d5635cdf84667bdc8e59af4e5b81d5f60a"
  integrity sha1-ULZ51WNc34Rme9yOWa9OW4HV9go=

serve-static@^1.13.1:
  version "1.15.0"
  resolved "http://r.npm.sankuai.com/serve-static/download/serve-static-1.15.0.tgz#faaef08cffe0a1a62f60cad0c4e513cff0ac9540"
  integrity sha1-+q7wjP/goaYvYMrQxOUTz/CslUA=
  dependencies:
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    parseurl "~1.3.3"
    send "0.18.0"

set-blocking@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/set-blocking/download/set-blocking-2.0.0.tgz#045f9782d011ae9a6803ddd382b24392b3d890f7"
  integrity sha1-BF+XgtARrppoA93TgrJDkrPYkPc=

set-function-length@^1.2.1:
  version "1.2.2"
  resolved "http://r.npm.sankuai.com/set-function-length/download/set-function-length-1.2.2.tgz#aac72314198eaed975cf77b2c3b6b880695e5449"
  integrity sha1-qscjFBmOrtl1z3eyw7a4gGleVEk=
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"

set-function-name@^2.0.1:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/set-function-name/download/set-function-name-2.0.2.tgz#16a705c5a0dc2f5e638ca96d8a8cd4e1c2b90985"
  integrity sha1-FqcFxaDcL15jjKltiozU4cK5CYU=
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    functions-have-names "^1.2.3"
    has-property-descriptors "^1.0.2"

set-value@^2.0.0, set-value@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/set-value/download/set-value-2.0.1.tgz#a18d40530e6f07de4228c7defe4227af8cad005b"
  integrity sha1-oY1AUw5vB95CKMfe/kInr4ytAFs=
  dependencies:
    extend-shallow "^2.0.1"
    is-extendable "^0.1.1"
    is-plain-object "^2.0.3"
    split-string "^3.0.1"

setimmediate@^1.0.5:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/setimmediate/download/setimmediate-1.0.5.tgz#290cbb232e306942d7d7ea9b83732ab7856f8285"
  integrity sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU=

setprototypeof@1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/setprototypeof/download/setprototypeof-1.2.0.tgz#66c9a24a73f9fc28cbe66b09fed3d33dcaf1b424"
  integrity sha1-ZsmiSnP5/CjL5msJ/tPTPcrxtCQ=

shallow-clone@^3.0.0:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/shallow-clone/download/shallow-clone-3.0.1.tgz#8f2981ad92531f55035b01fb230769a40e02efa3"
  integrity sha1-jymBrZJTH1UDWwH7IwdppA4C76M=
  dependencies:
    kind-of "^6.0.2"

shebang-command@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/shebang-command/download/shebang-command-1.2.0.tgz#44aac65b695b03398968c39f363fee5deafdf1ea"
  integrity sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=
  dependencies:
    shebang-regex "^1.0.0"

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/shebang-command/download/shebang-command-2.0.0.tgz#ccd0af4f8835fbdc265b82461aaf0c36663f34ea"
  integrity sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/shebang-regex/download/shebang-regex-1.0.0.tgz#da42f49740c0b42db2ca9728571cb190c98efea3"
  integrity sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/shebang-regex/download/shebang-regex-3.0.0.tgz#ae16f1644d873ecad843b0307b143362d4c42172"
  integrity sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=

shell-quote@1.6.1:
  version "1.6.1"
  resolved "http://r.npm.sankuai.com/shell-quote/download/shell-quote-1.6.1.tgz#f4781949cce402697127430ea3b3c5476f481767"
  integrity sha1-9HgZSczkAmlxJ0MOo7PFR29IF2c=
  dependencies:
    array-filter "~0.0.0"
    array-map "~0.0.0"
    array-reduce "~0.0.0"
    jsonify "~0.0.0"

shell-quote@^1.6.1:
  version "1.8.0"
  resolved "http://r.npm.sankuai.com/shell-quote/download/shell-quote-1.8.0.tgz#20d078d0eaf71d54f43bd2ba14a1b5b9bfa5c8ba"
  integrity sha1-INB40Or3HVT0O9K6FKG1ub+lyLo=

shellwords@^0.1.1:
  version "0.1.1"
  resolved "http://r.npm.sankuai.com/shellwords/download/shellwords-0.1.1.tgz#d6b9181c1a48d397324c84871efbcfc73fc0654b"
  integrity sha1-1rkYHBpI05cyTISHHvvPxz/AZUs=

side-channel@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/side-channel/download/side-channel-1.0.4.tgz#efce5c8fdc104ee751b25c58d4290011fa5ea2cf"
  integrity sha1-785cj9wQTudRslxY1CkAEfpeos8=
  dependencies:
    call-bind "^1.0.0"
    get-intrinsic "^1.0.2"
    object-inspect "^1.9.0"

signal-exit@^3.0.0, signal-exit@^3.0.2, signal-exit@^3.0.3:
  version "3.0.7"
  resolved "http://r.npm.sankuai.com/signal-exit/download/signal-exit-3.0.7.tgz#a9a1767f8af84155114eaabd73f99273c8f59ad9"
  integrity sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk=

simple-plist@^1.0.0:
  version "1.3.1"
  resolved "http://r.npm.sankuai.com/simple-plist/download/simple-plist-1.3.1.tgz#16e1d8f62c6c9b691b8383127663d834112fb017"
  integrity sha1-FuHY9ixsm2kbg4MSdmPYNBEvsBc=
  dependencies:
    bplist-creator "0.1.0"
    bplist-parser "0.3.1"
    plist "^3.0.5"

sisteransi@^1.0.5:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/sisteransi/download/sisteransi-1.0.5.tgz#134d681297756437cc05ca01370d3a7a571075ed"
  integrity sha1-E01oEpd1ZDfMBcoBNw06elcQde0=

slash@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/slash/download/slash-2.0.0.tgz#de552851a1759df3a8f206535442f5ec4ddeab44"
  integrity sha1-3lUoUaF1nfOo8gZTVEL17E3eq0Q=

slash@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/slash/download/slash-3.0.0.tgz#6539be870c165adbd5240220dbe361f1bc4d4634"
  integrity sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=

slice-ansi@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/slice-ansi/download/slice-ansi-2.1.0.tgz#cacd7693461a637a5788d92a7dd4fba068e81636"
  integrity sha1-ys12k0YaY3pXiNkqfdT7oGjoFjY=
  dependencies:
    ansi-styles "^3.2.0"
    astral-regex "^1.0.0"
    is-fullwidth-code-point "^2.0.0"

slice-ansi@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/slice-ansi/download/slice-ansi-3.0.0.tgz#31ddc10930a1b7e0b67b08c96c2f49b77a789787"
  integrity sha1-Md3BCTCht+C2ewjJbC9Jt3p4l4c=
  dependencies:
    ansi-styles "^4.0.0"
    astral-regex "^2.0.0"
    is-fullwidth-code-point "^3.0.0"

slice-ansi@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/slice-ansi/download/slice-ansi-4.0.0.tgz#500e8dd0fd55b05815086255b3195adf2a45fe6b"
  integrity sha1-UA6N0P1VsFgVCGJVsxla3ypF/ms=
  dependencies:
    ansi-styles "^4.0.0"
    astral-regex "^2.0.0"
    is-fullwidth-code-point "^3.0.0"

slice-ansi@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/slice-ansi/download/slice-ansi-5.0.0.tgz#b73063c57aa96f9cd881654b15294d95d285c42a"
  integrity sha1-tzBjxXqpb5zYgWVLFSlNldKFxCo=
  dependencies:
    ansi-styles "^6.0.0"
    is-fullwidth-code-point "^4.0.0"

snapdragon-node@^2.0.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/snapdragon-node/download/snapdragon-node-2.1.1.tgz#6c175f86ff14bdb0724563e8f3c1b021a286853b"
  integrity sha1-bBdfhv8UvbByRWPo88GwIaKGhTs=
  dependencies:
    define-property "^1.0.0"
    isobject "^3.0.0"
    snapdragon-util "^3.0.1"

snapdragon-util@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/snapdragon-util/download/snapdragon-util-3.0.1.tgz#f956479486f2acd79700693f6f7b805e45ab56e2"
  integrity sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI=
  dependencies:
    kind-of "^3.2.0"

snapdragon@^0.8.1:
  version "0.8.2"
  resolved "http://r.npm.sankuai.com/snapdragon/download/snapdragon-0.8.2.tgz#64922e7c565b0e14204ba1aa7d6964278d25182d"
  integrity sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0=
  dependencies:
    base "^0.11.1"
    debug "^2.2.0"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    map-cache "^0.2.2"
    source-map "^0.5.6"
    source-map-resolve "^0.5.0"
    use "^3.1.0"

sort-keys-length@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/sort-keys-length/download/sort-keys-length-1.0.1.tgz#9cb6f4f4e9e48155a6aa0671edd336ff1479a188"
  integrity sha1-nLb09OnkgVWmqgZx7dM2/xR5oYg=
  dependencies:
    sort-keys "^1.0.0"

sort-keys@^1.0.0:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/sort-keys/download/sort-keys-1.1.2.tgz#441b6d4d346798f1b4e49e8920adfba0e543f9ad"
  integrity sha1-RBttTTRnmPG05J6JIK37oOVD+a0=
  dependencies:
    is-plain-obj "^1.0.0"

sort-keys@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/sort-keys/download/sort-keys-2.0.0.tgz#658535584861ec97d730d6cf41822e1f56684128"
  integrity sha1-ZYU1WEhh7JfXMNbPQYIuH1ZoQSg=
  dependencies:
    is-plain-obj "^1.0.0"

source-map-resolve@^0.5.0:
  version "0.5.3"
  resolved "http://r.npm.sankuai.com/source-map-resolve/download/source-map-resolve-0.5.3.tgz#190866bece7553e1f8f267a2ee82c606b5509a1a"
  integrity sha1-GQhmvs51U+H48mei7oLGBrVQmho=
  dependencies:
    atob "^2.1.2"
    decode-uri-component "^0.2.0"
    resolve-url "^0.2.1"
    source-map-url "^0.4.0"
    urix "^0.1.0"

source-map-support@^0.5.16, source-map-support@^0.5.6:
  version "0.5.21"
  resolved "http://r.npm.sankuai.com/source-map-support/download/source-map-support-0.5.21.tgz#04fe7c7f9e1ed2d662233c28cb2b35b9f63f6e4f"
  integrity sha1-BP58f54e0tZiIzwoyys1ufY/bk8=
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map-url@^0.4.0:
  version "0.4.1"
  resolved "http://r.npm.sankuai.com/source-map-url/download/source-map-url-0.4.1.tgz#0af66605a745a5a2f91cf1bbf8a7afbc283dec56"
  integrity sha1-CvZmBadFpaL5HPG7+KevvCg97FY=

source-map@^0.5.6:
  version "0.5.7"
  resolved "http://r.npm.sankuai.com/source-map/download/source-map-0.5.7.tgz#8a039d2d1021d22d1ea14c80d8ea468ba2ef3fcc"
  integrity sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=

source-map@^0.6.0, source-map@^0.6.1, source-map@~0.6.1:
  version "0.6.1"
  resolved "http://r.npm.sankuai.com/source-map/download/source-map-0.6.1.tgz#74722af32e9614e9c287a8d0bbde48b5e2f1a263"
  integrity sha1-dHIq8y6WFOnCh6jQu95IteLxomM=

source-map@^0.7.3:
  version "0.7.4"
  resolved "http://r.npm.sankuai.com/source-map/download/source-map-0.7.4.tgz#a9bbe705c9d8846f4e08ff6765acf0f1b0898656"
  integrity sha1-qbvnBcnYhG9OCP9nZazw8bCJhlY=

spdx-correct@^3.0.0:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/spdx-correct/download/spdx-correct-3.2.0.tgz#4f5ab0668f0059e34f9c00dce331784a12de4e9c"
  integrity sha1-T1qwZo8AWeNPnADc4zF4ShLeTpw=
  dependencies:
    spdx-expression-parse "^3.0.0"
    spdx-license-ids "^3.0.0"

spdx-exceptions@^2.1.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/spdx-exceptions/download/spdx-exceptions-2.3.0.tgz#3f28ce1a77a00372683eade4a433183527a2163d"
  integrity sha1-PyjOGnegA3JoPq3kpDMYNSeiFj0=

spdx-expression-parse@^3.0.0:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/spdx-expression-parse/download/spdx-expression-parse-3.0.1.tgz#cf70f50482eefdc98e3ce0a6833e4a53ceeba679"
  integrity sha1-z3D1BILu/cmOPOCmgz5KU87rpnk=
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-license-ids@^3.0.0:
  version "3.0.13"
  resolved "http://r.npm.sankuai.com/spdx-license-ids/download/spdx-license-ids-3.0.13.tgz#7189a474c46f8d47c7b0da4b987bb45e908bd2d5"
  integrity sha1-cYmkdMRvjUfHsNpLmHu0XpCL0tU=

split-on-first@^1.0.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/split-on-first/download/split-on-first-1.1.0.tgz#f610afeee3b12bce1d0c30425e76398b78249a5f"
  integrity sha1-9hCv7uOxK84dDDBCXnY5i3gkml8=

split-string@^3.0.1, split-string@^3.0.2:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/split-string/download/split-string-3.1.0.tgz#7cb09dda3a86585705c64b39a6466038682e8fe2"
  integrity sha1-fLCd2jqGWFcFxks5pkZgOGguj+I=
  dependencies:
    extend-shallow "^3.0.0"

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/sprintf-js/download/sprintf-js-1.0.3.tgz#04e6926f662895354f3dd015203633b857297e2c"
  integrity sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=

stack-utils@^1.0.1:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/stack-utils/download/stack-utils-1.0.5.tgz#a19b0b01947e0029c8e451d5d61a498f5bb1471b"
  integrity sha1-oZsLAZR+ACnI5FHV1hpJj1uxRxs=
  dependencies:
    escape-string-regexp "^2.0.0"

stack-utils@^2.0.2:
  version "2.0.6"
  resolved "http://r.npm.sankuai.com/stack-utils/download/stack-utils-2.0.6.tgz#aaf0748169c02fc33c8232abccf933f54a1cc34f"
  integrity sha1-qvB0gWnAL8M8gjKrzPkz9Uocw08=
  dependencies:
    escape-string-regexp "^2.0.0"

stackframe@^1.3.4:
  version "1.3.4"
  resolved "http://r.npm.sankuai.com/stackframe/download/stackframe-1.3.4.tgz#b881a004c8c149a5e8efef37d51b16e412943310"
  integrity sha1-uIGgBMjBSaXo7+831RsW5BKUMxA=

stacktrace-parser@^0.1.3:
  version "0.1.10"
  resolved "http://r.npm.sankuai.com/stacktrace-parser/download/stacktrace-parser-0.1.10.tgz#29fb0cae4e0d0b85155879402857a1639eb6051a"
  integrity sha1-KfsMrk4NC4UVWHlAKFehY562BRo=
  dependencies:
    type-fest "^0.7.1"

static-extend@^0.1.1:
  version "0.1.2"
  resolved "http://r.npm.sankuai.com/static-extend/download/static-extend-0.1.2.tgz#60809c39cbff55337226fd5e0b520f341f1fb5c6"
  integrity sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=
  dependencies:
    define-property "^0.2.5"
    object-copy "^0.1.0"

statuses@2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/statuses/download/statuses-2.0.1.tgz#55cb000ccf1d48728bd23c685a063998cf1a1b63"
  integrity sha1-VcsADM8dSHKL0jxoWgY5mM8aG2M=

statuses@~1.5.0:
  version "1.5.0"
  resolved "http://r.npm.sankuai.com/statuses/download/statuses-1.5.0.tgz#161c7dac177659fd9811f43771fa99381478628c"
  integrity sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=

stream-buffers@2.2.x, stream-buffers@~2.2.0:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/stream-buffers/download/stream-buffers-2.2.0.tgz#91d5f5130d1cef96dcfa7f726945188741d09ee4"
  integrity sha1-kdX1Ew0c75bc+n9yaUUYh0HQnuQ=

strict-uri-encode@^1.0.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/strict-uri-encode/download/strict-uri-encode-1.1.0.tgz#279b225df1d582b1f54e65addd4352e18faa0713"
  integrity sha1-J5siXfHVgrH1TmWt3UNS4Y+qBxM=

strict-uri-encode@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/strict-uri-encode/download/strict-uri-encode-2.0.0.tgz#b9c7330c7042862f6b142dc274bbcc5866ce3546"
  integrity sha1-ucczDHBChi9rFC3CdLvMWGbONUY=

string-argv@0.3.1:
  version "0.3.1"
  resolved "http://r.npm.sankuai.com/string-argv/download/string-argv-0.3.1.tgz#95e2fbec0427ae19184935f816d74aaa4c5c19da"
  integrity sha1-leL77AQnrhkYSTX4FtdKqkxcGdo=

string-length@^4.0.1:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/string-length/download/string-length-4.0.2.tgz#a8a8dc7bd5c1a82b9b3c8b87e125f66871b6e57a"
  integrity sha1-qKjce9XBqCubPIuH4SX2aHG25Xo=
  dependencies:
    char-regex "^1.0.2"
    strip-ansi "^6.0.0"

string-natural-compare@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/string-natural-compare/download/string-natural-compare-3.0.1.tgz#7a42d58474454963759e8e8b7ae63d71c1e7fdf4"
  integrity sha1-ekLVhHRFSWN1no6LeuY9ccHn/fQ=

string-width@^1.0.1:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/string-width/download/string-width-1.0.2.tgz#118bdf5b8cdc51a2a7e70d211e07e2b0b9b107d3"
  integrity sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M=
  dependencies:
    code-point-at "^1.0.0"
    is-fullwidth-code-point "^1.0.0"
    strip-ansi "^3.0.0"

string-width@^2.1.0:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/string-width/download/string-width-2.1.1.tgz#ab93f27a8dc13d28cac815c462143a6d9012ae9e"
  integrity sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4=
  dependencies:
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^4.0.0"

string-width@^3.0.0, string-width@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/string-width/download/string-width-3.1.0.tgz#22767be21b62af1081574306f69ac51b62203961"
  integrity sha1-InZ74htirxCBV0MG9prFG2IgOWE=
  dependencies:
    emoji-regex "^7.0.1"
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^5.1.0"

string-width@^4.1.0, string-width@^4.2.0, string-width@^4.2.3:
  version "4.2.3"
  resolved "http://r.npm.sankuai.com/string-width/download/string-width-4.2.3.tgz#269c7117d27b05ad2e536830a8ec895ef9c6d010"
  integrity sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^5.0.0:
  version "5.1.2"
  resolved "http://r.npm.sankuai.com/string-width/download/string-width-5.1.2.tgz#14f8daec6d81e7221d2a357e668cab73bdbca794"
  integrity sha1-FPja7G2B5yIdKjV+Zoyrc728p5Q=
  dependencies:
    eastasianwidth "^0.2.0"
    emoji-regex "^9.2.2"
    strip-ansi "^7.0.1"

string.prototype.matchall@^4.0.8:
  version "4.0.8"
  resolved "http://r.npm.sankuai.com/string.prototype.matchall/download/string.prototype.matchall-4.0.8.tgz#3bf85722021816dcd1bf38bb714915887ca79fd3"
  integrity sha1-O/hXIgIYFtzRvzi7cUkViHynn9M=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"
    get-intrinsic "^1.1.3"
    has-symbols "^1.0.3"
    internal-slot "^1.0.3"
    regexp.prototype.flags "^1.4.3"
    side-channel "^1.0.4"

string.prototype.trim@^1.2.7:
  version "1.2.7"
  resolved "http://r.npm.sankuai.com/string.prototype.trim/download/string.prototype.trim-1.2.7.tgz#a68352740859f6893f14ce3ef1bb3037f7a90533"
  integrity sha1-poNSdAhZ9ok/FM4+8bswN/epBTM=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"

string.prototype.trim@^1.2.9:
  version "1.2.9"
  resolved "http://r.npm.sankuai.com/string.prototype.trim/download/string.prototype.trim-1.2.9.tgz#b6fa326d72d2c78b6df02f7759c73f8f6274faa4"
  integrity sha1-tvoybXLSx4tt8C93Wcc/j2J0+qQ=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.0"
    es-object-atoms "^1.0.0"

string.prototype.trimend@^1.0.6:
  version "1.0.6"
  resolved "http://r.npm.sankuai.com/string.prototype.trimend/download/string.prototype.trimend-1.0.6.tgz#c4a27fa026d979d79c04f17397f250a462944533"
  integrity sha1-xKJ/oCbZedecBPFzl/JQpGKURTM=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"

string.prototype.trimend@^1.0.8:
  version "1.0.8"
  resolved "http://r.npm.sankuai.com/string.prototype.trimend/download/string.prototype.trimend-1.0.8.tgz#3651b8513719e8a9f48de7f2f77640b26652b229"
  integrity sha1-NlG4UTcZ6Kn0jefy93ZAsmZSsik=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

string.prototype.trimstart@^1.0.6:
  version "1.0.6"
  resolved "http://r.npm.sankuai.com/string.prototype.trimstart/download/string.prototype.trimstart-1.0.6.tgz#e90ab66aa8e4007d92ef591bbf3cd422c56bdcf4"
  integrity sha1-6Qq2aqjkAH2S71kbvzzUIsVr3PQ=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"

string.prototype.trimstart@^1.0.8:
  version "1.0.8"
  resolved "http://r.npm.sankuai.com/string.prototype.trimstart/download/string.prototype.trimstart-1.0.8.tgz#7ee834dda8c7c17eff3118472bb35bfedaa34dde"
  integrity sha1-fug03ajHwX7/MRhHK7Nb/tqjTd4=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

string_decoder@^1.1.1:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/string_decoder/download/string_decoder-1.3.0.tgz#42f114594a46cf1a8e30b0a84f56c78c3edac21e"
  integrity sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=
  dependencies:
    safe-buffer "~5.2.0"

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/string_decoder/download/string_decoder-1.1.1.tgz#9cf1611ba62685d7030ae9e4ba34149c3af03fc8"
  integrity sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=
  dependencies:
    safe-buffer "~5.1.0"

strip-ansi@^3.0.0, strip-ansi@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-3.0.1.tgz#6a385fb8853d952d5ff05d0e8aaf94278dc63dcf"
  integrity sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=
  dependencies:
    ansi-regex "^2.0.0"

strip-ansi@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-4.0.0.tgz#a8479022eb1ac368a871389b635262c505ee368f"
  integrity sha1-qEeQIusaw2iocTibY1JixQXuNo8=
  dependencies:
    ansi-regex "^3.0.0"

strip-ansi@^5.0.0, strip-ansi@^5.1.0, strip-ansi@^5.2.0:
  version "5.2.0"
  resolved "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-5.2.0.tgz#8c9a536feb6afc962bdfa5b104a5091c1ad9c0ae"
  integrity sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=
  dependencies:
    ansi-regex "^4.1.0"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-6.0.1.tgz#9e26c63d30f53443e9489495b2105d37b67a85d9"
  integrity sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^7.0.1:
  version "7.0.1"
  resolved "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-7.0.1.tgz#61740a08ce36b61e50e65653f07060d000975fb2"
  integrity sha1-YXQKCM42th5Q5lZT8HBg0ACXX7I=
  dependencies:
    ansi-regex "^6.0.1"

strip-bom@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/strip-bom/download/strip-bom-3.0.0.tgz#2334c18e9c759f7bdd56fdef7e9ae3d588e68ed3"
  integrity sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=

strip-bom@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/strip-bom/download/strip-bom-4.0.0.tgz#9c3505c1db45bcedca3d9cf7a16f5c5aa3901878"
  integrity sha1-nDUFwdtFvO3KPZz3oW9cWqOQGHg=

strip-dirs@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/strip-dirs/download/strip-dirs-2.1.0.tgz#4987736264fc344cf20f6c34aca9d13d1d4ed6c5"
  integrity sha1-SYdzYmT8NEzyD2w0rKnRPR1O1sU=
  dependencies:
    is-natural-number "^4.0.1"

strip-eof@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/strip-eof/download/strip-eof-1.0.0.tgz#bb43ff5598a6eb05d89b59fcd129c983313606bf"
  integrity sha1-u0P/VZim6wXYm1n80SnJgzE2Br8=

strip-final-newline@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/strip-final-newline/download/strip-final-newline-2.0.0.tgz#89b852fb2fcbe936f6f4b3187afb0a12c1ab58ad"
  integrity sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=

strip-indent@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/strip-indent/download/strip-indent-3.0.0.tgz#c32e1cee940b6b3432c771bc2c54bcce73cd3001"
  integrity sha1-wy4c7pQLazQyx3G8LFS8znPNMAE=
  dependencies:
    min-indent "^1.0.0"

strip-json-comments@^3.1.0, strip-json-comments@^3.1.1:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/strip-json-comments/download/strip-json-comments-3.1.1.tgz#31f1281b3832630434831c310c01cccda8cbe006"
  integrity sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=

strip-outer@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/strip-outer/download/strip-outer-1.0.1.tgz#b2fd2abf6604b9d1e6013057195df836b8a9d631"
  integrity sha1-sv0qv2YEudHmATBXGV34Nrip1jE=
  dependencies:
    escape-string-regexp "^1.0.2"

sudo-prompt@^9.0.0:
  version "9.2.1"
  resolved "http://r.npm.sankuai.com/sudo-prompt/download/sudo-prompt-9.2.1.tgz#77efb84309c9ca489527a4e749f287e6bdd52afd"
  integrity sha1-d++4QwnJykiVJ6TnSfKH5r3VKv0=

supports-color@9.0.2:
  version "9.0.2"
  resolved "http://r.npm.sankuai.com/supports-color/download/supports-color-9.0.2.tgz#50f082888e4b0a4e2ccd2d0b4f9ef4efcd332485"
  integrity sha1-UPCCiI5LCk4szS0LT570780zJIU=
  dependencies:
    has-flag "^5.0.0"

supports-color@^5.3.0:
  version "5.5.0"
  resolved "http://r.npm.sankuai.com/supports-color/download/supports-color-5.5.0.tgz#e2e69a44ac8772f78a1ec0b35b689df6530efc8f"
  integrity sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=
  dependencies:
    has-flag "^3.0.0"

supports-color@^6.1.0:
  version "6.1.0"
  resolved "http://r.npm.sankuai.com/supports-color/download/supports-color-6.1.0.tgz#0764abc69c63d5ac842dd4867e8d025e880df8f3"
  integrity sha1-B2Srxpxj1ayELdSGfo0CXogN+PM=
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.0.0, supports-color@^7.1.0:
  version "7.2.0"
  resolved "http://r.npm.sankuai.com/supports-color/download/supports-color-7.2.0.tgz#1b7dcdcb32b8138801b3e478ba6a51caa89648da"
  integrity sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=
  dependencies:
    has-flag "^4.0.0"

supports-hyperlinks@^2.0.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/supports-hyperlinks/download/supports-hyperlinks-2.3.0.tgz#3943544347c1ff90b15effb03fc14ae45ec10624"
  integrity sha1-OUNUQ0fB/5CxXv+wP8FK5F7BBiQ=
  dependencies:
    has-flag "^4.0.0"
    supports-color "^7.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/supports-preserve-symlinks-flag/download/supports-preserve-symlinks-flag-1.0.0.tgz#6eda4bd344a3c94aea376d4cc31bc77311039e09"
  integrity sha1-btpL00SjyUrqN21MwxvHcxEDngk=

svg-parser@^2.0.2:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/svg-parser/download/svg-parser-2.0.4.tgz#fdc2e29e13951736140b76cb122c8ee6630eb6b5"
  integrity sha1-/cLinhOVFzYUC3bLEiyO5mMOtrU=

swr@^2.3.3:
  version "2.3.3"
  resolved "http://r.npm.sankuai.com/swr/download/swr-2.3.3.tgz#9d6a703355f15f9099f45114db3ef75764444788"
  integrity sha1-nWpwM1XxX5CZ9FEU2z73V2RER4g=
  dependencies:
    dequal "^2.0.3"
    use-sync-external-store "^1.4.0"

symbol-tree@^3.2.4:
  version "3.2.4"
  resolved "http://r.npm.sankuai.com/symbol-tree/download/symbol-tree-3.2.4.tgz#430637d248ba77e078883951fb9aa0eed7c63fa2"
  integrity sha1-QwY30ki6d+B4iDlR+5qg7tfGP6I=

table@^6.0.9:
  version "6.8.1"
  resolved "http://r.npm.sankuai.com/table/download/table-6.8.1.tgz#ea2b71359fe03b017a5fbc296204471158080bdf"
  integrity sha1-6itxNZ/gOwF6X7wpYgRHEVgIC98=
  dependencies:
    ajv "^8.0.1"
    lodash.truncate "^4.4.2"
    slice-ansi "^4.0.0"
    string-width "^4.2.3"
    strip-ansi "^6.0.1"

tar-stream@^1.5.2:
  version "1.6.2"
  resolved "http://r.npm.sankuai.com/tar-stream/download/tar-stream-1.6.2.tgz#8ea55dab37972253d9a9af90fdcd559ae435c555"
  integrity sha1-jqVdqzeXIlPZqa+Q/c1VmuQ1xVU=
  dependencies:
    bl "^1.0.0"
    buffer-alloc "^1.2.0"
    end-of-stream "^1.0.0"
    fs-constants "^1.0.0"
    readable-stream "^2.3.0"
    to-buffer "^1.1.1"
    xtend "^4.0.0"

tar-stream@^2.2.0:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/tar-stream/download/tar-stream-2.2.0.tgz#acad84c284136b060dc3faa64474aa9aebd77287"
  integrity sha1-rK2EwoQTawYNw/qmRHSqmuvXcoc=
  dependencies:
    bl "^4.0.3"
    end-of-stream "^1.4.1"
    fs-constants "^1.0.0"
    inherits "^2.0.3"
    readable-stream "^3.1.1"

temp@0.8.3:
  version "0.8.3"
  resolved "http://r.npm.sankuai.com/temp/download/temp-0.8.3.tgz#e0c6bc4d26b903124410e4fed81103014dfc1f59"
  integrity sha1-4Ma8TSa5AxJEEOT+2BEDAU38H1k=
  dependencies:
    os-tmpdir "^1.0.0"
    rimraf "~2.2.6"

terminal-link@^2.0.0:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/terminal-link/download/terminal-link-2.1.1.tgz#14a64a27ab3c0df933ea546fba55f2d078edc994"
  integrity sha1-FKZKJ6s8Dfkz6lRvulXy0HjtyZQ=
  dependencies:
    ansi-escapes "^4.2.1"
    supports-hyperlinks "^2.0.0"

test-exclude@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/test-exclude/download/test-exclude-6.0.0.tgz#04a8698661d805ea6fa293b6cb9e63ac044ef15e"
  integrity sha1-BKhphmHYBepvopO2y55jrARO8V4=
  dependencies:
    "@istanbuljs/schema" "^0.1.2"
    glob "^7.1.4"
    minimatch "^3.0.4"

text-table@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/text-table/download/text-table-0.2.0.tgz#7f5ee823ae805207c00af2df4a84ec3fcfa570b4"
  integrity sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=

throat@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/throat/download/throat-4.1.0.tgz#89037cbc92c56ab18926e6ba4cbb200e15672a6a"
  integrity sha1-iQN8vJLFarGJJua6TLsgDhVnKmo=

throat@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/throat/download/throat-5.0.0.tgz#c5199235803aad18754a667d659b5e72ce16764b"
  integrity sha1-xRmSNYA6rRh1SmZ9ZZtecs4Wdks=

through2@^2.0.0, through2@^2.0.1:
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/through2/download/through2-2.0.5.tgz#01c1e39eb31d07cb7d03a96a70823260b23132cd"
  integrity sha1-AcHjnrMdB8t9A6lqcIIyYLIxMs0=
  dependencies:
    readable-stream "~2.3.6"
    xtend "~4.0.1"

through@^2.3.6, through@^2.3.8:
  version "2.3.8"
  resolved "http://r.npm.sankuai.com/through/download/through-2.3.8.tgz#0dd4c9ffaabc357960b1b724115d7e0e86a2e1f5"
  integrity sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=

time-stamp@^1.0.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/time-stamp/download/time-stamp-1.1.0.tgz#764a5a11af50561921b133f3b44e618687e0f5c3"
  integrity sha1-dkpaEa9QVhkhsTPztE5hhofg9cM=

timed-out@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/timed-out/download/timed-out-4.0.1.tgz#f32eacac5a175bea25d7fab565ab3ed8741ef56f"
  integrity sha1-8y6srFoXW+ol1/q1Zas+2HQe9W8=

tmp@^0.0.33:
  version "0.0.33"
  resolved "http://r.npm.sankuai.com/tmp/download/tmp-0.0.33.tgz#6d34335889768d21b2bcda0aa277ced3b1bfadf9"
  integrity sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=
  dependencies:
    os-tmpdir "~1.0.2"

tmpl@1.0.5:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/tmpl/download/tmpl-1.0.5.tgz#8683e0b902bb9c20c4f726e3c0b69f36518c07cc"
  integrity sha1-hoPguQK7nCDE9ybjwLafNlGMB8w=

to-buffer@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/to-buffer/download/to-buffer-1.1.1.tgz#493bd48f62d7c43fcded313a03dcadb2e1213a80"
  integrity sha1-STvUj2LXxD/N7TE6A9ytsuEhOoA=

to-fast-properties@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/to-fast-properties/download/to-fast-properties-2.0.0.tgz#dc5e698cbd079265bc73e0377681a4e4e83f616e"
  integrity sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=

to-object-path@^0.3.0:
  version "0.3.0"
  resolved "http://r.npm.sankuai.com/to-object-path/download/to-object-path-0.3.0.tgz#297588b7b0e7e0ac08e04e672f85c1f4999e17af"
  integrity sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=
  dependencies:
    kind-of "^3.0.2"

to-regex-range@^2.1.0:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/to-regex-range/download/to-regex-range-2.1.1.tgz#7c80c17b9dfebe599e27367e0d4dd5590141db38"
  integrity sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=
  dependencies:
    is-number "^3.0.0"
    repeat-string "^1.6.1"

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/to-regex-range/download/to-regex-range-5.0.1.tgz#1648c44aae7c8d988a326018ed72f5b4dd0392e4"
  integrity sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=
  dependencies:
    is-number "^7.0.0"

to-regex@^3.0.1, to-regex@^3.0.2:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/to-regex/download/to-regex-3.0.2.tgz#13cfdd9b336552f30b51f33a8ae1b42a7a7599ce"
  integrity sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4=
  dependencies:
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    regex-not "^1.0.2"
    safe-regex "^1.1.0"

toidentifier@1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/toidentifier/download/toidentifier-1.0.1.tgz#3be34321a88a820ed1bd80dfaa33e479fbb8dd35"
  integrity sha1-O+NDIaiKgg7RvYDfqjPkefu43TU=

tough-cookie@^4.0.0:
  version "4.1.2"
  resolved "http://r.npm.sankuai.com/tough-cookie/download/tough-cookie-4.1.2.tgz#e53e84b85f24e0b65dd526f46628db6c85f6b874"
  integrity sha1-5T6EuF8k4LZd1Sb0ZijbbIX2uHQ=
  dependencies:
    psl "^1.1.33"
    punycode "^2.1.1"
    universalify "^0.2.0"
    url-parse "^1.5.3"

tr46@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/tr46/download/tr46-2.1.0.tgz#fa87aa81ca5d5941da8cbf1f9b749dc969a4e240"
  integrity sha1-+oeqgcpdWUHajL8fm3SdyWmk4kA=
  dependencies:
    punycode "^2.1.1"

tr46@~0.0.3:
  version "0.0.3"
  resolved "http://r.npm.sankuai.com/tr46/download/tr46-0.0.3.tgz#8184fd347dac9cdc185992f3a6622e14b9d9ab6a"
  integrity sha1-gYT9NH2snNwYWZLzpmIuFLnZq2o=

traverse@0.6.6:
  version "0.6.6"
  resolved "http://r.npm.sankuai.com/traverse/download/traverse-0.6.6.tgz#cbdf560fd7b9af632502fed40f918c157ea97137"
  integrity sha1-y99WD9e5r2MlAv7UD5GMFX6pcTc=

trim-repeated@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/trim-repeated/download/trim-repeated-1.0.0.tgz#e3646a2ea4e891312bf7eace6cfb05380bc01c21"
  integrity sha1-42RqLqTokTEr9+rObPsFOAvAHCE=
  dependencies:
    escape-string-regexp "^1.0.2"

ts-jest@^26.3.0:
  version "26.5.6"
  resolved "http://r.npm.sankuai.com/ts-jest/download/ts-jest-26.5.6.tgz#c32e0746425274e1dfe333f43cd3c800e014ec35"
  integrity sha1-wy4HRkJSdOHf4zP0PNPIAOAU7DU=
  dependencies:
    bs-logger "0.x"
    buffer-from "1.x"
    fast-json-stable-stringify "2.x"
    jest-util "^26.1.0"
    json5 "2.x"
    lodash "4.x"
    make-error "1.x"
    mkdirp "1.x"
    semver "7.x"
    yargs-parser "20.x"

tsconfig-paths@^3.15.0:
  version "3.15.0"
  resolved "http://r.npm.sankuai.com/tsconfig-paths/download/tsconfig-paths-3.15.0.tgz#5299ec605e55b1abb23ec939ef15edaf483070d4"
  integrity sha1-UpnsYF5VsauyPsk57xXtr0gwcNQ=
  dependencies:
    "@types/json5" "^0.0.29"
    json5 "^1.0.2"
    minimist "^1.2.6"
    strip-bom "^3.0.0"

tslib@^1.8.1, tslib@^1.9.0:
  version "1.14.1"
  resolved "http://r.npm.sankuai.com/tslib/download/tslib-1.14.1.tgz#cf2d38bdc34a134bcaf1091c41f6619e2f672d00"
  integrity sha1-zy04vcNKE0vK8QkcQfZhni9nLQA=

tslib@^2.1.0:
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/tslib/download/tslib-2.5.0.tgz#42bfed86f5787aeb41d031866c8f402429e0fddf"
  integrity sha1-Qr/thvV4eutB0DGGbI9AJCng/d8=

tslib@^2.4.1:
  version "2.6.2"
  resolved "http://r.npm.sankuai.com/tslib/download/tslib-2.6.2.tgz#703ac29425e7b37cd6fd456e92404d46d1f3e4ae"
  integrity sha1-cDrClCXns3zW/UVukkBNRtHz5K4=

tsutils@^3.17.1, tsutils@^3.21.0:
  version "3.21.0"
  resolved "http://r.npm.sankuai.com/tsutils/download/tsutils-3.21.0.tgz#b48717d394cea6c1e096983eed58e9d61715b623"
  integrity sha1-tIcX05TOpsHglpg+7Vjp1hcVtiM=
  dependencies:
    tslib "^1.8.1"

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  resolved "http://r.npm.sankuai.com/type-check/download/type-check-0.4.0.tgz#07b8203bfa7056c0657050e3ccd2c37730bab8f1"
  integrity sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=
  dependencies:
    prelude-ls "^1.2.1"

type-check@~0.3.2:
  version "0.3.2"
  resolved "http://r.npm.sankuai.com/type-check/download/type-check-0.3.2.tgz#5884cab512cf1d355e3fb784f30804b2b520db72"
  integrity sha1-WITKtRLPHTVeP7eE8wgEsrUg23I=
  dependencies:
    prelude-ls "~1.1.2"

type-detect@4.0.8:
  version "4.0.8"
  resolved "http://r.npm.sankuai.com/type-detect/download/type-detect-4.0.8.tgz#7646fb5f18871cfbb7749e69bd39a6388eb7450c"
  integrity sha1-dkb7XxiHHPu3dJ5pvTmmOI63RQw=

type-fest@^0.20.2:
  version "0.20.2"
  resolved "http://r.npm.sankuai.com/type-fest/download/type-fest-0.20.2.tgz#1bf207f4b28f91583666cb5fbd327887301cd5f4"
  integrity sha1-G/IH9LKPkVg2ZstfvTJ4hzAc1fQ=

type-fest@^0.21.3:
  version "0.21.3"
  resolved "http://r.npm.sankuai.com/type-fest/download/type-fest-0.21.3.tgz#d260a24b0198436e133fa26a524a6d65fa3b2e37"
  integrity sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc=

type-fest@^0.6.0:
  version "0.6.0"
  resolved "http://r.npm.sankuai.com/type-fest/download/type-fest-0.6.0.tgz#8d2a2370d3df886eb5c90ada1c5bf6188acf838b"
  integrity sha1-jSojcNPfiG61yQraHFv2GIrPg4s=

type-fest@^0.7.1:
  version "0.7.1"
  resolved "http://r.npm.sankuai.com/type-fest/download/type-fest-0.7.1.tgz#8dda65feaf03ed78f0a3f9678f1869147f7c5c48"
  integrity sha1-jdpl/q8D7Xjwo/lnjxhpFH98XEg=

type-fest@^0.8.1:
  version "0.8.1"
  resolved "http://r.npm.sankuai.com/type-fest/download/type-fest-0.8.1.tgz#09e249ebde851d3b1e48d27c105444667f17b83d"
  integrity sha1-CeJJ696FHTseSNJ8EFREZn8XuD0=

typed-array-buffer@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/typed-array-buffer/download/typed-array-buffer-1.0.2.tgz#1867c5d83b20fcb5ccf32649e5e2fc7424474ff3"
  integrity sha1-GGfF2Dsg/LXM8yZJ5eL8dCRHT/M=
  dependencies:
    call-bind "^1.0.7"
    es-errors "^1.3.0"
    is-typed-array "^1.1.13"

typed-array-byte-length@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/typed-array-byte-length/download/typed-array-byte-length-1.0.1.tgz#d92972d3cff99a3fa2e765a28fcdc0f1d89dec67"
  integrity sha1-2Sly08/5mj+i52Wij83A8did7Gc=
  dependencies:
    call-bind "^1.0.7"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-proto "^1.0.3"
    is-typed-array "^1.1.13"

typed-array-byte-offset@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/typed-array-byte-offset/download/typed-array-byte-offset-1.0.2.tgz#f9ec1acb9259f395093e4567eb3c28a580d02063"
  integrity sha1-+eway5JZ85UJPkVn6zwopYDQIGM=
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.7"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-proto "^1.0.3"
    is-typed-array "^1.1.13"

typed-array-length@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/typed-array-length/download/typed-array-length-1.0.4.tgz#89d83785e5c4098bec72e08b319651f0eac9c1bb"
  integrity sha1-idg3heXECYvscuCLMZZR8OrJwbs=
  dependencies:
    call-bind "^1.0.2"
    for-each "^0.3.3"
    is-typed-array "^1.1.9"

typed-array-length@^1.0.6:
  version "1.0.6"
  resolved "http://r.npm.sankuai.com/typed-array-length/download/typed-array-length-1.0.6.tgz#57155207c76e64a3457482dfdc1c9d1d3c4c73a3"
  integrity sha1-VxVSB8duZKNFdILf3BydHTxMc6M=
  dependencies:
    call-bind "^1.0.7"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-proto "^1.0.3"
    is-typed-array "^1.1.13"
    possible-typed-array-names "^1.0.0"

typedarray-to-buffer@^3.1.5:
  version "3.1.5"
  resolved "http://r.npm.sankuai.com/typedarray-to-buffer/download/typedarray-to-buffer-3.1.5.tgz#a97ee7a9ff42691b9f783ff1bc5112fe3fca9080"
  integrity sha1-qX7nqf9CaRufeD/xvFES/j/KkIA=
  dependencies:
    is-typedarray "^1.0.0"

typedarray@^0.0.6:
  version "0.0.6"
  resolved "http://r.npm.sankuai.com/typedarray/download/typedarray-0.0.6.tgz#867ac74e3864187b1d3d47d996a78ec5c8830777"
  integrity sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=

typescript@^4.1.3, typescript@^4.6.3:
  version "4.9.5"
  resolved "http://r.npm.sankuai.com/typescript/download/typescript-4.9.5.tgz#095979f9bcc0d09da324d58d03ce8f8374cbe65a"
  integrity sha1-CVl5+bzA0J2jJNWNA86Pg3TL5lo=

ua-parser-js@^0.7.18:
  version "0.7.35"
  resolved "http://r.npm.sankuai.com/ua-parser-js/download/ua-parser-js-0.7.35.tgz#8bda4827be4f0b1dda91699a29499575a1f1d307"
  integrity sha1-i9pIJ75PCx3akWmaKUmVdaHx0wc=

uc.micro@^1.0.1, uc.micro@^1.0.5:
  version "1.0.6"
  resolved "http://r.npm.sankuai.com/uc.micro/download/uc.micro-1.0.6.tgz#9c411a802a409a91fc6cf74081baba34b24499ac"
  integrity sha1-nEEagCpAmpH8bPdAgbq6NLJEmaw=

uglify-es@^3.1.9:
  version "3.3.9"
  resolved "http://r.npm.sankuai.com/uglify-es/download/uglify-es-3.3.9.tgz#0c1c4f0700bed8dbc124cdb304d2592ca203e677"
  integrity sha1-DBxPBwC+2NvBJM2zBNJZLKID5nc=
  dependencies:
    commander "~2.13.0"
    source-map "~0.6.1"

ultron@1.0.x:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/ultron/download/ultron-1.0.2.tgz#ace116ab557cd197386a4e88f4685378c8b2e4fa"
  integrity sha1-rOEWq1V80Zc4ak6I9GhTeMiy5Po=

unbox-primitive@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/unbox-primitive/download/unbox-primitive-1.0.2.tgz#29032021057d5e6cdbd08c5129c226dff8ed6f9e"
  integrity sha1-KQMgIQV9Xmzb0IxRKcIm3/jtb54=
  dependencies:
    call-bind "^1.0.2"
    has-bigints "^1.0.2"
    has-symbols "^1.0.3"
    which-boxed-primitive "^1.0.2"

unbzip2-stream@^1.0.9:
  version "1.4.3"
  resolved "http://r.npm.sankuai.com/unbzip2-stream/download/unbzip2-stream-1.4.3.tgz#b0da04c4371311df771cdc215e87f2130991ace7"
  integrity sha1-sNoExDcTEd93HNwhXofyEwmRrOc=
  dependencies:
    buffer "^5.2.1"
    through "^2.3.8"

unicode-canonical-property-names-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/unicode-canonical-property-names-ecmascript/download/unicode-canonical-property-names-ecmascript-2.0.0.tgz#301acdc525631670d39f6146e0e77ff6bbdebddc"
  integrity sha1-MBrNxSVjFnDTn2FG4Od/9rvevdw=

unicode-match-property-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/unicode-match-property-ecmascript/download/unicode-match-property-ecmascript-2.0.0.tgz#54fd16e0ecb167cf04cf1f756bdcc92eba7976c3"
  integrity sha1-VP0W4OyxZ88Ezx91a9zJLrp5dsM=
  dependencies:
    unicode-canonical-property-names-ecmascript "^2.0.0"
    unicode-property-aliases-ecmascript "^2.0.0"

unicode-match-property-value-ecmascript@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/unicode-match-property-value-ecmascript/download/unicode-match-property-value-ecmascript-2.1.0.tgz#cb5fffdcd16a05124f5a4b0bf7c3770208acbbe0"
  integrity sha1-y1//3NFqBRJPWksL98N3Agisu+A=

unicode-property-aliases-ecmascript@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/unicode-property-aliases-ecmascript/download/unicode-property-aliases-ecmascript-2.1.0.tgz#43d41e3be698bd493ef911077c9b131f827e8ccd"
  integrity sha1-Q9QeO+aYvUk++REHfJsTH4J+jM0=

union-value@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/union-value/download/union-value-1.0.1.tgz#0b6fe7b835aecda61c6ea4d4f02c14221e109847"
  integrity sha1-C2/nuDWuzaYcbqTU8CwUIh4QmEc=
  dependencies:
    arr-union "^3.1.0"
    get-value "^2.0.6"
    is-extendable "^0.1.1"
    set-value "^2.0.1"

universalify@^0.1.0:
  version "0.1.2"
  resolved "http://r.npm.sankuai.com/universalify/download/universalify-0.1.2.tgz#b646f69be3942dabcecc9d6639c80dc105efaa66"
  integrity sha1-tkb2m+OULavOzJ1mOcgNwQXvqmY=

universalify@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/universalify/download/universalify-0.2.0.tgz#6451760566fa857534745ab1dde952d1b1761be0"
  integrity sha1-ZFF2BWb6hXU0dFqx3elS0bF2G+A=

unpipe@~1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/unpipe/download/unpipe-1.0.0.tgz#b2bf4ee8514aae6165b4817829d21b2ef49904ec"
  integrity sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=

unset-value@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/unset-value/download/unset-value-1.0.0.tgz#8376873f7d2335179ffb1e6fc3a8ed0dfc8ab559"
  integrity sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=
  dependencies:
    has-value "^0.3.1"
    isobject "^3.0.0"

update-browserslist-db@^1.0.10:
  version "1.0.10"
  resolved "http://r.npm.sankuai.com/update-browserslist-db/download/update-browserslist-db-1.0.10.tgz#0f54b876545726f17d00cd9a2561e6dade943ff3"
  integrity sha1-D1S4dlRXJvF9AM2aJWHm2t6UP/M=
  dependencies:
    escalade "^3.1.1"
    picocolors "^1.0.0"

uri-js@^4.2.2:
  version "4.4.1"
  resolved "http://r.npm.sankuai.com/uri-js/download/uri-js-4.4.1.tgz#9b1a52595225859e55f669d928f88c6c57f2a77e"
  integrity sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=
  dependencies:
    punycode "^2.1.0"

urix@^0.1.0:
  version "0.1.0"
  resolved "http://r.npm.sankuai.com/urix/download/urix-0.1.0.tgz#da937f7a62e21fec1fd18d49b35c2935067a6c72"
  integrity sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=

url-parse-lax@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/url-parse-lax/download/url-parse-lax-3.0.0.tgz#16b5cafc07dbe3676c1b1999177823d6503acb0c"
  integrity sha1-FrXK/Afb42dsGxmZF3gj1lA6yww=
  dependencies:
    prepend-http "^2.0.0"

url-parse@^1.5.3:
  version "1.5.10"
  resolved "http://r.npm.sankuai.com/url-parse/download/url-parse-1.5.10.tgz#9d3c2f736c1d75dd3bd2be507dcc111f1e2ea9c1"
  integrity sha1-nTwvc2wddd070r5QfcwRHx4uqcE=
  dependencies:
    querystringify "^2.1.1"
    requires-port "^1.0.0"

url-to-options@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/url-to-options/download/url-to-options-1.0.1.tgz#1505a03a289a48cbd7a434efbaeec5055f5633a9"
  integrity sha1-FQWgOiiaSMvXpDTvuu7FBV9WM6k=

usb@^1.6.3:
  version "1.9.2"
  resolved "http://r.npm.sankuai.com/usb/download/usb-1.9.2.tgz#fb6b36f744ecc707a196c45a6ec72442cb6f2b73"
  integrity sha1-+2s290TsxwehlsRabsckQstvK3M=
  dependencies:
    node-addon-api "^4.2.0"
    node-gyp-build "^4.3.0"

use-latest-callback@^0.1.5:
  version "0.1.5"
  resolved "http://r.npm.sankuai.com/use-latest-callback/download/use-latest-callback-0.1.5.tgz#a4a836c08fa72f6608730b5b8f4bbd9c57c04f51"
  integrity sha1-pKg2wI+nL2YIcwtbj0u9nFfAT1E=

use-subscription@^1.0.0:
  version "1.8.0"
  resolved "http://r.npm.sankuai.com/use-subscription/download/use-subscription-1.8.0.tgz#f118938c29d263c2bce12fc5585d3fe694d4dbce"
  integrity sha1-8RiTjCnSY8K84S/FWF0/5pTU284=
  dependencies:
    use-sync-external-store "^1.2.0"

use-sync-external-store@1.2.0, use-sync-external-store@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/use-sync-external-store/download/use-sync-external-store-1.2.0.tgz#7dbefd6ef3fe4e767a0cf5d7287aacfb5846928a"
  integrity sha1-fb79bvP+TnZ6DPXXKHqs+1hGkoo=

use-sync-external-store@^1.4.0:
  version "1.5.0"
  resolved "http://r.npm.sankuai.com/use-sync-external-store/download/use-sync-external-store-1.5.0.tgz#55122e2a3edd2a6c106174c27485e0fd59bcfca0"
  integrity sha1-VRIuKj7dKmwQYXTCdIXg/Vm8/KA=

use@^3.1.0:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/use/download/use-3.1.1.tgz#d50c8cac79a19fbc20f2911f56eb973f4e10070f"
  integrity sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8=

util-deprecate@^1.0.1, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/util-deprecate/download/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

utils-merge@1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/utils-merge/download/utils-merge-1.0.1.tgz#9f95710f50a267947b2ccc124741c1028427e713"
  integrity sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=

uuid@^3.3.2:
  version "3.4.0"
  resolved "http://r.npm.sankuai.com/uuid/download/uuid-3.4.0.tgz#b23e4358afa8a202fe7a100af1f5f883f02007ee"
  integrity sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4=

uuid@^8.3.0:
  version "8.3.2"
  resolved "http://r.npm.sankuai.com/uuid/download/uuid-8.3.2.tgz#80d5b5ced271bb9af6c445f21a1a04c606cefbe2"
  integrity sha1-gNW1ztJxu5r2xEXyGhoExgbO++I=

v8-compile-cache@^2.0.3:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/v8-compile-cache/download/v8-compile-cache-2.3.0.tgz#2de19618c66dc247dcfb6f99338035d8245a2cee"
  integrity sha1-LeGWGMZtwkfc+2+ZM4A12CRaLO4=

v8-to-istanbul@^7.0.0:
  version "7.1.2"
  resolved "http://r.npm.sankuai.com/v8-to-istanbul/download/v8-to-istanbul-7.1.2.tgz#30898d1a7fa0c84d225a2c1434fb958f290883c1"
  integrity sha1-MImNGn+gyE0iWiwUNPuVjykIg8E=
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.1"
    convert-source-map "^1.6.0"
    source-map "^0.7.3"

validate-npm-package-license@^3.0.1:
  version "3.0.4"
  resolved "http://r.npm.sankuai.com/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz#fc91f6b9c7ba15c857f4cb2c5defeec39d4f410a"
  integrity sha1-/JH2uce6FchX9MssXe/uw51PQQo=
  dependencies:
    spdx-correct "^3.0.0"
    spdx-expression-parse "^3.0.0"

vary@~1.1.2:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/vary/download/vary-1.1.2.tgz#2299f02c6ded30d4a5961b0b9f74524a18f634fc"
  integrity sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=

vlq@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/vlq/download/vlq-1.0.1.tgz#c003f6e7c0b4c1edd623fd6ee50bbc0d6a1de468"
  integrity sha1-wAP258C0we3WI/1u5Qu8DWod5Gg=

w3c-hr-time@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/w3c-hr-time/download/w3c-hr-time-1.0.2.tgz#0a89cdf5cc15822df9c360543676963e0cc308cd"
  integrity sha1-ConN9cwVgi35w2BUNnaWPgzDCM0=
  dependencies:
    browser-process-hrtime "^1.0.0"

w3c-xmlserializer@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/w3c-xmlserializer/download/w3c-xmlserializer-2.0.0.tgz#3e7104a05b75146cc60f564380b7f683acf1020a"
  integrity sha1-PnEEoFt1FGzGD1ZDgLf2g6zxAgo=
  dependencies:
    xml-name-validator "^3.0.0"

walker@^1.0.7, walker@~1.0.5:
  version "1.0.8"
  resolved "http://r.npm.sankuai.com/walker/download/walker-1.0.8.tgz#bd498db477afe573dc04185f011d3ab8a8d7653f"
  integrity sha1-vUmNtHev5XPcBBhfAR06uKjXZT8=
  dependencies:
    makeerror "1.0.12"

warn-once@^0.1.0:
  version "0.1.1"
  resolved "http://r.npm.sankuai.com/warn-once/download/warn-once-0.1.1.tgz#952088f4fb56896e73fd4e6a3767272a3fccce43"
  integrity sha1-lSCI9PtWiW5z/U5qN2cnKj/MzkM=

wcwidth@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/wcwidth/download/wcwidth-1.0.1.tgz#f0b0dcf915bc5ff1528afadb2c0e17b532da2fe8"
  integrity sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g=
  dependencies:
    defaults "^1.0.3"

webidl-conversions@^3.0.0:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/webidl-conversions/download/webidl-conversions-3.0.1.tgz#24534275e2a7bc6be7bc86611cc16ae0a5654871"
  integrity sha1-JFNCdeKnvGvnvIZhHMFq4KVlSHE=

webidl-conversions@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/webidl-conversions/download/webidl-conversions-5.0.0.tgz#ae59c8a00b121543a2acc65c0434f57b0fc11aff"
  integrity sha1-rlnIoAsSFUOirMZcBDT1ew/BGv8=

webidl-conversions@^6.1.0:
  version "6.1.0"
  resolved "http://r.npm.sankuai.com/webidl-conversions/download/webidl-conversions-6.1.0.tgz#9111b4d7ea80acd40f5270d666621afa78b69514"
  integrity sha1-kRG01+qArNQPUnDWZmIa+ni2lRQ=

whatwg-encoding@^1.0.5:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/whatwg-encoding/download/whatwg-encoding-1.0.5.tgz#5abacf777c32166a51d085d6b4f3e7d27113ddb0"
  integrity sha1-WrrPd3wyFmpR0IXWtPPn0nET3bA=
  dependencies:
    iconv-lite "0.4.24"

whatwg-fetch@>=0.10.0, whatwg-fetch@^3.0.0:
  version "3.6.2"
  resolved "http://r.npm.sankuai.com/whatwg-fetch/download/whatwg-fetch-3.6.2.tgz#dced24f37f2624ed0281725d51d0e2e3fe677f8c"
  integrity sha1-3O0k838mJO0CgXJdUdDi4/5nf4w=

whatwg-mimetype@^2.3.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/whatwg-mimetype/download/whatwg-mimetype-2.3.0.tgz#3d4b1e0312d2079879f826aff18dbeeca5960fbf"
  integrity sha1-PUseAxLSB5h5+Cav8Y2+7KWWD78=

whatwg-url@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/whatwg-url/download/whatwg-url-5.0.0.tgz#966454e8765462e37644d3626f6742ce8b70965d"
  integrity sha1-lmRU6HZUYuN2RNNib2dCzotwll0=
  dependencies:
    tr46 "~0.0.3"
    webidl-conversions "^3.0.0"

whatwg-url@^8.0.0, whatwg-url@^8.5.0:
  version "8.7.0"
  resolved "http://r.npm.sankuai.com/whatwg-url/download/whatwg-url-8.7.0.tgz#656a78e510ff8f3937bc0bcbe9f5c0ac35941b77"
  integrity sha1-ZWp45RD/jzk3vAvL6fXArDWUG3c=
  dependencies:
    lodash "^4.7.0"
    tr46 "^2.1.0"
    webidl-conversions "^6.1.0"

which-boxed-primitive@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/which-boxed-primitive/download/which-boxed-primitive-1.0.2.tgz#13757bc89b209b049fe5d86430e21cf40a89a8e6"
  integrity sha1-E3V7yJsgmwSf5dhkMOIc9AqJqOY=
  dependencies:
    is-bigint "^1.0.1"
    is-boolean-object "^1.1.0"
    is-number-object "^1.0.4"
    is-string "^1.0.5"
    is-symbol "^1.0.3"

which-module@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/which-module/download/which-module-2.0.0.tgz#d9ef07dce77b9902b8a3a8fa4b31c3e3f7e6e87a"
  integrity sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho=

which-pm-runs@^1.0.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/which-pm-runs/download/which-pm-runs-1.1.0.tgz#35ccf7b1a0fce87bd8b92a478c9d045785d3bf35"
  integrity sha1-Ncz3saD86HvYuSpHjJ0EV4XTvzU=

which-typed-array@^1.1.14, which-typed-array@^1.1.15:
  version "1.1.15"
  resolved "http://r.npm.sankuai.com/which-typed-array/download/which-typed-array-1.1.15.tgz#264859e9b11a649b388bfaaf4f767df1f779b38d"
  integrity sha1-JkhZ6bEaZJs4i/qvT3Z98fd5s40=
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.7"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-tostringtag "^1.0.2"

which-typed-array@^1.1.9:
  version "1.1.9"
  resolved "http://r.npm.sankuai.com/which-typed-array/download/which-typed-array-1.1.9.tgz#307cf898025848cf995e795e8423c7f337efbde6"
  integrity sha1-MHz4mAJYSM+ZXnlehCPH8zfvveY=
  dependencies:
    available-typed-arrays "^1.0.5"
    call-bind "^1.0.2"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-tostringtag "^1.0.0"
    is-typed-array "^1.1.10"

which@^1.2.9:
  version "1.3.1"
  resolved "http://r.npm.sankuai.com/which/download/which-1.3.1.tgz#a45043d54f5805316da8d62f9f50918d3da70b0a"
  integrity sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=
  dependencies:
    isexe "^2.0.0"

which@^2.0.1, which@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/which/download/which-2.0.2.tgz#7c6a8dd0a636a0327e10b59c9286eee93f3f51b1"
  integrity sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=
  dependencies:
    isexe "^2.0.0"

window-size@^0.1.4:
  version "0.1.4"
  resolved "http://r.npm.sankuai.com/window-size/download/window-size-0.1.4.tgz#f8e1aa1ee5a53ec5bf151ffa09742a6ad7697876"
  integrity sha1-+OGqHuWlPsW/FR/6CXQqatdpeHY=

word-wrap@^1.2.3, word-wrap@~1.2.3:
  version "1.2.3"
  resolved "http://r.npm.sankuai.com/word-wrap/download/word-wrap-1.2.3.tgz#610636f6b1f703891bd34771ccb17fb93b47079c"
  integrity sha1-YQY29rH3A4kb00dxzLF/uTtHB5w=

wordwrap@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/wordwrap/download/wordwrap-1.0.0.tgz#27584810891456a4171c8d0226441ade90cbcaeb"
  integrity sha1-J1hIEIkUVqQXHI0CJkQa3pDLyus=

wrap-ansi@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-2.1.0.tgz#d8fc3d284dd05794fe84973caecdd1cf824fdd85"
  integrity sha1-2Pw9KE3QV5T+hJc8rs3Rz4JP3YU=
  dependencies:
    string-width "^1.0.1"
    strip-ansi "^3.0.1"

wrap-ansi@^5.1.0:
  version "5.1.0"
  resolved "http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-5.1.0.tgz#1fd1f67235d5b6d0fee781056001bfb694c03b09"
  integrity sha1-H9H2cjXVttD+54EFYAG/tpTAOwk=
  dependencies:
    ansi-styles "^3.2.0"
    string-width "^3.0.0"
    strip-ansi "^5.0.0"

wrap-ansi@^6.0.1, wrap-ansi@^6.2.0:
  version "6.2.0"
  resolved "http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-6.2.0.tgz#e9393ba07102e6c91a3b221478f0257cd2856e53"
  integrity sha1-6Tk7oHEC5skaOyIUePAlfNKFblM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^7.0.0:
  version "7.0.0"
  resolved "http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-7.0.0.tgz#67e145cff510a6a6984bdf1152911d69d2eb9e43"
  integrity sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrappy@1:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/wrappy/download/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

write-file-atomic@^3.0.0:
  version "3.0.3"
  resolved "http://r.npm.sankuai.com/write-file-atomic/download/write-file-atomic-3.0.3.tgz#56bd5c5a5c70481cd19c571bd39ab965a5de56e8"
  integrity sha1-Vr1cWlxwSBzRnFcb05q5ZaXeVug=
  dependencies:
    imurmurhash "^0.1.4"
    is-typedarray "^1.0.0"
    signal-exit "^3.0.2"
    typedarray-to-buffer "^3.1.5"

ws@^1.1.0, ws@^1.1.5:
  version "1.1.5"
  resolved "http://r.npm.sankuai.com/ws/download/ws-1.1.5.tgz#cbd9e6e75e09fc5d2c90015f21f0c40875e0dd51"
  integrity sha1-y9nm514J/F0skAFfIfDECHXg3VE=
  dependencies:
    options ">=0.0.5"
    ultron "1.0.x"

ws@^7, ws@^7.4.6:
  version "7.5.9"
  resolved "http://r.npm.sankuai.com/ws/download/ws-7.5.9.tgz#54fa7db29f4c7cec68b1ddd3a89de099942bb591"
  integrity sha1-VPp9sp9MfOxosd3TqJ3gmZQrtZE=

xcode@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/xcode/download/xcode-2.1.0.tgz#bab64a7e954bb50ca8d19da7e09531c65a43ecfe"
  integrity sha1-urZKfpVLtQyo0Z2n4JUxxlpD7P4=
  dependencies:
    simple-plist "^1.0.0"
    uuid "^3.3.2"

xml-name-validator@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/xml-name-validator/download/xml-name-validator-3.0.0.tgz#6ae73e06de4d8c6e47f9fb181f78d648ad457c6a"
  integrity sha1-auc+Bt5NjG5H+fsYH3jWSK1FfGo=

xmlbuilder@^15.1.1:
  version "15.1.1"
  resolved "http://r.npm.sankuai.com/xmlbuilder/download/xmlbuilder-15.1.1.tgz#9dcdce49eea66d8d10b42cae94a79c3c8d0c2ec5"
  integrity sha1-nc3OSe6mbY0QtCyulKecPI0MLsU=

xmlchars@^2.2.0:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/xmlchars/download/xmlchars-2.2.0.tgz#060fe1bcb7f9c76fe2a17db86a9bc3ab894210cb"
  integrity sha1-Bg/hvLf5x2/ioX24apvDq4lCEMs=

xmldoc@^1.1.2:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/xmldoc/download/xmldoc-1.2.0.tgz#7554371bfd8c138287cff01841ae4566d26e5541"
  integrity sha1-dVQ3G/2ME4KHz/AYQa5FZtJuVUE=
  dependencies:
    sax "^1.2.4"

xpipe@^1.0.5:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/xpipe/download/xpipe-1.0.5.tgz#8dd8bf45fc3f7f55f0e054b878f43a62614dafdf"
  integrity sha1-jdi/Rfw/f1Xw4FS4ePQ6YmFNr98=

xtend@^4.0.0, xtend@~4.0.1:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/xtend/download/xtend-4.0.2.tgz#bb72779f5fa465186b1f438f674fa347fdb5db54"
  integrity sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q=

y18n@^3.2.0:
  version "3.2.2"
  resolved "http://r.npm.sankuai.com/y18n/download/y18n-3.2.2.tgz#85c901bd6470ce71fc4bb723ad209b70f7f28696"
  integrity sha1-hckBvWRwznH8S7cjrSCbcPfyhpY=

y18n@^4.0.0:
  version "4.0.3"
  resolved "http://r.npm.sankuai.com/y18n/download/y18n-4.0.3.tgz#b5f259c82cd6e336921efd7bfd8bf560de9eeedf"
  integrity sha1-tfJZyCzW4zaSHv17/Yv1YN6e7t8=

yallist@^2.1.2:
  version "2.1.2"
  resolved "http://r.npm.sankuai.com/yallist/download/yallist-2.1.2.tgz#1c11f9218f076089a47dd512f93c6699a6a81d52"
  integrity sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI=

yallist@^3.0.2:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/yallist/download/yallist-3.1.1.tgz#dbb7daf9bfd8bac9ab45ebf602b8cbad0d5d08fd"
  integrity sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=

yallist@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/yallist/download/yallist-4.0.0.tgz#9bb92790d9c0effec63be73519e11a35019a3a72"
  integrity sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI=

yaml@^1.10.0:
  version "1.10.2"
  resolved "http://r.npm.sankuai.com/yaml/download/yaml-1.10.2.tgz#2301c5ffbf12b467de8da2333a459e29e7920e4b"
  integrity sha1-IwHF/78StGfejaIzOkWeKeeSDks=

yargs-parser@20.x:
  version "20.2.9"
  resolved "http://r.npm.sankuai.com/yargs-parser/download/yargs-parser-20.2.9.tgz#2eb7dc3b0289718fc295f362753845c41a0c94ee"
  integrity sha1-LrfcOwKJcY/ClfNidThFxBoMlO4=

yargs-parser@^15.0.1:
  version "15.0.3"
  resolved "http://r.npm.sankuai.com/yargs-parser/download/yargs-parser-15.0.3.tgz#316e263d5febe8b38eef61ac092b33dfcc9b1115"
  integrity sha1-MW4mPV/r6LOO72GsCSsz38ybERU=
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs-parser@^18.1.2:
  version "18.1.3"
  resolved "http://r.npm.sankuai.com/yargs-parser/download/yargs-parser-18.1.3.tgz#be68c4975c6b2abf469236b0c870362fab09a7b0"
  integrity sha1-vmjEl1xrKr9GkjawyHA2L6sJp7A=
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs@^14.2.0:
  version "14.2.3"
  resolved "http://r.npm.sankuai.com/yargs/download/yargs-14.2.3.tgz#1a1c3edced1afb2a2fea33604bc6d1d8d688a414"
  integrity sha1-Ghw+3O0a+yov6jNgS8bR2NaIpBQ=
  dependencies:
    cliui "^5.0.0"
    decamelize "^1.2.0"
    find-up "^3.0.0"
    get-caller-file "^2.0.1"
    require-directory "^2.1.1"
    require-main-filename "^2.0.0"
    set-blocking "^2.0.0"
    string-width "^3.0.0"
    which-module "^2.0.0"
    y18n "^4.0.0"
    yargs-parser "^15.0.1"

yargs@^15.1.0, yargs@^15.4.1:
  version "15.4.1"
  resolved "http://r.npm.sankuai.com/yargs/download/yargs-15.4.1.tgz#0d87a16de01aee9d8bec2bfbf74f67851730f4f8"
  integrity sha1-DYehbeAa7p2L7Cv7909nhRcw9Pg=
  dependencies:
    cliui "^6.0.0"
    decamelize "^1.2.0"
    find-up "^4.1.0"
    get-caller-file "^2.0.1"
    require-directory "^2.1.1"
    require-main-filename "^2.0.0"
    set-blocking "^2.0.0"
    string-width "^4.2.0"
    which-module "^2.0.0"
    y18n "^4.0.0"
    yargs-parser "^18.1.2"

yargs@^3.31.0:
  version "3.32.0"
  resolved "http://r.npm.sankuai.com/yargs/download/yargs-3.32.0.tgz#03088e9ebf9e756b69751611d2a5ef591482c995"
  integrity sha1-AwiOnr+edWtpdRYR0qXvWRSCyZU=
  dependencies:
    camelcase "^2.0.1"
    cliui "^3.0.3"
    decamelize "^1.1.1"
    os-locale "^1.4.0"
    string-width "^1.0.1"
    window-size "^0.1.4"
    y18n "^3.2.0"

yauzl@^2.4.2:
  version "2.10.0"
  resolved "http://r.npm.sankuai.com/yauzl/download/yauzl-2.10.0.tgz#c7eb17c93e112cb1086fa6d8e51fb0667b79a5f9"
  integrity sha1-x+sXyT4RLLEIb6bY5R+wZnt5pfk=
  dependencies:
    buffer-crc32 "~0.2.3"
    fd-slicer "~1.1.0"

yocto-queue@^0.1.0:
  version "0.1.0"
  resolved "http://r.npm.sankuai.com/yocto-queue/download/yocto-queue-0.1.0.tgz#0294eb3dee05028d31ee1a5fa2c556a6aaf10a1b"
  integrity sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=

zip-stream@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/zip-stream/download/zip-stream-4.1.0.tgz#51dd326571544e36aa3f756430b313576dc8fc79"
  integrity sha1-Ud0yZXFUTjaqP3VkMLMTV23I/Hk=
  dependencies:
    archiver-utils "^2.1.0"
    compress-commons "^4.1.0"
    readable-stream "^3.6.0"

zustand@^4.4.6:
  version "4.4.6"
  resolved "http://r.npm.sankuai.com/zustand/download/zustand-4.4.6.tgz#03c78e3e2686c47095c93714c0c600b72a6512bd"
  integrity sha1-A8eOPiaGxHCVyTcUwMYAtyplEr0=
  dependencies:
    use-sync-external-store "1.2.0"
