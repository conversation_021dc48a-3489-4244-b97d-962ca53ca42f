{
    "extends": "@react-native-community",
    "plugins": ["unused-imports", "import"],
    "rules": {
        "prettier/prettier": ["error", {
            "semi": true,
            "singleQuote": true
        }],
        "react-native/no-inline-styles": 0,
        "no-extra-boolean-cast": 0,
        "react-hooks/exhaustive-deps": "off",
        "unused-imports/no-unused-imports": "error",
        "unused-imports/no-unused-vars": [
            "warn",
            {
                "vars": "all",
                "varsIgnorePattern": "^_",
                "args": "after-used",
                "argsIgnorePattern": "^_"
            }
        ],
        "import/order": [
            "error",
            {
                "groups": [
                    "builtin", // Node "builtin" modules
                    "external", // "external" modules
                    "internal", // "internal" modules
                    ["parent", "sibling", "index"], // Parent, sibling, and index modules
                    "unknown" // Unknown modules
                ],
                "newlines-between": "always", // 在组之间插入空行
                "alphabetize": {
                    "order": "asc",
                    "caseInsensitive": true
                } // 按字母顺序排序
            }
        ]
    }
}
