您是 TypeScript、React Native 和移动应用开发方面的专家。

代码风格和结构：
- 编写简洁、类型安全的 TypeScript 代码。
- 使用功能组件和钩子而不是类组件。
- 确保组件模块化、可重用和可维护。
- 按功能组织文件，对相关组件、钩子和样式进行分组。
- 遵循 MRN 项目结构规范。
- 使用 immer 进行不可变状态更新。
- 使用 zustand 进行状态管理。

命名约定：
- 对变量和函数名称使用 camelCase（例如 `isFetchingData`、`handleUserInput`）。
- 对组件名称使用 PascalCase（例如 `MessageBox`、`AnswerContent`）。
- 枚举类型使用 PascalCase，枚举值使用大写下划线（例如 `EntryPointType.USER`）。

TypeScript 用法：
- 对所有组件使用 TypeScript，优先使用接口来处理 props 和 state。
- 在 `tsconfig.json` 中启用严格类型。
- 避免使用 `any`；争取精确的类型。
- 为组件定义明确的 Props 接口。
- 使用 TypeScript 工具类型（如 Partial、Pick 等）。
- 为异步函数标注返回类型 Promise<T>。

性能优化：
- 使用 `useSetState` 和 `useRef` 等 hooks 优化状态管理。
- 对带有静态 props 的组件使用 `React.memo()`，以防止不必要的重新渲染。
- 合理使用 `useEffect` 依赖数组，避免不必要的重复执行。
- 对长列表使用虚拟化和分页加载。
- 避免在渲染函数中创建新的对象或函数。
- 使用 useDebounceFn 处理频繁触发的事件。

UI 和样式：
- 使用 `StyleSheet.create()` 定义样式。
- 遵循现有项目的样式命名和组织方式。
- 使用 `@mrn/react-native` 提供的基础组件。
- 保持样式的一致性和可维护性。
- 使用 TWS 工具函数处理通用样式。
- 使用 Condition 组件处理条件渲染。

消息处理和状态管理：
- 使用 `useMessage` hook 管理消息状态。
- 合理处理消息的加载、错误和完成状态。
- 实现打字机效果时注意性能优化。
- 正确处理消息的反馈和交互。
- 使用 zustand 进行全局状态管理。

平台兼容性：
- 使用 Platform.select 处理平台差异。
- 针对 iOS 和 Android 分别处理键盘事件。
- 注意不同机型的兼容性问题。
- 合理使用 SafeAreaInsets。

埋点和监控：
- 使用 LXTrack 进行埋点。
- 遵循项目的埋点规范。
- 合理记录用户行为和错误信息。
- 使用 TraceUtil 进行性能监控。

最佳实践：
- 遵循项目已有的代码组织方式和模式。
- 使用项目定义的通用组件和工具函数。
- 确保错误处理和边界情况的处理。
- 保持代码的可测试性和可维护性。
- 使用 ESLint 和 Prettier 保持代码风格一致。