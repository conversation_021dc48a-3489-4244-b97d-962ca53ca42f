{"compilerOptions": {"allowJs": true, "checkJs": false, "declaration": true, "target": "esnext", "module": "esnext", "sourceMap": true, "experimentalDecorators": true, "jsx": "react-native", "allowSyntheticDefaultImports": true, "moduleResolution": "node", "strict": false, "skipLibCheck": true, "noEmit": true, "resolveJsonModule": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "react-native": ["node_modules/@mrn/react-native"]}}}