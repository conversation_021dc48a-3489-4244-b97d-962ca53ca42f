const { execSync } = require('child_process');
const { exec } = require('child_process');
const fs = require('fs');
const readline = require('readline');

const args = process.argv.slice(2);

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
});

const mrnLink = (packageName) => {
    const child = exec(`yarn mrn link ${packageName}`);

    child.stdout.on('data', (data) => {
        console.log(`${data}`);
    });

    child.stderr.on('data', (data) => {
        console.error(`error: ${data}`);
    });

    child.on('close', (code) => {
        console.log(`子进程退出，退出码: ${code}`);
    });
};

// 执行command命令获取mac电脑用户目录
const userDir = execSync('echo $HOME').toString().trim();
const configDir = `${userDir}/.mrnLinkConfig.json`;
fs.readFile(configDir, 'utf8', (err, data) => {
    if (err) {
        console.log('读取~/.mrnLinkConfig.json文件错误：', err);
        rl.close();
        return;
    }

    const config = JSON.parse(data);
    const keys = Object.keys(config.packages);
    if (!keys) {
        console.log('没有可以link的库');
    }

    if (args.length > 0) {
        const input = parseInt(args[0], 10) - 1;
        if (input >= 0 && input < keys.length) {
            const key = keys[input];
            console.log(`正在link ${key}`);
            mrnLink(key);
            return;
        }
    }

    keys.forEach((key, index) => {
        console.log(`${index + 1}. ${key}`);
    });

    rl.question('请选择要link的库，并输入编号：', (answer) => {
        const choiceIndex = parseInt(answer, 10) - 1;
        if (
            isNaN(choiceIndex) ||
            choiceIndex < 0 ||
            choiceIndex >= keys.length
        ) {
            console.log('选择的命令编号无效');
            rl.close();
            return;
        }

        const key = keys[choiceIndex];
        mrnLink(key);
        rl.close();
    });
});
