// mrn.config.js 配置文档
// http://mrn.sankuai.com/docs/guide/conf.html#mrn-config-js

module.exports = [
    {
        name: 'bee-assistant',
        biz: 'waimaicrm',
        bundleType: 0,
        bundleDependencies: [
            '@mrn/mrn-base',
            '@mfe/waimai-mfe-bee-common',
            '@mfe/bee-foundation-navigation',
            '@mfe/bee-foundation-utils',
        ],
        debugger: {
            moduleName: 'bee-assistant',
            initialProperties: {
                hideNavigationBar: true,
            },
        },
    },
    {
        name: 'bee-assistant-main',
        biz: 'waimaicrm',
        main: './bee-assistant-main.tsx',
        bundleType: 1,
        bundleDependencies: [
            '@mrn/mrn-base',
            '@mfe/waimai-mfe-bee-common',
            '@mfe/bee-foundation-navigation',
            '@mfe/bee-foundation-utils',
        ],
        debugger: {
            moduleName: 'bee-assistant',
            initialProperties: {
                hideNavigationBar: true,
            },
        },
    },
    {
        name: 'waimai-mfe-bee',
        biz: 'waimai',
        bundleType: 1,
        bundleDependencies: ['@mrn/mrn-base', '@mfe/waimai-mfe-bee-common'],
        main: './debug.tsx',
        debugger: {
            moduleName: 'bee',
            initialProperties: {
                hideNavigationBar: true,
            },
        },
    },
];
